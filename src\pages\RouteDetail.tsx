"use client";

import React from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { MadeWithDyad } from "@/components/made-with-dyad";

// Placeholder for mock data (will be created in the next step)
interface RouteDetailData {
  id: string;
  name: string;
  status: "operational" | "degraded" | "down";
  uptimePercentage: number;
  lastChecked: string;
  links: { id: string; name: string; distance: string; totalLoss: string; }[];
  assets: { id: string; type: string; count: number; location?: [number, number]; }[];
  averageSLAHours: number;
  slaLast: number;
  troubleTickets: {
    id: string;
    priority: "high" | "medium" | "low";
    status: "open" | "in-progress" | "closed";
    title: string;
    assignedTo: string;
    openedAt: string;
    rfo?: { rootCause: string; timeToRepair: string; notes: string; };
    history: { timestamp: string; event: string; }[];
  }[];
  patrolFindings: {
    id: string;
    date: string;
    inspector: string;
    findings: string;
    status: "resolved" | "pending";
  }[];
  scheduledActivities: {
    id: string;
    date: string;
    type: string;
    description: string;
    status: "scheduled" | "completed" | "cancelled";
  }[];
  portData: {
    id: string;
    portName: string;
    trafficName: string;
    totalLoss: string;
    otdrDistance: string;
    groundDistance: string;
  }[];
}

// Mock data function (will be moved to a separate file later)
const getRouteDetailData = (id: string): RouteDetailData | undefined => {
  // This is a placeholder. In a real app, you'd fetch this from an API.
  // For now, we'll return a dummy object or find it from a larger mock dataset.
  const sampleData: RouteDetailData[] = [
    {
      id: "JKT-001",
      name: "Route Jakarta Pusat - Sudirman",
      status: "operational",
      uptimePercentage: 99.9,
      lastChecked: "2024-08-16 10:00 AM",
      links: [
        { id: "link-1", name: "Link A1", distance: "5 km", totalLoss: "0.1 dB" },
        { id: "link-2", name: "Link A2", distance: "7 km", totalLoss: "0.1 dB" },
      ],
      assets: [
        { id: "asset-1", type: "Handhole", count: 5, location: [-6.2088, 106.8456] }, // Jakarta
        { id: "asset-2", type: "Pole", count: 10, location: [-6.2100, 106.8400] },
        { id: "asset-3", type: "JC", count: 3, location: [-6.2050, 106.8500] },
      ],
      averageSLAHours: 5.5,
      slaLast: 5.3,
      troubleTickets: [
        {
          id: "TT-001", priority: "high", status: "closed", title: "Fiber cut on Link A1", assignedTo: "John Doe", openedAt: "2024-08-01 09:00 AM",
          rfo: { rootCause: "Construction damage", timeToRepair: "4h", notes: "Repaired by splicing team." },
          history: [{ timestamp: "2024-08-01 09:00 AM", event: "Ticket opened" }, { timestamp: "2024-08-01 01:00 PM", event: "Repaired and closed" }]
        },
        {
          id: "TT-002", priority: "medium", status: "in-progress", title: "Intermittent signal on Link A2", assignedTo: "Jane Smith", openedAt: "2024-08-10 02:00 PM",
          history: [{ timestamp: "2024-08-10 02:00 PM", event: "Ticket opened" }, { timestamp: "2024-08-10 03:00 PM", event: "Assigned to Jane Smith" }]
        },
      ],
      patrolFindings: [
        { id: "PF-001", date: "2024-07-25", inspector: "Budi", findings: "Minor cable abrasion near pole #5", status: "resolved" },
        { id: "PF-002", date: "2024-08-05", inspector: "Ani", findings: "Vegetation encroachment on Link A2", status: "pending" },
      ],
      scheduledActivities: [
        { id: "SA-001", date: "2024-08-20", type: "Preventive Maintenance", description: "Routine inspection of all assets", status: "scheduled" },
        { id: "SA-002", date: "2024-08-22", type: "Upgrade", description: "Firmware upgrade on OLT-01", status: "scheduled" },
      ],
      portData: [
        { id: "P-001", portName: "Port 1/1/1", trafficName: "Residential A", totalLoss: "0.15 dB", otdrDistance: "4.8 km", groundDistance: "5.0 km" },
        { id: "P-002", portName: "Port 1/1/2", trafficName: "Business B", totalLoss: "0.18 dB", otdrDistance: "6.9 km", groundDistance: "7.1 km" },
      ],
    },
    {
      id: "TGR-002",
      name: "Route Tangerang - BSD City",
      status: "operational",
      uptimePercentage: 99.8,
      lastChecked: "2024-08-16 09:55 AM",
      links: [
        { id: "link-3", name: "Link B1", distance: "10 km", totalLoss: "0.2 dB" },
        { id: "link-4", name: "Link B2", distance: "8 km", totalLoss: "0.1 dB" },
      ],
      assets: [
        { id: "asset-4", type: "Handhole", count: 8, location: [-6.2297, 106.6479] }, // Tangerang
        { id: "asset-5", type: "Pole", count: 15, location: [-6.2300, 106.6500] },
      ],
      averageSLAHours: 6.2,
      slaLast: 6.0,
      troubleTickets: [],
      patrolFindings: [],
      scheduledActivities: [],
      portData: [],
    },
    // Add more dummy data for other routes as needed
  ];
  return sampleData.find(route => route.id === id);
};


const RouteDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const routeData = id ? getRouteDetailData(id) : undefined; // Fetch data based on ID

  if (!routeData) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background text-foreground p-4">
        <h1 className="text-3xl font-bold mb-4">Route Not Found</h1>
        <p className="text-lg text-muted-foreground mb-6">The route with ID "{id}" could not be found.</p>
        <Button onClick={() => navigate('/')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Dashboard
        </Button>
        <MadeWithDyad />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background text-foreground p-4 sm:p-6 lg:p-8">
      <header className="mb-8 flex items-center justify-between">
        <Button onClick={() => navigate('/')} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Dashboard
        </Button>
        <h1 className="text-3xl sm:text-4xl font-bold text-primary text-center flex-grow">
          Route Detail: {routeData.name} ({routeData.id})
        </h1>
        <div className="w-24"></div> {/* Spacer to balance the back button */}
      </header>

      <main className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Overview Card */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl">Overview</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <p className="text-muted-foreground">Status:</p>
              <p className="font-semibold text-lg">{routeData.status.charAt(0).toUpperCase() + routeData.status.slice(1)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Uptime:</p>
              <p className="font-semibold text-lg">{routeData.uptimePercentage}%</p>
            </div>
            <div>
              <p className="text-muted-foreground">Last Checked:</p>
              <p className="font-semibold text-lg">{routeData.lastChecked}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Average SLA:</p>
              <p className="font-semibold text-lg">{routeData.averageSLAHours.toFixed(1)} hrs</p>
            </div>
            <div>
              <p className="text-muted-foreground">SLA Last:</p>
              <p className="font-semibold text-lg">{routeData.slaLast.toFixed(1)} hrs</p>
            </div>
          </CardContent>
        </Card>

        {/* Placeholder for Trouble Ticket History */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl">Trouble Ticket History</CardTitle>
          </CardHeader>
          <CardContent>
            {routeData.troubleTickets.length > 0 ? (
              <div className="space-y-4">
                {routeData.troubleTickets.map(ticket => (
                  <div key={ticket.id} className="border p-3 rounded-md">
                    <h3 className="font-semibold">{ticket.title} ({ticket.id})</h3>
                    <p className="text-sm text-muted-foreground">Status: {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)} | Priority: {ticket.priority.charAt(0).toUpperCase() + ticket.priority.slice(1)}</p>
                    {ticket.rfo && (
                      <div className="mt-2 text-sm">
                        <p><span className="font-medium">RFO Root Cause:</span> {ticket.rfo.rootCause}</p>
                        <p><span className="font-medium">Time to Repair:</span> {ticket.rfo.timeToRepair}</p>
                        <p><span className="font-medium">Notes:</span> {ticket.rfo.notes}</p>
                      </div>
                    )}
                    <div className="mt-2 text-xs text-muted-foreground">
                      <p className="font-medium">History:</p>
                      <ul className="list-disc list-inside ml-2">
                        {ticket.history.map((hist, idx) => (
                          <li key={idx}>{hist.timestamp}: {hist.event}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No trouble tickets for this route.</p>
            )}
          </CardContent>
        </Card>

        {/* Placeholder for Asset Details (including map) */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl">Asset Details</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Map will go here */}
            <div className="h-64 w-full bg-gray-200 flex items-center justify-center rounded-md text-muted-foreground mb-4">
              Map Placeholder (Leaflet will be integrated here)
            </div>
            {routeData.assets.length > 0 ? (
              <div className="space-y-2">
                {routeData.assets.map(asset => (
                  <p key={asset.id} className="text-sm">
                    <span className="font-medium">{asset.type}:</span> {asset.count} units
                    {asset.location && ` (Lat: ${asset.location[0]}, Lon: ${asset.location[1]})`}
                  </p>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No asset details available.</p>
            )}
          </CardContent>
        </Card>

        {/* Placeholder for Patrol Findings */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl">Patrol Findings</CardTitle>
          </CardHeader>
          <CardContent>
            {routeData.patrolFindings.length > 0 ? (
              <div className="space-y-4">
                {routeData.patrolFindings.map(finding => (
                  <div key={finding.id} className="border p-3 rounded-md">
                    <p className="font-semibold">Date: {finding.date} | Inspector: {finding.inspector}</p>
                    <p className="text-sm text-muted-foreground">Findings: {finding.findings}</p>
                    <p className="text-sm text-muted-foreground">Status: {finding.status.charAt(0).toUpperCase() + finding.status.slice(1)}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No patrol findings for this route.</p>
            )}
          </CardContent>
        </Card>

        {/* Placeholder for Scheduled Activities */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl">Scheduled Activities</CardTitle>
          </CardHeader>
          <CardContent>
            {routeData.scheduledActivities.length > 0 ? (
              <div className="space-y-4">
                {routeData.scheduledActivities.map(activity => (
                  <div key={activity.id} className="border p-3 rounded-md">
                    <p className="font-semibold">Date: {activity.date} | Type: {activity.type}</p>
                    <p className="text-sm text-muted-foreground">Description: {activity.description}</p>
                    <p className="text-sm text-muted-foreground">Status: {activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No scheduled activities for this route.</p>
            )}
          </CardContent>
        </Card>

        {/* Placeholder for Port Data */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl">Port Data</CardTitle>
          </CardHeader>
          <CardContent>
            {routeData.portData.length > 0 ? (
              <div className="space-y-4">
                {routeData.portData.map(port => (
                  <div key={port.id} className="border p-3 rounded-md">
                    <p className="font-semibold">Port: {port.portName} | Traffic: {port.trafficName}</p>
                    <p className="text-sm text-muted-foreground">Total Loss: {port.totalLoss}</p>
                    <p className="text-sm text-muted-foreground">OTDR Distance: {port.otdrDistance} | Ground Distance: {port.groundDistance}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No port data available for this route.</p>
            )}
          </CardContent>
        </Card>
      </main>

      <MadeWithDyad />
    </div>
  );
};

export default RouteDetail;