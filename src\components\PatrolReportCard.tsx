import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertTriangle, CheckCircle, Clock, Wrench, Eye, Users, X } from 'lucide-react';

interface PatrolFinding {
  id: string;
  type: 'cable_exposure' | 'hdpe_exposure' | 'third_party_work' | 'asset_condition' | 'other';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed';
  location: string;
  reportedDate: Date;
  repairedDate?: Date;
  route?: string;
  affectedLength?: number; // in meters
}

interface RepairSummary {
  totalFindings: number;
  completedRepairs: number;
  pendingRepairs: number;
  inProgressRepairs: number;
  averageRepairTime: number; // in hours
}

interface LengthSummary {
  totalCableExposureLength: number;
  completedCableExposureLength: number;
  totalThirdPartyLength: number;
  completedThirdPartyLength: number;
}

const PatrolReportCard: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<'cable_exposure' | 'third_party_work' | null>(null);
  
  // Mock data for demonstration
  const patrolFindings: PatrolFinding[] = [
    {
      id: '1',
      type: 'cable_exposure',
      description: 'Fiber optic cable exposed due to soil erosion',
      severity: 'high',
      status: 'completed',
      location: 'Route A - KM 15.2',
      reportedDate: new Date('2024-01-15'),
      repairedDate: new Date('2024-01-16'),
      route: 'Route-A',
      affectedLength: 2.5
    },
    {
      id: '2',
      type: 'hdpe_exposure',
      description: 'HDPE conduit partially exposed near road crossing',
      severity: 'medium',
      status: 'in_progress',
      location: 'Route B - KM 8.7',
      reportedDate: new Date('2024-01-18'),
      route: 'Route-B',
      affectedLength: 1.8
    },
    {
      id: '3',
      type: 'third_party_work',
      description: 'Construction work detected near fiber route',
      severity: 'critical',
      status: 'pending',
      location: 'Route C - KM 22.1',
      reportedDate: new Date('2024-01-20'),
      route: 'Route-C',
      affectedLength: 5.2
    },
    {
      id: '4',
      type: 'asset_condition',
      description: 'Splice closure showing signs of water ingress',
      severity: 'medium',
      status: 'completed',
      location: 'Route A - KM 18.5',
      reportedDate: new Date('2024-01-12'),
      repairedDate: new Date('2024-01-14'),
      route: 'Route-A',
      affectedLength: 0.5
    },
    {
      id: '5',
      type: 'cable_exposure',
      description: 'Cable exposed due to road excavation',
      severity: 'high',
      status: 'in_progress',
      location: 'Route D - KM 7.3',
      reportedDate: new Date('2024-01-22'),
      route: 'Route-D',
      affectedLength: 3.1
    },
    {
      id: '6',
      type: 'third_party_work',
      description: 'PLN excavation work near cable route',
      severity: 'medium',
      status: 'completed',
      location: 'Route B - KM 12.4',
      reportedDate: new Date('2024-01-10'),
      repairedDate: new Date('2024-01-12'),
      route: 'Route-B',
      affectedLength: 1.0
    }
  ];

  const repairSummary: RepairSummary = {
    totalFindings: patrolFindings.length,
    completedRepairs: patrolFindings.filter(f => f.status === 'completed').length,
    pendingRepairs: patrolFindings.filter(f => f.status === 'pending').length,
    inProgressRepairs: patrolFindings.filter(f => f.status === 'in_progress').length,
    averageRepairTime: 28 // hours
  };

  // Calculate length summary for cable exposure and third party work
  const lengthSummary: LengthSummary = {
    totalCableExposureLength: patrolFindings
      .filter(f => f.type === 'cable_exposure')
      .reduce((sum, f) => sum + (f.affectedLength || 0), 0),
    completedCableExposureLength: patrolFindings
      .filter(f => f.type === 'cable_exposure' && f.status === 'completed')
      .reduce((sum, f) => sum + (f.affectedLength || 0), 0),
    totalThirdPartyLength: patrolFindings
      .filter(f => f.type === 'third_party_work')
      .reduce((sum, f) => sum + (f.affectedLength || 0), 0),
    completedThirdPartyLength: patrolFindings
      .filter(f => f.type === 'third_party_work' && f.status === 'completed')
      .reduce((sum, f) => sum + (f.affectedLength || 0), 0)
  };

  const cableExposureCompletionRate = lengthSummary.totalCableExposureLength > 0 
    ? (lengthSummary.completedCableExposureLength / lengthSummary.totalCableExposureLength) * 100 
    : 0;
  
  const thirdPartyCompletionRate = lengthSummary.totalThirdPartyLength > 0 
    ? (lengthSummary.completedThirdPartyLength / lengthSummary.totalThirdPartyLength) * 100 
    : 0;

  const handleCardClick = (type: 'cable_exposure' | 'third_party_work') => {
    setSelectedType(type);
    setIsModalOpen(true);
  };

  const getFilteredFindings = (type: 'cable_exposure' | 'third_party_work') => {
    return patrolFindings.filter(f => f.type === type);
  };

  const getRouteDetails = (type: 'cable_exposure' | 'third_party_work') => {
    const findings = getFilteredFindings(type);
    const routeMap = new Map();
    
    findings.forEach(finding => {
      if (finding.route) {
        if (!routeMap.has(finding.route)) {
          routeMap.set(finding.route, {
            route: finding.route,
            totalLength: 0,
            completedLength: 0,
            findings: []
          });
        }
        const routeData = routeMap.get(finding.route);
        routeData.totalLength += finding.affectedLength || 0;
        if (finding.status === 'completed') {
          routeData.completedLength += finding.affectedLength || 0;
        }
        routeData.findings.push(finding);
      }
    });
    
    return Array.from(routeMap.values());
  };

  const getTypeIcon = (type: PatrolFinding['type']) => {
    switch (type) {
      case 'cable_exposure':
      case 'hdpe_exposure':
        return <AlertTriangle className="h-4 w-4" />;
      case 'third_party_work':
        return <Users className="h-4 w-4" />;
      case 'asset_condition':
        return <Eye className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: PatrolFinding['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'pending':
        return <Wrench className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: PatrolFinding['severity']) => {
    switch (severity) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeLabel = (type: PatrolFinding['type']) => {
    switch (type) {
      case 'cable_exposure':
        return 'Cable Exposure';
      case 'hdpe_exposure':
        return 'HDPE Exposure';
      case 'third_party_work':
        return 'Third Party Work';
      case 'asset_condition':
        return 'Asset Condition';
      default:
        return 'Other';
    }
  };

  const completionRate = (repairSummary.completedRepairs / repairSummary.totalFindings) * 100;

  return (
    <div>
      <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Patrol Report Summary
        </CardTitle>
        <CardDescription>
          Overview of patrol findings and repair status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{repairSummary.totalFindings}</div>
            <div className="text-sm text-blue-600">Total Findings</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{repairSummary.completedRepairs}</div>
            <div className="text-sm text-green-600">Completed</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{repairSummary.inProgressRepairs}</div>
            <div className="text-sm text-yellow-600">In Progress</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{repairSummary.pendingRepairs}</div>
            <div className="text-sm text-red-600">Pending</div>
          </div>
        </div>

        {/* Completion Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Repair Completion Rate</span>
            <span>{completionRate.toFixed(1)}%</span>
          </div>
          <Progress value={completionRate} className="h-2" />
        </div>

        {/* Average Repair Time */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <span className="text-sm font-medium">Average Repair Time</span>
          <span className="text-lg font-bold">{repairSummary.averageRepairTime}h</span>
        </div>

        {/* Length Summary for Cable Exposure and Third Party Work */}
        <div className="space-y-4">
          <h4 className="font-semibold text-sm">Affected Length Summary (meters)</h4>
          
          {/* Cable Exposure Summary */}
          <div className="p-4 border rounded-lg bg-orange-50 cursor-pointer hover:bg-orange-100 transition-colors" onClick={() => handleCardClick('cable_exposure')}>
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-orange-800">Cable Exposure</span>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </div>
            <div className="grid grid-cols-2 gap-4 mb-3">
              <div className="text-center">
                <div className="text-xl font-bold text-orange-600">{lengthSummary.totalCableExposureLength.toFixed(1)}m</div>
                <div className="text-xs text-orange-600">Total Affected</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-green-600">{lengthSummary.completedCableExposureLength.toFixed(1)}m</div>
                <div className="text-xs text-green-600">Repaired</div>
              </div>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>Repair Progress</span>
                <span>{cableExposureCompletionRate.toFixed(1)}%</span>
              </div>
              <Progress value={cableExposureCompletionRate} className="h-2" />
            </div>
          </div>

          {/* Third Party Work Summary */}
          <div className="p-4 border rounded-lg bg-blue-50 cursor-pointer hover:bg-blue-100 transition-colors" onClick={() => handleCardClick('third_party_work')}>
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-blue-800">Third Party Work</span>
              <Users className="h-4 w-4 text-blue-600" />
            </div>
            <div className="grid grid-cols-2 gap-4 mb-3">
              <div className="text-center">
                <div className="text-xl font-bold text-blue-600">{lengthSummary.totalThirdPartyLength.toFixed(1)}m</div>
                <div className="text-xs text-blue-600">Total Affected</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-green-600">{lengthSummary.completedThirdPartyLength.toFixed(1)}m</div>
                <div className="text-xs text-green-600">Resolved</div>
              </div>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>Resolution Progress</span>
                <span>{thirdPartyCompletionRate.toFixed(1)}%</span>
              </div>
              <Progress value={thirdPartyCompletionRate} className="h-2" />
            </div>
          </div>
        </div>

        {/* Recent Findings */}
        <div className="space-y-3">
          <h4 className="font-semibold text-sm">Recent Findings</h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {patrolFindings.map((finding) => (
              <div key={finding.id} className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50">
                <div className="flex-shrink-0 mt-1">
                  {getTypeIcon(finding.type)}
                </div>
                <div className="flex-grow min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="outline" className={getSeverityColor(finding.severity)}>
                      {finding.severity.toUpperCase()}
                    </Badge>
                    <Badge variant="secondary">
                      {getTypeLabel(finding.type)}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 mb-1">{finding.description}</p>
                  {finding.affectedLength && (finding.type === 'cable_exposure' || finding.type === 'third_party_work') && (
                    <div className="text-xs text-blue-600 mb-1">
                      Affected Length: {finding.affectedLength}m
                    </div>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{finding.location}</span>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(finding.status)}
                      <span className="capitalize">{finding.status.replace('_', ' ')}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Finding Types Distribution */}
        <div className="space-y-3">
          <h4 className="font-semibold text-sm">Finding Types Distribution</h4>
          <div className="grid grid-cols-2 gap-2">
            {[
              { type: 'cable_exposure', count: patrolFindings.filter(f => f.type === 'cable_exposure').length },
              { type: 'hdpe_exposure', count: patrolFindings.filter(f => f.type === 'hdpe_exposure').length },
              { type: 'third_party_work', count: patrolFindings.filter(f => f.type === 'third_party_work').length },
              { type: 'asset_condition', count: patrolFindings.filter(f => f.type === 'asset_condition').length }
            ].map((item) => (
              <div key={item.type} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-xs">{getTypeLabel(item.type as PatrolFinding['type'])}</span>
                <span className="text-sm font-bold">{item.count}</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
    
    {/* Route Details Modal */}
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {selectedType === 'cable_exposure' ? (
              <><AlertTriangle className="h-5 w-5 text-orange-600" /> Cable Exposure Details</>
            ) : (
              <><Users className="h-5 w-5 text-blue-600" /> Third Party Work Details</>
            )}
          </DialogTitle>
          <DialogDescription>
            Detailed breakdown of {selectedType === 'cable_exposure' ? 'cable exposure' : 'third party work'} findings by route
          </DialogDescription>
        </DialogHeader>
        
        {selectedType && (
          <div className="space-y-4">
            {getRouteDetails(selectedType).map((routeData, index) => {
              const completionRate = routeData.totalLength > 0 ? (routeData.completedLength / routeData.totalLength) * 100 : 0;
              return (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-lg">{routeData.route}</h3>
                    <Badge variant="outline" className={selectedType === 'cable_exposure' ? 'border-orange-300 text-orange-700' : 'border-blue-300 text-blue-700'}>
                      {routeData.findings.length} finding{routeData.findings.length > 1 ? 's' : ''}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="p-2 bg-gray-50 rounded">
                      <div className="text-lg font-bold text-gray-700">{routeData.totalLength.toFixed(1)}m</div>
                      <div className="text-xs text-gray-600">Total Affected</div>
                    </div>
                    <div className="p-2 bg-green-50 rounded">
                      <div className="text-lg font-bold text-green-600">{routeData.completedLength.toFixed(1)}m</div>
                      <div className="text-xs text-green-600">{selectedType === 'cable_exposure' ? 'Repaired' : 'Resolved'}</div>
                    </div>
                    <div className="p-2 bg-blue-50 rounded">
                      <div className="text-lg font-bold text-blue-600">{completionRate.toFixed(1)}%</div>
                      <div className="text-xs text-blue-600">Progress</div>
                    </div>
                  </div>
                  
                  <Progress value={completionRate} className="h-2" />
                  
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Findings:</h4>
                    {routeData.findings.map((finding) => (
                      <div key={finding.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className="flex-shrink-0 mt-1">
                          {getStatusIcon(finding.status)}
                        </div>
                        <div className="flex-grow">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant="outline" className={getSeverityColor(finding.severity)}>
                              {finding.severity.toUpperCase()}
                            </Badge>
                            <span className="text-xs text-gray-500">{finding.affectedLength}m</span>
                          </div>
                          <p className="text-sm text-gray-700 mb-1">{finding.description}</p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>{finding.location}</span>
                            <span className="capitalize">{finding.status.replace('_', ' ')}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </DialogContent>
    </Dialog>
    </div>
  );
};

export default PatrolReportCard;