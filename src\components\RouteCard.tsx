"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CheckCircle, XCircle, AlertTriangle, Edit, Check, X, Save } from "lucide-react";
import { cn } from "@/lib/utils";
import { NetworkRoute } from "@/data/networkRoutes";

interface RouteCardProps {
  route: NetworkRoute;
  onEdit: (route: NetworkRoute) => void;
  onClick: (routeId: string) => void;
}

const RouteCard: React.FC<RouteCardProps> = ({ route, onEdit, onClick }) => {
  const [coreNetworkTitle, setCoreNetworkTitle] = useState("Core Network Status Summary");
  const [isEditingCoreTitle, setIsEditingCoreTitle] = useState(false);
  const [tempCoreTitle, setTempCoreTitle] = useState(coreNetworkTitle);

  // State for Core Network tables
  const [table1Data, setTable1Data] = useState([
    {
      id: 1,
      noPort: "P001",
      trafficName: "Traffic A",
      otdrDistance: "12.5 km",
      groundDistance: "13.2 km",
      totalLoss: "0.8 dB",
      rsl: "-15.2 dBm"
    },
    {
      id: 2,
      noPort: "P002",
      trafficName: "Traffic B",
      otdrDistance: "8.3 km",
      groundDistance: "9.1 km",
      totalLoss: "0.6 dB",
      rsl: "-12.8 dBm"
    }
  ]);

  const [table2Data, setTable2Data] = useState([
    {
      id: 1,
      noPort: "P101",
      trafficName: "Traffic X",
      otdrDistance: "22.1 km",
      groundDistance: "23.8 km",
      totalLoss: "1.5 dB",
      rsl: "-20.3 dBm"
    },
    {
      id: 2,
      noPort: "P102",
      trafficName: "Traffic Y",
      otdrDistance: "18.9 km",
      groundDistance: "19.6 km",
      totalLoss: "1.1 dB",
      rsl: "-17.9 dBm"
    }
  ]);

  const [editingTable1, setEditingTable1] = useState<number | null>(null);
  const [editingTable2, setEditingTable2] = useState<number | null>(null);
  const [editData1, setEditData1] = useState<any>(null);
  const [editData2, setEditData2] = useState<any>(null);
  
  const [tableATitle, setTableATitle] = useState('Core Network Table A');
  const [isEditingTableATitle, setIsEditingTableATitle] = useState(false);
  const [tempTableATitle, setTempTableATitle] = useState('');
  
  const [tableBTitle, setTableBTitle] = useState('Core Network Table B');
  const [isEditingTableBTitle, setIsEditingTableBTitle] = useState(false);
  const [tempTableBTitle, setTempTableBTitle] = useState('');

  const handleEditCoreTitle = () => {
    setTempCoreTitle(coreNetworkTitle);
    setIsEditingCoreTitle(true);
  };

  const handleSaveCoreTitle = () => {
    setCoreNetworkTitle(tempCoreTitle);
    setIsEditingCoreTitle(false);
  };

  const handleCancelCoreEdit = () => {
    setTempCoreTitle(coreNetworkTitle);
    setIsEditingCoreTitle(false);
  };

  const handleCoreKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveCoreTitle();
    } else if (e.key === 'Escape') {
      handleCancelCoreEdit();
    }
  };

  // Fungsi untuk menangani editing tabel
  const handleEdit1 = (row: any) => {
    setEditingTable1(row.id);
    setEditData1({ ...row });
  };

  const handleEdit2 = (row: any) => {
    setEditingTable2(row.id);
    setEditData2({ ...row });
  };

  const handleSave1 = () => {
    if (editData1) {
      setTable1Data(prev => prev.map(item => item.id === editData1.id ? editData1 : item));
      setEditingTable1(null);
      setEditData1(null);
    }
  };

  const handleSave2 = () => {
    if (editData2) {
      setTable2Data(prev => prev.map(item => item.id === editData2.id ? editData2 : item));
      setEditingTable2(null);
      setEditData2(null);
    }
  };

  const handleCancel1 = () => {
    setEditingTable1(null);
    setEditData1(null);
  };

  const handleCancel2 = () => {
    setEditingTable2(null);
    setEditData2(null);
  };

  // Fungsi untuk edit label Table A
  const handleEditTableATitle = () => {
    setTempTableATitle(tableATitle);
    setIsEditingTableATitle(true);
  };

  const handleSaveTableATitle = () => {
    setTableATitle(tempTableATitle);
    setIsEditingTableATitle(false);
  };

  const handleCancelTableAEdit = () => {
    setTempTableATitle('');
    setIsEditingTableATitle(false);
  };

  const handleTableATitleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveTableATitle();
    } else if (e.key === 'Escape') {
      handleCancelTableAEdit();
    }
  };

  // Fungsi untuk edit label Table B
  const handleEditTableBTitle = () => {
    setTempTableBTitle(tableBTitle);
    setIsEditingTableBTitle(true);
  };

  const handleSaveTableBTitle = () => {
    setTableBTitle(tempTableBTitle);
    setIsEditingTableBTitle(false);
  };

  const handleCancelTableBEdit = () => {
    setTempTableBTitle('');
    setIsEditingTableBTitle(false);
  };

  const handleTableBTitleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveTableBTitle();
    } else if (e.key === 'Escape') {
      handleCancelTableBEdit();
    }
  };

  const renderTableCell = (value: string, field: string, editData: any, setEditData: (data: any) => void, isEditing: boolean) => {
    if (isEditing && editData) {
      return (
        <Input
          value={editData[field] as string}
          onChange={(e) => setEditData({ ...editData, [field]: e.target.value })}
          className="h-6 text-xs"
          onClick={(e) => e.stopPropagation()}
        />
      );
    }
    return <span className="text-xs">{value}</span>;
  };
  const getStatusBadgeColor = (status: NetworkRoute["status"]) => {
    switch (status) {
      case "operational":
        return "bg-green-500 hover:bg-green-600";
      case "degraded":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "down":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: NetworkRoute["status"]) => {
    switch (status) {
      case "operational":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "degraded":
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case "down":
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  const getSLAColorClass = (slaHours: number) => {
    if (slaHours < 6) {
      return "text-green-500";
    } else if (slaHours >= 6 && slaHours <= 7) {
      return "text-blue-500";
    } else {
      return "text-red-500";
    }
  };

  const aggregatedDistance = route.links.reduce((sum, link) => {
    const value = parseFloat(link.distance.replace(' km', ''));
    return sum + (isNaN(value) ? 0 : value);
  }, 0);
  const aggregatedTotalLoss = route.links.reduce((sum, link) => {
    const value = parseFloat(link.totalLoss.replace(' dB', ''));
    return sum + (isNaN(value) ? 0 : value);
  }, 0);

  return (
    <Card className="p-4 flex flex-col justify-between cursor-pointer hover:shadow-lg transition-shadow" onClick={() => onClick(route.id)}>
      <div>
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-lg">{route.name}</h3>
          <Badge className={getStatusBadgeColor(route.status)}>
            {route.status.charAt(0).toUpperCase() + route.status.slice(1)}
          </Badge>
        </div>
        <div className="flex items-center text-sm text-muted-foreground mb-3">
          {getStatusIcon(route.status)}
          <span className="ml-2">ID: {route.id}</span>
        </div>
        <div className="text-sm text-muted-foreground grid grid-cols-[max-content_1fr] gap-x-2">
          {/* Total Loss */}
          <div className="flex justify-between items-baseline">
            <span className="font-medium">Total Loss</span>
            <span>:</span>
          </div>
          <p>{aggregatedTotalLoss.toFixed(1)} dB</p>

          {/* Distance */}
          <div className="flex justify-between items-baseline">
            <span className="font-medium">Distance</span>
            <span>:</span>
          </div>
          <p>{aggregatedDistance.toFixed(1)} km</p>

          {/* Assets Label */}
          <div className="flex justify-between items-baseline">
            <span className="font-medium">Assets</span>
            <span>:</span>
          </div>
          {/* Individual Asset Details (indented) */}
          <div className="col-span-full pl-4">
            {route.assets.map(asset => (
              <p key={asset.id}>{asset.type} ({asset.count})</p>
            ))}
          </div>
        </div>

        {/* New section to display individual links */}
        {route.links.length > 0 && (
          <div className="mt-4 pt-4 border-t border-dashed border-muted-foreground/30">
            <h5 className="font-semibold text-sm mb-2">Links:</h5>
            <div className="space-y-1">
              {route.links.map((link) => (
                <div key={link.id} className="text-xs text-muted-foreground grid grid-cols-[max-content_1fr] gap-x-2">
                  <div className="font-medium flex justify-between">
                    <span className="text-blue-500">{link.name}</span>
                    <span>:</span>
                  </div>
                  <span>{link.distance} / {link.totalLoss}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      
      {/* Core Network Summary Card */}
      <Card className="mt-4 bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900">
        <CardHeader>
          <div className="flex items-center justify-between">
            {isEditingCoreTitle ? (
              <div className="flex items-center gap-2 flex-1">
                <Input
                  value={tempCoreTitle}
                  onChange={(e) => setTempCoreTitle(e.target.value)}
                  onKeyDown={handleCoreKeyPress}
                  className="text-lg font-semibold text-slate-800 dark:text-slate-200 bg-transparent border-slate-300 dark:border-slate-600"
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => { e.stopPropagation(); handleSaveCoreTitle(); }}
                  className="h-8 w-8 p-0"
                >
                  <Check className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => { e.stopPropagation(); handleCancelCoreEdit(); }}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg text-slate-800 dark:text-slate-200">{coreNetworkTitle}</CardTitle>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => { e.stopPropagation(); handleEditCoreTitle(); }}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-start gap-4">
            {/* Table 1 */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg p-3" style={{width: '42%'}}>
              <div className="flex items-center justify-between mb-3">
                {isEditingTableATitle ? (
                  <div className="flex items-center gap-2 flex-1">
                    <Input
                      value={tempTableATitle}
                      onChange={(e) => setTempTableATitle(e.target.value)}
                      onKeyDown={handleTableATitleKeyPress}
                      className="text-sm font-semibold text-blue-800 dark:text-blue-200 bg-transparent border-blue-300 dark:border-blue-600"
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                    />
                    <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleSaveTableATitle(); }} className="h-6 w-6 p-0">
                      <Check className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleCancelTableAEdit(); }} className="h-6 w-6 p-0">
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <h3 className="text-sm font-semibold text-blue-800 dark:text-blue-200">{tableATitle}</h3>
                    <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleEditTableATitle(); }} className="h-6 w-6 p-0">
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
              <div className="overflow-x-auto">
                <table className="w-full text-xs">
                  <thead>
                    <tr className="border-b border-blue-200 dark:border-blue-700">
                      <th className="text-left p-1 font-semibold text-blue-800 dark:text-blue-200">Port</th>
                      <th className="text-left p-1 font-semibold text-blue-800 dark:text-blue-200">Traffic</th>
                      <th className="text-left p-1 font-semibold text-blue-800 dark:text-blue-200">OTDR</th>
                      <th className="text-left p-1 font-semibold text-blue-800 dark:text-blue-200">Loss</th>
                      <th className="text-left p-1 font-semibold text-blue-800 dark:text-blue-200">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {table1Data.map((row) => {
                      const isEditing = editingTable1 === row.id;
                      return (
                        <tr key={row.id} className="border-b border-blue-100 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900">
                          <td className="p-1">
                            {renderTableCell(row.noPort, 'noPort', editData1, setEditData1, isEditing)}
                          </td>
                          <td className="p-1">
                            {renderTableCell(row.trafficName, 'trafficName', editData1, setEditData1, isEditing)}
                          </td>
                          <td className="p-1">
                            {renderTableCell(row.otdrDistance, 'otdrDistance', editData1, setEditData1, isEditing)}
                          </td>
                          <td className="p-1">
                            {renderTableCell(row.totalLoss, 'totalLoss', editData1, setEditData1, isEditing)}
                          </td>
                          <td className="p-1">
                            {isEditing ? (
                              <div className="flex gap-1">
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleSave1(); }} className="h-5 w-5 p-0">
                                  <Save className="h-2 w-2" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleCancel1(); }} className="h-5 w-5 p-0">
                                  <X className="h-2 w-2" />
                                </Button>
                              </div>
                            ) : (
                              <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleEdit1(row); }} className="h-5 w-5 p-0">
                                <Edit className="h-2 w-2" />
                              </Button>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Connection Lines */}
            <div className="flex flex-col justify-center items-center h-full" style={{width: '16%'}}>
              <div className="relative flex flex-col justify-center items-center" style={{height: '100%', marginTop: '10px'}}>
                {/* First Line - Blue */}
                <div className="h-0.5 bg-blue-500 relative" style={{width: '100px', marginBottom: '10px'}}>
                  <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
                    <div className="w-0 h-0 border-l-4 border-l-blue-500 border-t-2 border-t-transparent border-b-2 border-b-transparent"></div>
                  </div>
                </div>
                {/* Second Line - Orange */}
                <div className="h-0.5 bg-orange-500 relative" style={{width: '100px'}}>
                  <div className="absolute left-0 top-1/2 transform -translate-y-1/2">
                    <div className="w-0 h-0 border-r-4 border-r-orange-500 border-t-2 border-t-transparent border-b-2 border-b-transparent"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Table 2 */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg p-3" style={{width: '42%'}}>
              <div className="flex items-center justify-between mb-3">
                {isEditingTableBTitle ? (
                  <div className="flex items-center gap-2 flex-1">
                    <Input
                      value={tempTableBTitle}
                      onChange={(e) => setTempTableBTitle(e.target.value)}
                      onKeyDown={handleTableBTitleKeyPress}
                      className="text-sm font-semibold text-green-800 dark:text-green-200 bg-transparent border-green-300 dark:border-green-600"
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                    />
                    <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleSaveTableBTitle(); }} className="h-6 w-6 p-0">
                      <Check className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleCancelTableBEdit(); }} className="h-6 w-6 p-0">
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <h3 className="text-sm font-semibold text-green-800 dark:text-green-200">{tableBTitle}</h3>
                    <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleEditTableBTitle(); }} className="h-6 w-6 p-0">
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
              <div className="overflow-x-auto">
                <table className="w-full text-xs">
                  <thead>
                    <tr className="border-b border-green-200 dark:border-green-700">
                      <th className="text-left p-1 font-semibold text-green-800 dark:text-green-200">Port</th>
                      <th className="text-left p-1 font-semibold text-green-800 dark:text-green-200">Traffic</th>
                      <th className="text-left p-1 font-semibold text-green-800 dark:text-green-200">OTDR</th>
                      <th className="text-left p-1 font-semibold text-green-800 dark:text-green-200">Loss</th>
                      <th className="text-left p-1 font-semibold text-green-800 dark:text-green-200">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {table2Data.map((row) => {
                      const isEditing = editingTable2 === row.id;
                      return (
                        <tr key={row.id} className="border-b border-green-100 dark:border-green-800 hover:bg-green-50 dark:hover:bg-green-900">
                          <td className="p-1">
                            {renderTableCell(row.noPort, 'noPort', editData2, setEditData2, isEditing)}
                          </td>
                          <td className="p-1">
                            {renderTableCell(row.trafficName, 'trafficName', editData2, setEditData2, isEditing)}
                          </td>
                          <td className="p-1">
                            {renderTableCell(row.otdrDistance, 'otdrDistance', editData2, setEditData2, isEditing)}
                          </td>
                          <td className="p-1">
                            {renderTableCell(row.totalLoss, 'totalLoss', editData2, setEditData2, isEditing)}
                          </td>
                          <td className="p-1">
                            {isEditing ? (
                              <div className="flex gap-1">
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleSave2(); }} className="h-5 w-5 p-0">
                                  <Save className="h-2 w-2" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleCancel2(); }} className="h-5 w-5 p-0">
                                  <X className="h-2 w-2" />
                                </Button>
                              </div>
                            ) : (
                              <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleEdit2(row); }} className="h-5 w-5 p-0">
                                <Edit className="h-2 w-2" />
                              </Button>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mt-4 flex justify-end">
        <Button variant="outline" size="sm" onClick={(e) => { e.stopPropagation(); onEdit(route); }}>
          <Edit className="mr-2 h-4 w-4" /> Edit
        </Button>
      </div>
    </Card>
  );
};

export default RouteCard;