import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types
export interface Profile {
  id: string
  username?: string
  full_name?: string
  avatar_url?: string
  phone?: string
  department?: string
  position?: string
  is_active: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

export interface Role {
  id: string
  name: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
  updated_at: string
}

export interface UserRole {
  user_id: string
  role_id: string
  assigned_by?: string
  created_at: string
}

export interface RolePermission {
  role_id: string
  permission_id: string
  created_at: string
}

export interface AuditLog {
  id: string
  user_id?: string
  action: string
  resource_type?: string
  resource_id?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  ip_address?: string
  user_agent?: string
  created_at: string
}

// Auth helper functions
export const authHelpers = {
  // Get current user profile
  async getCurrentUserProfile(): Promise<Profile | null> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return null

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) {
      console.error('Error fetching user profile:', error)
      return null
    }

    return data
  },

  // Get user roles
  async getUserRoles(userId?: string): Promise<Role[]> {
    const { data: { user } } = await supabase.auth.getUser()
    const targetUserId = userId || user?.id

    if (!targetUserId) return []

    const { data, error } = await supabase
      .rpc('get_user_roles', { user_uuid: targetUserId })

    if (error) {
      console.error('Error fetching user roles:', error)
      return []
    }

    return data || []
  },

  // Check if user has permission
  async hasPermission(permission: string, userId?: string): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser()
    const targetUserId = userId || user?.id

    if (!targetUserId) return false

    const { data, error } = await supabase
      .rpc('user_has_permission', {
        user_uuid: targetUserId,
        permission_name: permission
      })

    if (error) {
      console.error('Error checking permission:', error)
      return false
    }

    return data || false
  },

  // Check if user has any of the specified roles
  async hasRole(roleNames: string | string[], userId?: string): Promise<boolean> {
    const roles = await this.getUserRoles(userId)
    const roleNamesArray = Array.isArray(roleNames) ? roleNames : [roleNames]
    
    return roles.some(role => roleNamesArray.includes(role.role_name))
  },

  // Update user profile
  async updateProfile(updates: Partial<Profile>): Promise<Profile | null> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return null

    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating profile:', error)
      return null
    }

    return data
  },

  // Update last login
  async updateLastLogin(): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    await supabase
      .from('profiles')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id)
  },

  // Log user activity
  async logActivity(
    action: string,
    resourceType?: string,
    resourceId?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    await supabase
      .from('audit_logs')
      .insert({
        user_id: user?.id,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        old_values: oldValues,
        new_values: newValues,
        created_at: new Date().toISOString()
      })
  }
}

// Admin helper functions (requires admin role)
export const adminHelpers = {
  // Get all users with their roles
  async getAllUsers(): Promise<any[]> {
    const { data, error } = await supabase
      .from('user_roles_summary')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching users:', error)
      return []
    }

    return data || []
  },

  // Assign role to user
  async assignRole(userId: string, roleId: string): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser()
    
    const { error } = await supabase
      .from('user_roles')
      .insert({
        user_id: userId,
        role_id: roleId,
        assigned_by: user?.id
      })

    if (error) {
      console.error('Error assigning role:', error)
      return false
    }

    // Log the activity
    await authHelpers.logActivity(
      'assign_role',
      'user_roles',
      `${userId}-${roleId}`,
      undefined,
      { user_id: userId, role_id: roleId }
    )

    return true
  },

  // Remove role from user
  async removeRole(userId: string, roleId: string): Promise<boolean> {
    const { error } = await supabase
      .from('user_roles')
      .delete()
      .eq('user_id', userId)
      .eq('role_id', roleId)

    if (error) {
      console.error('Error removing role:', error)
      return false
    }

    // Log the activity
    await authHelpers.logActivity(
      'remove_role',
      'user_roles',
      `${userId}-${roleId}`,
      { user_id: userId, role_id: roleId },
      undefined
    )

    return true
  },

  // Get all roles
  async getAllRoles(): Promise<Role[]> {
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) {
      console.error('Error fetching roles:', error)
      return []
    }

    return data || []
  },

  // Get all permissions
  async getAllPermissions(): Promise<Permission[]> {
    const { data, error } = await supabase
      .from('permissions')
      .select('*')
      .order('resource', { ascending: true })

    if (error) {
      console.error('Error fetching permissions:', error)
      return []
    }

    return data || []
  },

  // Get audit logs
  async getAuditLogs(limit = 100): Promise<AuditLog[]> {
    const { data, error } = await supabase
      .from('audit_logs')
      .select(`
        *,
        profiles:user_id (
          username,
          full_name
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching audit logs:', error)
      return []
    }

    return data || []
  }
}

export default supabase
