"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CheckCircle, XCircle, AlertTriangle, Edit, Check, X, Save, Network, Route } from "lucide-react";
import { cn } from "@/lib/utils";
import { NetworkRoute } from "@/data/networkRoutes";

interface IntegratedRouteCardProps {
  route: NetworkRoute;
  onEdit: (route: NetworkRoute) => void;
  onClick: (routeId: string) => void;
}

interface CoreNetworkData {
  id: number;
  noPort: string;
  trafficName: string;
  otdrDistance: string;
  groundDistance: string;
  totalLoss: string;
  rsl: string;
}

const IntegratedRouteCard: React.FC<IntegratedRouteCardProps> = ({ route, onEdit, onClick }) => {
  // Route Card States
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Core Network States - Title is now static

  // Table Data States
  const [table1Data, setTable1Data] = useState<CoreNetworkData[]>([
    {
      id: 1,
      noPort: "P001",
      trafficName: "Traffic A",
      otdrDistance: "12.5 km",
      groundDistance: "13.2 km",
      totalLoss: "0.8 dB",
      rsl: "-15.2 dBm"
    },
    {
      id: 2,
      noPort: "P002",
      trafficName: "Traffic B",
      otdrDistance: "8.3 km",
      groundDistance: "9.1 km",
      totalLoss: "0.6 dB",
      rsl: "-12.8 dBm"
    }
  ]);

  const [table2Data, setTable2Data] = useState<CoreNetworkData[]>([
    {
      id: 1,
      noPort: "P101",
      trafficName: "Traffic X",
      otdrDistance: "22.1 km",
      groundDistance: "23.8 km",
      totalLoss: "1.5 dB",
      rsl: "-20.3 dBm"
    },
    {
      id: 2,
      noPort: "P102",
      trafficName: "Traffic Y",
      otdrDistance: "18.9 km",
      groundDistance: "19.6 km",
      totalLoss: "1.1 dB",
      rsl: "-17.9 dBm"
    }
  ]);

  // Editing States
  const [editingTable1, setEditingTable1] = useState<number | null>(null);
  const [editingTable2, setEditingTable2] = useState<number | null>(null);
  const [editData1, setEditData1] = useState<CoreNetworkData | null>(null);
  const [editData2, setEditData2] = useState<CoreNetworkData | null>(null);
  


  // Helper Functions
  const getStatusBadgeColor = (status: NetworkRoute["status"]) => {
    switch (status) {
      case "operational":
        return "bg-green-500 hover:bg-green-600";
      case "degraded":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "down":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: NetworkRoute["status"]) => {
    switch (status) {
      case "operational":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "degraded":
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case "down":
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getSLAColorClass = (slaHours: number) => {
    if (slaHours < 6) {
      return "text-green-500";
    } else if (slaHours >= 6 && slaHours <= 7) {
      return "text-blue-500";
    } else {
      return "text-red-500";
    }
  };

  // Core Network Title is now static - no editing handlers needed

  // Table Editing Handlers
  const handleEdit1 = (row: CoreNetworkData) => {
    setEditingTable1(row.id);
    setEditData1({ ...row });
  };

  const handleEdit2 = (row: CoreNetworkData) => {
    setEditingTable2(row.id);
    setEditData2({ ...row });
  };

  const handleSave1 = () => {
    if (editData1) {
      setTable1Data(prev => prev.map(item => item.id === editData1.id ? editData1 : item));
      setEditingTable1(null);
      setEditData1(null);
    }
  };

  const handleSave2 = () => {
    if (editData2) {
      setTable2Data(prev => prev.map(item => item.id === editData2.id ? editData2 : item));
      setEditingTable2(null);
      setEditData2(null);
    }
  };

  const handleCancel1 = () => {
    setEditingTable1(null);
    setEditData1(null);
  };

  const handleCancel2 = () => {
    setEditingTable2(null);
    setEditData2(null);
  };



  const renderTableCell = (value: string, field: keyof CoreNetworkData, editData: CoreNetworkData | null, setEditData: (data: CoreNetworkData) => void, isEditing: boolean) => {
    if (isEditing && editData) {
      return (
        <Input
          value={editData[field] as string}
          onChange={(e) => setEditData({ ...editData, [field]: e.target.value })}
          className="h-6 text-xs"
          onClick={(e) => e.stopPropagation()}
        />
      );
    }
    return <span className="text-xs">{value}</span>;
  };

  // Calculate aggregated values
  const aggregatedDistance = route.links.reduce((sum, link) => {
    const value = parseFloat(link.distance.replace(' km', ''));
    return sum + (isNaN(value) ? 0 : value);
  }, 0);
  
  const aggregatedTotalLoss = route.links.reduce((sum, link) => {
    const value = parseFloat(link.totalLoss.replace(' dB', ''));
    return sum + (isNaN(value) ? 0 : value);
  }, 0);

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
      {/* Main Route Information */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Route className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold cursor-pointer hover:text-blue-600 transition-colors" onClick={() => onClick(route.id)}>
                {route.name}
              </CardTitle>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                {getStatusIcon(route.status)}
                <span>ID: {route.id}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusBadgeColor(route.status)}>
              {route.status.charAt(0).toUpperCase() + route.status.slice(1)}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-muted-foreground hover:text-foreground"
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Route Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 p-3 rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">Total Loss</p>
            <p className="font-semibold text-blue-600 dark:text-blue-400">{aggregatedTotalLoss.toFixed(1)} dB</p>
          </div>
          <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 p-3 rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">Distance</p>
            <p className="font-semibold text-green-600 dark:text-green-400">{aggregatedDistance.toFixed(1)} km</p>
          </div>
          <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 p-3 rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">Average SLA</p>
            <p className={cn("font-semibold", getSLAColorClass(route.averageSLAHours))}>
              {route.averageSLAHours.toFixed(1)} hrs
            </p>
          </div>
          <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 p-3 rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">SLA Last</p>
            <p className={cn("font-semibold", getSLAColorClass(route.slaLast))}>
              {route.slaLast.toFixed(1)} hrs
            </p>
          </div>
        </div>

        {/* Assets Summary */}
        <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg">
          <h4 className="text-sm font-medium mb-2">Assets</h4>
          <div className="flex flex-wrap gap-2">
            {route.assets.map(asset => (
              <Badge key={asset.id} variant="secondary" className="text-xs">
                {asset.type} ({asset.count})
              </Badge>
            ))}
          </div>
        </div>

        {/* Links Summary */}
        {route.links.length > 0 && (
          <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Network Links</h4>
            <div className="space-y-1">
              {route.links.map((link) => (
                <div key={link.id} className="flex justify-between items-center text-xs">
                  <span className="text-blue-600 dark:text-blue-400 font-medium">{link.name}</span>
                  <span className="text-muted-foreground">{link.distance} / {link.totalLoss}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Core Network Status - Expandable */}
        {isExpanded && (
          <Card className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 border-dashed">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Network className="w-4 h-4 text-slate-600 dark:text-slate-400" />
                  <CardTitle className="text-sm">Core Network Status Summary</CardTitle>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Core Network Description Format */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Source Network */}
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">Source Network</h3>
                    <div className="space-y-3">
                      {table1Data.map((row) => {
                        const isEditing = editingTable1 === row.id;
                        return (
                          <div key={row.id} className="bg-white/50 dark:bg-slate-800/30 rounded-lg p-3 space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Port:</span>
                              <div className="flex items-center gap-2">
                                {isEditing ? (
                                  <Input
                                    value={editData1.noPort || ''}
                                    onChange={(e) => setEditData1({...editData1, noPort: e.target.value})}
                                    className="h-6 text-xs w-20"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                ) : (
                                  <span className="text-xs text-blue-800 dark:text-blue-200">{row.noPort}</span>
                                )}
                                {!isEditing && (
                                  <Button size="sm" variant="ghost" onClick={(e) => { e.stopPropagation(); handleEdit1(row); }} className="h-4 w-4 p-0">
                                    <Edit className="h-2 w-2" />
                                  </Button>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Traffic:</span>
                              {isEditing ? (
                                <Input
                                  value={editData1.trafficName || ''}
                                  onChange={(e) => setEditData1({...editData1, trafficName: e.target.value})}
                                  className="h-6 text-xs w-24"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <span className="text-xs text-blue-800 dark:text-blue-200">{row.trafficName}</span>
                              )}
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">OTDR:</span>
                              {isEditing ? (
                                <Input
                                  value={editData1.otdrDistance || ''}
                                  onChange={(e) => setEditData1({...editData1, otdrDistance: e.target.value})}
                                  className="h-6 text-xs w-20"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <span className="text-xs text-blue-800 dark:text-blue-200">{row.otdrDistance}</span>
                              )}
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Loss:</span>
                              {isEditing ? (
                                <Input
                                  value={editData1.totalLoss || ''}
                                  onChange={(e) => setEditData1({...editData1, totalLoss: e.target.value})}
                                  className="h-6 text-xs w-20"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <span className="text-xs text-blue-800 dark:text-blue-200">{row.totalLoss}</span>
                              )}
                            </div>
                            {isEditing && (
                              <div className="flex gap-2 justify-end pt-2">
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleSave1(); }} className="h-6 px-2">
                                  <Save className="h-3 w-3" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleCancel1(); }} className="h-6 px-2">
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Destination Network */}
                  <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-3">Destination Network</h3>
                    <div className="space-y-3">
                      {table2Data.map((row) => {
                        const isEditing = editingTable2 === row.id;
                        return (
                          <div key={row.id} className="bg-white/50 dark:bg-slate-800/30 rounded-lg p-3 space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-green-700 dark:text-green-300">Port:</span>
                              <div className="flex items-center gap-2">
                                {isEditing ? (
                                  <Input
                                    value={editData2.noPort || ''}
                                    onChange={(e) => setEditData2({...editData2, noPort: e.target.value})}
                                    className="h-6 text-xs w-20"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                ) : (
                                  <span className="text-xs text-green-800 dark:text-green-200">{row.noPort}</span>
                                )}
                                {!isEditing && (
                                  <Button size="sm" variant="ghost" onClick={(e) => { e.stopPropagation(); handleEdit2(row); }} className="h-4 w-4 p-0">
                                    <Edit className="h-2 w-2" />
                                  </Button>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-green-700 dark:text-green-300">Traffic:</span>
                              {isEditing ? (
                                <Input
                                  value={editData2.trafficName || ''}
                                  onChange={(e) => setEditData2({...editData2, trafficName: e.target.value})}
                                  className="h-6 text-xs w-24"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <span className="text-xs text-green-800 dark:text-green-200">{row.trafficName}</span>
                              )}
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-green-700 dark:text-green-300">OTDR:</span>
                              {isEditing ? (
                                <Input
                                  value={editData2.otdrDistance || ''}
                                  onChange={(e) => setEditData2({...editData2, otdrDistance: e.target.value})}
                                  className="h-6 text-xs w-20"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <span className="text-xs text-green-800 dark:text-green-200">{row.otdrDistance}</span>
                              )}
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-green-700 dark:text-green-300">Loss:</span>
                              {isEditing ? (
                                <Input
                                  value={editData2.totalLoss || ''}
                                  onChange={(e) => setEditData2({...editData2, totalLoss: e.target.value})}
                                  className="h-6 text-xs w-20"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <span className="text-xs text-green-800 dark:text-green-200">{row.totalLoss}</span>
                              )}
                            </div>
                            {isEditing && (
                              <div className="flex gap-2 justify-end pt-2">
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleSave2(); }} className="h-6 px-2">
                                  <Save className="h-3 w-3" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); handleCancel2(); }} className="h-6 px-2">
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Connection Status */}
                <div className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 rounded-lg p-3">
                  <div className="flex items-center justify-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-xs text-muted-foreground">Source Network</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="h-0.5 bg-gradient-to-r from-blue-500 to-green-500 w-8"></div>
                      <div className="w-0 h-0 border-l-2 border-l-green-500 border-t-1 border-t-transparent border-b-1 border-b-transparent"></div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-xs text-muted-foreground">Destination Network</span>
                    </div>
                  </div>
                  <div className="text-center mt-2">
                    <span className="text-xs text-muted-foreground font-medium">Network Connection Active</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-2 border-t border-dashed">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-muted-foreground hover:text-foreground"
          >
            <Network className="mr-2 h-4 w-4" />
            {isExpanded ? 'Hide' : 'Show'} Core Network
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => { e.stopPropagation(); onEdit(route); }}
          >
            <Edit className="mr-2 h-4 w-4" /> Edit Route
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default IntegratedRouteCard;