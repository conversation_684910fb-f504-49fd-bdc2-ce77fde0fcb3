/* Custom styles for sidebar sub-items */

/* Sub-item hover effects */
.sidebar-sub-item {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.sidebar-sub-item:hover {
  transform: translateX(2px);
}

.sidebar-sub-item:hover::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: linear-gradient(to bottom, #0ea5e9, #38bdf8);
  border-radius: 2px;
  opacity: 0.8;
}

/* Active sub-item indicator */
.sidebar-sub-item.active::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 80%;
  background: linear-gradient(to bottom, #0284c7, #0ea5e9);
  border-radius: 2px;
}

/* Sky blue gradient for sub-items */
.sidebar-sub-item-text {
  background: linear-gradient(135deg, #0ea5e9, #38bdf8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
}

/* Icon styling for sub-items */
.sidebar-sub-item-icon {
  color: #0ea5e9;
  filter: drop-shadow(0 1px 2px rgba(14, 165, 233, 0.1));
}

/* Smooth transitions */
.sidebar-item-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover state for sub-items */
.sidebar-sub-item:hover .sidebar-sub-item-text {
  background: linear-gradient(135deg, #0284c7, #0ea5e9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-sub-item:hover .sidebar-sub-item-icon {
  color: #0284c7;
  transform: scale(1.05);
}

/* Focus states for accessibility */
.sidebar-sub-item:focus-within {
  outline: 2px solid #38bdf8;
  outline-offset: 2px;
  border-radius: 6px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .sidebar-sub-item-text {
    background: linear-gradient(135deg, #38bdf8, #7dd3fc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .sidebar-sub-item-icon {
    color: #38bdf8;
  }
  
  .sidebar-sub-item:hover .sidebar-sub-item-text {
    background: linear-gradient(135deg, #0ea5e9, #38bdf8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .sidebar-sub-item:hover .sidebar-sub-item-icon {
    color: #0ea5e9;
  }
}
