"use client";

import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import "./sidebar-styles.css";
import { Button } from "@/components/ui/button";
import { Home, Ticket, Cpu, Network, Shield, FileText, AlertTriangle, Calendar, Users, Settings } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "../contexts/AuthContext";
import UserProfile from "./UserProfile";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

const Sidebar: React.FC = () => {
  const location = useLocation();
  const [currentTime, setCurrentTime] = useState(new Date());
  const { isAuthenticated, checkPermission } = useAuth();
  const [isAdminMenuOpen, setIsAdminMenuOpen] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Base navigation menu items
  const baseNavItems = [
    {
      name: "Dashboard",
      href: "/",
      icon: Home,
    },
    {
      name: "Problem Handling",
      href: "/problem-handling",
      icon: AlertTriangle,
    },
    {
      name: "Trouble Tickets",
      href: "/problem-handling/trouble-tickets",
      icon: Ticket,
      isSubItem: true,
      requiredPermission: "view_tickets",
    },
    {
      name: "Maintenance Timeline",
      href: "/problem-handling/maintenance",
      icon: Calendar,
      isSubItem: true,
      requiredPermission: "view_maintenance",
    },
    {
      name: "Core Management",
      href: "/core-management",
      icon: Cpu,
    },
    {
      name: "Port Management",
      href: "/port-management",
      icon: Network,
      isSubItem: true,
    },
    {
      name: "Asset Management",
      href: "/asset-management",
      icon: Cpu,
    },
    {
      name: "Patrol",
      href: "/patrol",
      icon: Shield,
    },
    {
      name: "SOR OTDR",
      href: "/patrol/sor",
      icon: FileText,
      isSubItem: true,
    },
  ];
  
  // Admin navigation menu items
  const adminNavItems = [
    {
      name: "User Management",
      href: "/user-management",
      icon: Users,
      requiredPermission: "manage_users",
    },
    {
      name: "Role Management",
      href: "/role-management",
      icon: Settings,
      requiredPermission: "manage_users",
    },
  ];
  
  // Combine navigation menus based on user permissions
  const navItems = baseNavItems.filter(item => 
    !item.requiredPermission || checkPermission(item.requiredPermission)
  );

  return (
    <div className="flex flex-col h-full border-r bg-sidebar text-sidebar-foreground p-4">
      <div className="mb-8 text-2xl font-bold text-sky-500">
        Network Management Service
      </div>
      <Separator className="mb-8 bg-sidebar-border" />
      <nav className="flex-grow space-y-2">
        {/* Main Navigation Menu */}
        {navItems.map((item) => {
          const isActive = location.pathname === item.href;
          const Icon = item.icon;
          return (
            <Button
              key={item.name}
              asChild
              variant="ghost"
              className={cn(
                "w-full justify-start text-sm py-3 rounded-lg sidebar-item-transition",
                item.isSubItem ? "pl-12 py-2 ml-2 sidebar-sub-item" : "px-4", // Increased indent and added margin
                isActive
                  ? item.isSubItem
                    ? "bg-sky-100 text-sky-700 hover:bg-sky-200 border-l-4 border-sky-500 active" // Special active style for sub items
                    : "bg-sidebar-primary text-sidebar-primary-foreground hover:bg-sidebar-primary/90"
                  : item.isSubItem
                    ? "hover:bg-sky-50 hover:text-sky-600 text-sky-500" // Sky blue for sub items
                    : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              )}
            >
              <Link to={item.href} className="flex items-center w-full">
                <Icon className={cn(
                  "flex-shrink-0",
                  item.isSubItem ? "h-4 w-4 mr-3 sidebar-sub-item-icon" : "h-5 w-5 mr-3" // Smaller icon for sub items
                )} />
                <span className={cn(
                  "font-medium",
                  item.isSubItem ? "sidebar-sub-item-text text-sm" : "text-base" // Sky blue text for sub items
                )}>
                  {item.name}
                </span>
              </Link>
            </Button>
          );
        })}
        
        {/* Admin Menu */}
        {isAuthenticated && checkPermission("manage_users") && (
          <Collapsible 
            open={isAdminMenuOpen} 
            onOpenChange={setIsAdminMenuOpen}
            className="mt-4"
          >
            <CollapsibleTrigger asChild>
              <Button 
                variant="ghost" 
                className="w-full justify-between text-sm py-3 px-4 rounded-lg hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              >
                <div className="flex items-center">
                  <Settings className="h-5 w-5 mr-3" />
                  <span>Admin Settings</span>
                </div>
                <span className="text-xs">{isAdminMenuOpen ? "▲" : "▼"}</span>
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-1 mt-1">
              {adminNavItems.map((item) => {
                const isActive = location.pathname === item.href;
                const Icon = item.icon;
                return (
                  <Button
                    key={item.name}
                    asChild
                    variant="ghost"
                    className={cn(
                      "w-full justify-start text-sm py-2 pl-12 ml-2 rounded-lg sidebar-item-transition sidebar-sub-item", // Increased indent and added margin
                      isActive
                        ? "bg-sky-100 text-sky-700 hover:bg-sky-200 border-l-4 border-sky-500 active" // Special active style for admin sub items
                        : "hover:bg-sky-50 hover:text-sky-600 text-sky-500" // Sky blue for admin sub items
                    )}
                  >
                    <Link to={item.href} className="flex items-center w-full">
                      <Icon className="h-4 w-4 mr-3 text-sky-500 flex-shrink-0" /> {/* Sky blue icon */}
                      <span className="text-sky-600 text-sm font-medium">{item.name}</span> {/* Sky blue text */}
                    </Link>
                  </Button>
                );
              })}
            </CollapsibleContent>
          </Collapsible>
        )}
      </nav>
      
      {/* Clock Section */}
      <div className="mt-auto pt-4">
        <div className="text-center mb-4">
          <p className="text-xs text-sidebar-foreground/70 mb-1">
            Jakarta, Indonesia
          </p>
          <p className="text-2xl font-bold text-blue-800">
            {currentTime.toLocaleTimeString('id-ID', {
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })}
          </p>
        </div>
        <Separator className="mb-4 bg-sidebar-border" />
        {/* User Profile Section */}
         <UserProfile compact={true} />
      </div>
    </div>
  );
};

export default Sidebar;