// Mock Supabase client untuk development ketika environment variables belum dikonfigurasi
// Hanya untuk development - jangan gunakan di production

console.warn('🚨 Using Mock Supabase Client - Configure real Supabase for production!')

// Mock auth response
const mockAuthResponse = {
  data: {
    user: null,
    session: null
  },
  error: null
}

// Mock Supabase client
export const supabase = {
  auth: {
    getSession: async () => mockAuthResponse,
    getUser: async () => mockAuthResponse,
    signInWithPassword: async (credentials: any) => {
      console.warn('Mock login attempt:', credentials.email)
      return {
        data: {
          user: {
            id: 'mock-user-id',
            email: credentials.email,
            created_at: new Date().toISOString()
          },
          session: {
            access_token: 'mock-token',
            expires_at: Date.now() + 3600000
          }
        },
        error: null
      }
    },
    signOut: async () => {
      console.warn('Mock logout')
      return { error: null }
    },
    onAuthStateChange: (callback: any) => {
      console.warn('Mock auth state change listener')
      return {
        data: {
          subscription: {
            unsubscribe: () => console.warn('Mock unsubscribe')
          }
        }
      }
    }
  },
  from: (table: string) => ({
    select: (columns?: string) => ({
      eq: (column: string, value: any) => ({
        single: async () => {
          console.warn(`Mock query: SELECT ${columns || '*'} FROM ${table} WHERE ${column} = ${value}`)
          return { data: null, error: null }
        }
      }),
      order: (column: string, options?: any) => ({
        limit: (count: number) => ({
          async then(resolve: any) {
            console.warn(`Mock query: SELECT ${columns || '*'} FROM ${table} ORDER BY ${column} LIMIT ${count}`)
            resolve({ data: [], error: null })
          }
        })
      })
    }),
    insert: (data: any) => ({
      select: () => ({
        single: async () => {
          console.warn(`Mock insert into ${table}:`, data)
          return { data: null, error: null }
        }
      }),
      async then(resolve: any) {
        console.warn(`Mock insert into ${table}:`, data)
        resolve({ data: null, error: null })
      }
    }),
    update: (data: any) => ({
      eq: (column: string, value: any) => ({
        select: () => ({
          single: async () => {
            console.warn(`Mock update ${table} SET ... WHERE ${column} = ${value}:`, data)
            return { data: null, error: null }
          }
        })
      })
    }),
    delete: () => ({
      eq: (column: string, value: any) => ({
        async then(resolve: any) {
          console.warn(`Mock delete from ${table} WHERE ${column} = ${value}`)
          resolve({ data: null, error: null })
        }
      })
    })
  }),
  rpc: async (functionName: string, params?: any) => {
    console.warn(`Mock RPC call: ${functionName}`, params)
    
    // Mock specific functions
    switch (functionName) {
      case 'user_has_permission':
        return { data: true, error: null }
      case 'get_user_roles':
        return { data: [{ role_name: 'admin', role_description: 'Mock Admin' }], error: null }
      default:
        return { data: null, error: null }
    }
  }
}

// Mock helper functions
export const authHelpers = {
  getCurrentUserProfile: async () => {
    console.warn('Mock getCurrentUserProfile')
    return {
      id: 'mock-user-id',
      username: 'mock-user',
      full_name: 'Mock User',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  },
  getUserRoles: async () => {
    console.warn('Mock getUserRoles')
    return [{ role_name: 'admin', role_description: 'Mock Admin Role' }]
  },
  hasPermission: async () => {
    console.warn('Mock hasPermission')
    return true
  },
  hasRole: async () => {
    console.warn('Mock hasRole')
    return true
  },
  updateProfile: async (updates: any) => {
    console.warn('Mock updateProfile:', updates)
    return null
  },
  updateLastLogin: async () => {
    console.warn('Mock updateLastLogin')
  },
  logActivity: async (action: string, ...args: any[]) => {
    console.warn('Mock logActivity:', action, args)
  }
}

export const adminHelpers = {
  getAllUsers: async () => {
    console.warn('Mock getAllUsers')
    return []
  },
  assignRole: async (userId: string, roleId: string) => {
    console.warn('Mock assignRole:', userId, roleId)
    return true
  },
  removeRole: async (userId: string, roleId: string) => {
    console.warn('Mock removeRole:', userId, roleId)
    return true
  },
  getAllRoles: async () => {
    console.warn('Mock getAllRoles')
    return []
  },
  getAllPermissions: async () => {
    console.warn('Mock getAllPermissions')
    return []
  },
  getAuditLogs: async () => {
    console.warn('Mock getAuditLogs')
    return []
  }
}

export default supabase
