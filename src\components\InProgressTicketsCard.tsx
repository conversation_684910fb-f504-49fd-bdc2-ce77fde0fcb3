"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Clock, MapPin, AlertCircle, Activity } from "lucide-react";
import { TroubleTicket } from "./TroubleTicketManagement";
import { initialSampleRoutes } from "@/data/networkRoutes"; // Updated: Import initialSampleRoutes from src/data/networkRoutes
import { differenceInSeconds } from "date-fns";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";

// Helper function to format duration
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

interface InProgressTicketsCardProps {
  tickets: TroubleTicket[];
  index?: number; // Index to display specific ticket
}

const InProgressTicketsCard: React.FC<InProgressTicketsCardProps> = ({ tickets, index = 0 }) => {
  const [inProgressTickets, setInProgressTickets] = React.useState<TroubleTicket[]>([]);
  const [currentTime, setCurrentTime] = React.useState(new Date());
  const navigate = useNavigate();

  React.useEffect(() => {
    // Filter tickets that are 'in-progress' or 'open'
    const filtered = tickets.filter(
      (ticket) => ticket.status === "in-progress" || ticket.status === "open"
    );
    // Get only the ticket at the specified index
    const selectedTicket = filtered[index] ? [filtered[index]] : [];
    setInProgressTickets(selectedTicket);

    // Set up interval to update current time every second for live duration
    const intervalId = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(intervalId);
  }, [tickets, index]);

  const handleTicketClick = (ticket: TroubleTicket) => {
    // Navigate to the TroubleTicketDetail page using the ticket's ID
    navigate(`/trouble-tickets/${ticket.id}`);
  };

  return (
    <Card className="col-span-1 lg:col-span-3 h-[500px] flex flex-col"> {/* Increased height from 400px to 500px */}
      <CardHeader className="flex-shrink-0">
        <CardTitle className="text-xl flex items-center">
          <div className="relative mr-2">
            <AlertCircle className="h-5 w-5 text-yellow-500" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          </div>
          In-Progress Trouble Tickets
          <Activity className="ml-2 h-4 w-4 text-green-500 animate-pulse" />
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto">
        {inProgressTickets.length > 0 ? (
          <div className="space-y-4">
            {inProgressTickets.map((ticket) => {
              const openedDate = new Date(ticket.openedAt);
              const elapsedSeconds = differenceInSeconds(currentTime, openedDate);
              const formattedDuration = formatDuration(elapsedSeconds);

              return (
                <div
                  key={ticket.id}
                  className="relative border rounded-lg p-4 shadow-sm bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-[1.02]"
                  onClick={() => handleTicketClick(ticket)}
                >
                  {/* Header with Activity Indicator */}
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-grow">
                      <h4 className="font-semibold text-base mb-1 flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                        {ticket.title} ({ticket.id})
                      </h4>
                      <div className="text-sm text-muted-foreground flex items-center">
                        <MapPin className="w-4 h-4 mr-1 text-blue-500" />
                        <span>Route: {ticket.routeName || "N/A"}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-green-600 dark:text-green-400 font-medium bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
                        ACTIVE
                      </div>
                    </div>
                  </div>

                  {/* Activity-Based Timeline Bar */}
                  <div className="mb-3">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-xs text-muted-foreground">Activity Progress</span>
                      <span className="text-xl font-bold text-orange-600 dark:text-orange-400">{formattedDuration}</span>
                    </div>
                    
                    {/* Calculate progress based on activities */}
                    {(() => {
                      const activities = ticket.activities || [];
                      const completedActivities = activities.filter(activity => 
                        activity.realDuration !== null && activity.realDuration !== undefined
                      );
                      const totalActivities = activities.length;
                      const progressPercentage = totalActivities > 0 ? (completedActivities.length / totalActivities) * 100 : 0;
                      const currentActivityIndex = completedActivities.length;
                      const currentActivity = activities[currentActivityIndex];
                      
                      return (
                        <>
                          {/* Activity Status */}
                          <div className="flex justify-between items-center mt-2">
                            <div className="text-xs text-muted-foreground">
                              <span className="font-medium text-green-600 dark:text-green-400">
                                {completedActivities.length}
                              </span>
                              <span className="mx-1">/</span>
                              <span>{totalActivities}</span>
                              <span className="ml-1">activities completed</span>
                            </div>
                            <div className="text-xs font-medium">
                              {currentActivity ? (
                                <span className="text-blue-600 dark:text-blue-400">
                                  Current: {currentActivity.name}
                                </span>
                              ) : (
                                <span className="text-green-600 dark:text-green-400">
                                  All activities completed
                                </span>
                              )}
                            </div>
                          </div>
                          
                          {/* Progress percentage */}
                          <div className="text-center mt-1">
                            <span className="text-xs font-bold text-blue-600 dark:text-blue-400">
                              {Math.round(progressPercentage)}% Complete
                            </span>
                          </div>

                          {/* Activity Boxes with Duration */}
                          <div className="grid grid-cols-3 gap-3 mt-4">
                            {activities.map((activity, index) => {
                              const isCompleted = activity.realDuration !== null && activity.realDuration !== undefined;
                              const isCurrent = index === currentActivityIndex;
                              
                              return (
                                <div 
                                  key={index}
                                  className={`p-3 rounded-md border shadow-sm ${isCompleted 
                                    ? 'bg-green-100 border-green-300 dark:bg-green-900/30 dark:border-green-700' 
                                    : isCurrent 
                                    ? 'bg-yellow-100 border-yellow-300 dark:bg-yellow-900/30 dark:border-yellow-700 animate-pulse' 
                                    : 'bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-700'}`}
                                >
                                  <div className="text-sm font-medium truncate mb-1">{activity.name}</div>
                                  <div className="flex justify-between items-center">
                                    <div className="text-xs font-medium bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full text-blue-700 dark:text-blue-300">
                                      Est: {activity.estimatedDuration} min
                                    </div>
                                    {isCompleted ? (
                                      <div className="text-xs font-medium bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full text-green-700 dark:text-green-300">
                                        Act: {activity.realDuration} min
                                      </div>
                                    ) : isCurrent ? (
                                      <div className="text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full text-yellow-700 dark:text-yellow-300 animate-pulse">
                                        In Progress
                                      </div>
                                    ) : (
                                      <div className="text-xs font-medium bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full text-gray-500 dark:text-gray-400">
                                        Pending
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </>
                      );
                    })()}
                  </div>

                  {/* Footer Info */}
                  <div className="flex justify-between items-center text-xs text-muted-foreground">
                    <div className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>Opened: {ticket.openedAt}</span>
                    </div>
                    <div className="flex items-center">
                      <Activity className="w-3 h-3 mr-1 text-green-500" />
                      <span className="text-green-600 dark:text-green-400 font-medium">Live</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <p className="text-center text-muted-foreground py-8">No trouble ticket assigned to this section.</p>
        )}
      </CardContent>
    </Card>
  );
};

export default InProgressTicketsCard;