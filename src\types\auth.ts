// Type definitions for user authentication and roles

// Type for User
export interface User {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  full_name?: string;
  avatar_url?: string;
  is_active: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
  roles?: Role[];
}

// Type for Role
export interface Role {
  id: number;
  name: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
  permissions?: Permission[];
}

// Type for Permission
export interface Permission {
  id: number;
  name: string;
  description?: string;
  resource: string;
  action: string;
  created_at: Date;
  updated_at: Date;
}

// Type for User Session
export interface UserSession {
  id: string;
  user_id: number;
  ip_address?: string;
  user_agent?: string;
  expires_at: Date;
  created_at: Date;
}

// Type for Password Reset Token
export interface PasswordResetToken {
  id: number;
  user_id: number;
  token: string;
  expires_at: Date;
  created_at: Date;
}

// Type for authentication
export interface AuthCredentials {
  username: string;
  email?: string; // Optional email field for Supabase compatibility
  password: string;
}

// Type for authentication response
export interface AuthResponse {
  user: Omit<User, 'password_hash'>;
  token: string;
  expires_at: Date;
}

// Type for authentication context
export interface AuthContext {
  user: Omit<User, 'password_hash'> | null;
  roles: Role[];
  permissions: string[];
  isAuthenticated: boolean;
  login: (credentials: AuthCredentials) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
}