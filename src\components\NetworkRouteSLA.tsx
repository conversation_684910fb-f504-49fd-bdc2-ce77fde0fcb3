"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Printer, PlusCircle } from "lucide-react";
import { showSuccess } from "@/utils/toast";
import { useNavigate } from "react-router-dom";
import { NetworkRoute, initialSampleRoutes, generateNewRouteId, generateLinkId, generateAssetId, formSchema } from "@/data/networkRoutes";
import IntegratedRouteCard from "./IntegratedRouteCard";
import RouteFormDialog from "./RouteFormDialog";
import * as z from "zod";

const NetworkRouteSLA: React.FC = () => {
  const [routes, setRoutes] = React.useState<NetworkRoute[]>(initialSampleRoutes);
  const [editingRoute, setEditingRoute] = React.useState<NetworkRoute | null>(null);
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [isCreatingNewRoute, setIsCreatingNewRoute] = React.useState(false);
  const navigate = useNavigate();

  const handleEditClick = (route: NetworkRoute) => {
    setEditingRoute(route);
    setIsCreatingNewRoute(false);
    setIsDialogOpen(true);
  };

  const handleCreateClick = () => {
    setEditingRoute(null);
    setIsCreatingNewRoute(true);
    setIsDialogOpen(true);
  };

  const handlePrintClick = () => {
    window.print();
  };

  const handleCardClick = (routeId: string) => {
    navigate(`/route/${routeId}`);
  };

  const handleFormSubmit = (values: z.infer<typeof formSchema>) => {
    if (isCreatingNewRoute) {
      const newRoute: NetworkRoute = {
        id: values.id,
        name: values.name,
        status: "operational", // Default status for new routes
        uptimePercentage: 100, // Default uptime for new routes
        lastChecked: new Date().toLocaleString(), // Current timestamp
        links: values.links.map(link => ({
          id: link.id || generateLinkId(), // Ensure ID is present
          name: link.name,
          distance: link.distance,
          totalLoss: link.totalLoss,
        })),
        assets: values.assets.map(asset => ({
          id: asset.id || generateAssetId(), // Ensure ID is present
          type: asset.type,
          count: asset.count,
        })),
        averageSLAHours: values.averageSLAHours,
        slaLast: values.averageSLAHours, // Initial SLA Last is same as average
      };
      setRoutes((prevRoutes) => [...prevRoutes, newRoute]);
      showSuccess("New route created successfully!");
    } else {
      setRoutes((prevRoutes) =>
        prevRoutes.map((route) =>
          route.id === values.id
            ? {
                ...route,
                name: values.name,
                links: values.links.map(link => ({
                  id: link.id!, // Assert ID is present for existing links
                  name: link.name,
                  distance: link.distance,
                  totalLoss: link.totalLoss,
                })),
                assets: values.assets.map(asset => ({
                  id: asset.id!, // Assert ID is present for existing assets
                  type: asset.type,
                  count: asset.count,
                })),
                averageSLAHours: values.averageSLAHours,
              }
            : route
        )
      );
      showSuccess("Route updated successfully!");
    }
    setIsDialogOpen(false);
    setEditingRoute(null);
    setIsCreatingNewRoute(false);
  };

  return (
    <>
      <Card className="col-span-1 lg:col-span-3">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl">Network Route SLA Monitoring</CardTitle>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleCreateClick}>
              <PlusCircle className="mr-2 h-4 w-4" /> Create New Route
            </Button>
            <Button variant="outline" size="sm" onClick={handlePrintClick}>
              <Printer className="mr-2 h-4 w-4" /> Print
            </Button>
          </div>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {routes.map((route) => (
            <IntegratedRouteCard
              key={route.id}
              route={route}
              onEdit={handleEditClick}
              onClick={handleCardClick}
            />
          ))}
        </CardContent>
      </Card>

      <RouteFormDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onSubmit={handleFormSubmit}
        initialData={editingRoute}
        isCreatingNewRoute={isCreatingNewRoute}
      />
    </>
  );
};

export default NetworkRouteSLA;