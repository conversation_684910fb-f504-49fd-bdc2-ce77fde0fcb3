import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
}

/**
 * Komponen untuk melindungi rute yang memerlukan autentikasi
 * Jika user tidak terautentikasi, redirect ke halaman login
 * Jika requiredPermission diberikan, cek apakah user memiliki permission tersebut
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredPermission 
}) => {
  const { isAuthenticated, checkPermission } = useAuth();
  const location = useLocation();

  // Jika user tidak terautentikasi, redirect ke halaman login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Jika requiredPermission diberikan, cek apakah user memiliki permission tersebut
  if (requiredPermission && !checkPermission(requiredPermission)) {
    // Redirect ke halaman unauthorized atau dashboard
    return <Navigate to="/unauthorized" replace />;
  }

  // Jika user terautentikasi dan memiliki permission yang diperlukan, tampilkan children
  return <>{children}</>;
};

export default ProtectedRoute;