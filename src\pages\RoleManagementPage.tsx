import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Edit, Plus, Search, Trash2 } from 'lucide-react';
import { Role, Permission } from '../types/auth';
import ProtectedRoute from '../components/ProtectedRoute';

// Sample data for roles
const sampleRoles: Role[] = [
  {
    id: 1,
    name: 'admin',
    description: 'Administrator with full system access',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 2,
    name: 'operator',
    description: 'System operator with access to operational features',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 3,
    name: 'viewer',
    description: 'User with view-only access to data',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 4,
    name: 'maintenance',
    description: 'Maintenance staff with access to maintenance features',
    created_at: new Date(),
    updated_at: new Date(),
  },
];

// Sample data for permissions
const samplePermissions: Permission[] = [
  {
    id: 1,
    name: 'view_dashboard',
    description: 'View dashboard',
    resource: 'dashboard',
    action: 'view',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 2,
    name: 'manage_users',
    description: 'Manage users',
    resource: 'users',
    action: 'manage',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 3,
    name: 'view_tickets',
    description: 'View tickets',
    resource: 'tickets',
    action: 'view',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 4,
    name: 'create_tickets',
    description: 'Create new tickets',
    resource: 'tickets',
    action: 'create',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 5,
    name: 'update_tickets',
    description: 'Update tickets',
    resource: 'tickets',
    action: 'update',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 6,
    name: 'delete_tickets',
    description: 'Delete tickets',
    resource: 'tickets',
    action: 'delete',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 7,
    name: 'view_maintenance',
    description: 'View maintenance schedule',
    resource: 'maintenance',
    action: 'view',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 8,
    name: 'schedule_maintenance',
    description: 'Menjadwalkan pemeliharaan',
    resource: 'maintenance',
    action: 'schedule',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 9,
    name: 'update_maintenance',
    description: 'Update maintenance schedule',
    resource: 'maintenance',
    action: 'update',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 10,
    name: 'view_reports',
    description: 'View reports',
    resource: 'reports',
    action: 'view',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 11,
    name: 'generate_reports',
    description: 'Generate reports',
    resource: 'reports',
    action: 'generate',
    created_at: new Date(),
    updated_at: new Date(),
  },
];

// Sample data for role and permission relations
const sampleRolePermissions: { roleId: number; permissionIds: number[] }[] = [
  {
    roleId: 1, // admin
    permissionIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], // all permissions
  },
  {
    roleId: 2, // operator
    permissionIds: [1, 3, 4, 5, 7, 8, 9, 10], // cannot manage users and delete tickets
  },
  {
    roleId: 3, // viewer
    permissionIds: [1, 3, 7, 10], // view only
  },
  {
    roleId: 4, // maintenance
    permissionIds: [1, 3, 7, 8, 9], // focus on maintenance
  },
];

const RoleManagementPage: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>(sampleRoles);
  const [permissions] = useState<Permission[]>(samplePermissions);
  const [rolePermissions, setRolePermissions] = useState(sampleRolePermissions);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState(false);
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [newRole, setNewRole] = useState({
    name: '',
    description: '',
    selectedPermissions: [] as number[],
  });
  const { toast } = useToast();

  // Filter roles based on search keywords
  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (role.description && role.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Function to get permissions based on role ID
  const getPermissionsByRoleId = (roleId: number): number[] => {
    const rolePermission = rolePermissions.find((rp) => rp.roleId === roleId);
    return rolePermission ? rolePermission.permissionIds : [];
  };

  // Function to handle changes in add role form
  const handleAddRoleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewRole({
      ...newRole,
      [name]: value,
    });
  };

  // Fungsi untuk menangani perubahan izin pada form tambah peran
  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    if (checked) {
      setNewRole({
        ...newRole,
        selectedPermissions: [...newRole.selectedPermissions, permissionId],
      });
    } else {
      setNewRole({
        ...newRole,
        selectedPermissions: newRole.selectedPermissions.filter((id) => id !== permissionId),
      });
    }
  };

  // Fungsi untuk menambahkan peran baru
  const handleAddRole = () => {
    // Validasi form
    if (!newRole.name) {
      toast({
        title: 'Validation Failed',
        description: 'Role name is required',
        variant: 'destructive',
      });
      return;
    }

    // Buat peran baru
    const newRoleData: Role = {
      id: roles.length + 1,
      name: newRole.name,
      description: newRole.description,
      created_at: new Date(),
      updated_at: new Date(),
    };

    // Tambahkan peran baru ke daftar peran
    setRoles([...roles, newRoleData]);

    // Tambahkan relasi peran dan izin
    if (newRole.selectedPermissions.length > 0) {
      setRolePermissions([
        ...rolePermissions,
        {
          roleId: newRoleData.id,
          permissionIds: newRole.selectedPermissions,
        },
      ]);
    }

    // Reset form dan tutup dialog
    setNewRole({
      name: '',
      description: '',
      selectedPermissions: [],
    });
    setIsAddRoleDialogOpen(false);

    toast({
      title: 'Role Added',
      description: `Role ${newRoleData.name} has been successfully added`,
    });
  };

  // Fungsi untuk mengedit peran
  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setNewRole({
      name: role.name,
      description: role.description || '',
      selectedPermissions: getPermissionsByRoleId(role.id),
    });
    setIsEditRoleDialogOpen(true);
  };

  // Fungsi untuk menyimpan perubahan peran
  const handleSaveRole = () => {
    if (!selectedRole) return;

    // Validasi form
    if (!newRole.name) {
      toast({
        title: 'Validation Failed',
        description: 'Role name is required',
        variant: 'destructive',
      });
      return;
    }

    // Update peran
    const updatedRole: Role = {
      ...selectedRole,
      name: newRole.name,
      description: newRole.description,
      updated_at: new Date(),
    };

    // Update daftar peran
    setRoles(roles.map((role) => (role.id === selectedRole.id ? updatedRole : role)));

    // Update relasi peran dan izin
    const rolePermissionIndex = rolePermissions.findIndex((rp) => rp.roleId === selectedRole.id);
    if (rolePermissionIndex !== -1) {
      const updatedRolePermissions = [...rolePermissions];
      updatedRolePermissions[rolePermissionIndex] = {
        roleId: selectedRole.id,
        permissionIds: newRole.selectedPermissions,
      };
      setRolePermissions(updatedRolePermissions);
    } else if (newRole.selectedPermissions.length > 0) {
      setRolePermissions([
        ...rolePermissions,
        {
          roleId: selectedRole.id,
          permissionIds: newRole.selectedPermissions,
        },
      ]);
    }

    // Reset form dan tutup dialog
    setSelectedRole(null);
    setNewRole({
      name: '',
      description: '',
      selectedPermissions: [],
    });
    setIsEditRoleDialogOpen(false);

    toast({
      title: 'Role Updated',
      description: `Role ${updatedRole.name} has been successfully updated`,
    });
  };

  // Fungsi untuk menghapus peran
  const handleDeleteRole = (roleId: number) => {
    // Konfirmasi penghapusan
    if (!window.confirm('Are you sure you want to delete this role?')) return;

    // Remove role from list
    setRoles(roles.filter((role) => role.id !== roleId));

    // Hapus relasi peran dan izin
    setRolePermissions(rolePermissions.filter((rp) => rp.roleId !== roleId));

    toast({
      title: 'Role Deleted',
      description: 'Role has been successfully removed from the system',
    });
  };

  // Fungsi untuk mendapatkan nama izin berdasarkan ID
  const getPermissionNameById = (permissionId: number): string => {
    const permission = permissions.find((p) => p.id === permissionId);
    return permission ? permission.name : '';
  };

  // Fungsi untuk mengelompokkan izin berdasarkan resource
  const groupPermissionsByResource = () => {
    const grouped: { [key: string]: Permission[] } = {};
    permissions.forEach((permission) => {
      if (!grouped[permission.resource]) {
        grouped[permission.resource] = [];
      }
      grouped[permission.resource].push(permission);
    });
    return grouped;
  };

  const groupedPermissions = groupPermissionsByResource();

  return (
    <ProtectedRoute requiredPermission="manage_users">
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Role Management</h1>
          <Button onClick={() => setIsAddRoleDialogOpen(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Role
          </Button>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Role List</CardTitle>
            <CardDescription>Manage roles and permissions in the system</CardDescription>
            <div className="mt-4 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search roles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Role Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead>Created Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRoles.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                      No roles found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRoles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell className="font-medium">{role.name}</TableCell>
                      <TableCell>{role.description || '-'}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1 max-w-md">
                          {getPermissionsByRoleId(role.id).map((permissionId) => (
                            <Badge key={permissionId} variant="outline">
                              {getPermissionNameById(permissionId)}
                            </Badge>
                          ))}
                          {getPermissionsByRoleId(role.id).length === 0 && '-'}
                        </div>
                      </TableCell>
                      <TableCell>{new Date(role.created_at).toLocaleDateString()}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditRole(role)}
                            title="Edit Role"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteRole(role.id)}
                            title="Delete Role"
                            disabled={role.name === 'admin'} // Cannot delete admin role
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Dialog Tambah Peran */}
        <Dialog open={isAddRoleDialogOpen} onOpenChange={setIsAddRoleDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Role</DialogTitle>
              <DialogDescription>
                Fill out the form below to add a new role to the system
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Role Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={newRole.name}
                  onChange={handleAddRoleChange}
                  placeholder="Enter role name"
                />
              </div>
              <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={newRole.description}
                    onChange={handleAddRoleChange}
                    placeholder="Enter role description"
                    rows={3}
                  />
              </div>
              <div className="space-y-2">
                  <Label>Permissions</Label>
                  <div className="border rounded-md p-4 space-y-4 max-h-60 overflow-y-auto">
                  {Object.entries(groupedPermissions).map(([resource, perms]) => (
                    <div key={resource} className="space-y-2">
                      <h4 className="text-sm font-semibold capitalize">{resource}</h4>
                      <div className="grid grid-cols-1 gap-2">
                        {perms.map((permission) => (
                          <Label key={permission.id} className="flex items-center gap-2 text-sm">
                            <Checkbox
                              checked={newRole.selectedPermissions.includes(permission.id)}
                              onCheckedChange={(checked) =>
                                handlePermissionChange(permission.id, checked as boolean)
                              }
                            />
                            <span>{permission.description}</span>
                          </Label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddRoleDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddRole}>Add Role</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog Edit Peran */}
        <Dialog open={isEditRoleDialogOpen} onOpenChange={setIsEditRoleDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Role</DialogTitle>
              <DialogDescription>Edit role information and permissions in the system</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                  <Label htmlFor="edit-name">Role Name *</Label>
                  <Input
                    id="edit-name"
                    name="name"
                    value={newRole.name}
                    onChange={handleAddRoleChange}
                    placeholder="Enter role name"
                    disabled={selectedRole?.name === 'admin'} // Cannot change admin role name
                  />
              </div>
              <div className="space-y-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    name="description"
                    value={newRole.description}
                    onChange={handleAddRoleChange}
                    placeholder="Enter role description"
                    rows={3}
                  />
              </div>
              <div className="space-y-2">
                  <Label>Permissions</Label>
                  <div className="border rounded-md p-4 space-y-4 max-h-60 overflow-y-auto">
                  {Object.entries(groupedPermissions).map(([resource, perms]) => (
                    <div key={resource} className="space-y-2">
                      <h4 className="text-sm font-semibold capitalize">{resource}</h4>
                      <div className="grid grid-cols-1 gap-2">
                        {perms.map((permission) => (
                          <Label key={permission.id} className="flex items-center gap-2 text-sm">
                            <Checkbox
                              checked={newRole.selectedPermissions.includes(permission.id)}
                              onCheckedChange={(checked) =>
                                handlePermissionChange(permission.id, checked as boolean)
                              }
                              disabled={selectedRole?.name === 'admin'} // Admin selalu memiliki semua izin
                            />
                            <span>{permission.description}</span>
                          </Label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditRoleDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveRole}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </ProtectedRoute>
  );
};

export default RoleManagementPage;