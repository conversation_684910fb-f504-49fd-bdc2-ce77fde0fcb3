"use client";

import React, { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Edit, Check, X, Plus, Trash2, ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { usePortData, PortData } from "@/contexts/PortDataContext";

const PortManagementPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Use shared port data from context
  const { table1Data, table2Data, setTable1Data, setTable2Data, addNewRouteData } = usePortData();

  // Calculate active connections between table A and B
  const activeConnections = useMemo(() => {
    const activePortsTable1 = table1Data.filter(traffic => 
      !traffic.trafficName.toLowerCase().includes('idle')
    );
    const activePortsTable2 = table2Data.filter(traffic => 
      !traffic.trafficName.toLowerCase().includes('idle')
    );
    
    if (activePortsTable1.length > 0 && activePortsTable2.length > 0) {
      return Math.min(activePortsTable1.length, activePortsTable2.length);
    }
    
    return 0;
  }, [table1Data, table2Data]);

  // State for add new port form
  const [showAddForm, setShowAddForm] = useState<{ table1: boolean; table2: boolean }>({
    table1: false,
    table2: false
  });
  
  const [newPortData, setNewPortData] = useState<PortData>({
    id: 0,
    noPort: "",
    trafficName: "",
    route: "",
    odcName: "",
    otdrDistance: "",
    groundDistance: "",
    totalLoss: "",
    rsl: ""
  });

  // State for edit port
  const [editingPort, setEditingPort] = useState<{ table: 'table1' | 'table2' | null; id: number | null }>({
    table: null,
    id: null
  });
  
  const [editPortData, setEditPortData] = useState<PortData>({
    id: 0,
    noPort: "",
    trafficName: "",
    route: "",
    odcName: "",
    otdrDistance: "",
    groundDistance: "",
    totalLoss: "",
    rsl: ""
  });

  // Function to synchronize routes between tables
  const synchronizeRoutes = (newRoute: string) => {
    // Update all items in table1Data to use the new route
    const updatedTable1Data = table1Data.map(item => ({
      ...item,
      route: newRoute
    }));
    
    // Update all items in table2Data to use the new route
    const updatedTable2Data = table2Data.map(item => ({
      ...item,
      route: newRoute
    }));
    
    setTable1Data(updatedTable1Data);
    setTable2Data(updatedTable2Data);
  };

  // CRUD functions for Port Management
  
  // Function to add new port
  const handleAddPort = (tableType: 'table1' | 'table2') => {
    const newId = tableType === 'table1' 
      ? Math.max(...table1Data.map(p => p.id), 0) + 1
      : Math.max(...table2Data.map(p => p.id), 0) + 1;
    
    const newPort = { ...newPortData, id: newId };
    
    // Check if this is a completely new route (different from current route in both tables)
    const currentRouteTable1 = table1Data.length > 0 ? table1Data[0].route : '';
    const currentRouteTable2 = table2Data.length > 0 ? table2Data[0].route : '';
    const isNewRoute = newPort.route && newPort.route.trim() !== '' && 
                      newPort.route !== currentRouteTable1 && 
                      newPort.route !== currentRouteTable2;
    
    if (isNewRoute) {
      // This is a completely new route, create new tables with this route
      addNewRouteData(newPort.route);
    } else {
      // Regular port addition to existing route
      if (newPort.route && newPort.route.trim() !== '' && currentRouteTable1 && currentRouteTable1 !== newPort.route) {
        synchronizeRoutes(newPort.route);
      }
      
      if (tableType === 'table1') {
        const updatedData = [...table1Data, newPort];
        setTable1Data(updatedData);
      } else {
        const updatedData = [...table2Data, newPort];
        setTable2Data(updatedData);
      }
    }
    
    // Reset form
    setNewPortData({
      id: 0,
      noPort: "",
      trafficName: "",
      route: "",
      odcName: "",
      otdrDistance: "",
      groundDistance: "",
      totalLoss: "",
      rsl: ""
    });
    
    setShowAddForm({ ...showAddForm, [tableType]: false });
  };
  
  // Function to delete port
  const handleDeletePort = (tableType: 'table1' | 'table2', portId: number) => {
    if (tableType === 'table1') {
      const updatedData = table1Data.filter(port => port.id !== portId);
      setTable1Data(updatedData);
    } else {
      const updatedData = table2Data.filter(port => port.id !== portId);
      setTable2Data(updatedData);
    }
  };
  
  // Function to start editing port
  const handleStartEditPort = (tableType: 'table1' | 'table2', port: PortData) => {
    setEditingPort({ table: tableType, id: port.id });
    setEditPortData({ ...port });
  };
  
  // Function to save port edit
  const handleSaveEditPort = () => {
    if (editingPort.table === 'table1') {
      const originalPort = table1Data.find(port => port.id === editingPort.id);
      const updatedData = table1Data.map(port => 
        port.id === editingPort.id ? editPortData : port
      );
      setTable1Data(updatedData);
      
      // If route was changed, synchronize with table2
      if (originalPort && originalPort.route !== editPortData.route) {
        synchronizeRoutes(editPortData.route);
      }
    } else if (editingPort.table === 'table2') {
      const originalPort = table2Data.find(port => port.id === editingPort.id);
      const updatedData = table2Data.map(port => 
        port.id === editingPort.id ? editPortData : port
      );
      setTable2Data(updatedData);
      
      // If route was changed, synchronize with table1
      if (originalPort && originalPort.route !== editPortData.route) {
        synchronizeRoutes(editPortData.route);
      }
    }
    
    setEditingPort({ table: null, id: null });
  };
  
  // Function to cancel port edit
  const handleCancelEditPort = () => {
    setEditingPort({ table: null, id: null });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header dengan tombol kembali */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => navigate('/core-management')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Core Management
          </Button>
          <h1 className="text-3xl font-bold">Port Management</h1>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Test New Route Button */}
          <Button
            onClick={() => {
              const newRouteNumber = Math.floor(Math.random() * 100) + 2;
              addNewRouteData(`Route-${String(newRouteNumber).padStart(2, '0')}`);
            }}
            className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
          >
            <Plus className="h-4 w-4" />
            Test Add New Route
          </Button>
          
          {/* Summary Card */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="text-center">
                <p className="text-sm font-medium text-blue-600">Active Connections</p>
                <p className="text-2xl font-bold text-blue-800">{activeConnections}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Port Management Section */}
      <div className="space-y-6">
        {/* Table 1 Management */}
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-lg font-semibold">Table A - Port Data</CardTitle>
            <Button
              onClick={() => setShowAddForm({ ...showAddForm, table1: true })}
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Port
            </Button>
          </CardHeader>
          <CardContent>
            {/* Add Form for Table 1 */}
            {showAddForm.table1 && (
              <div className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                <h4 className="font-medium mb-3">Add New Port</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Input
                    placeholder="No Port"
                    value={newPortData.noPort}
                    onChange={(e) => setNewPortData({ ...newPortData, noPort: e.target.value })}
                  />
                  <Input
                    placeholder="Traffic Name"
                    value={newPortData.trafficName}
                    onChange={(e) => setNewPortData({ ...newPortData, trafficName: e.target.value })}
                  />
                  <Input
                    placeholder="Route"
                    value={newPortData.route}
                    onChange={(e) => setNewPortData({ ...newPortData, route: e.target.value })}
                  />
                  <Input
                    placeholder="ODC Name"
                    value={newPortData.odcName}
                    onChange={(e) => setNewPortData({ ...newPortData, odcName: e.target.value })}
                  />
                  <Input
                    placeholder="OTDR Distance"
                    value={newPortData.otdrDistance}
                    onChange={(e) => setNewPortData({ ...newPortData, otdrDistance: e.target.value })}
                  />
                  <Input
                    placeholder="Ground Distance"
                    value={newPortData.groundDistance}
                    onChange={(e) => setNewPortData({ ...newPortData, groundDistance: e.target.value })}
                  />
                  <Input
                    placeholder="Total Loss"
                    value={newPortData.totalLoss}
                    onChange={(e) => setNewPortData({ ...newPortData, totalLoss: e.target.value })}
                  />
                  <Input
                    placeholder="RSL"
                    value={newPortData.rsl}
                    onChange={(e) => setNewPortData({ ...newPortData, rsl: e.target.value })}
                  />
                </div>
                <div className="flex gap-2 mt-3">
                  <Button size="sm" onClick={() => handleAddPort('table1')}>
                    <Check className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setShowAddForm({ ...showAddForm, table1: false })}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                </div>
              </div>
            )}
            
            {/* Table 1 Data */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border border-gray-300 px-3 py-2 text-left">No Port</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Traffic Name</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Route</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">ODC Name</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">OTDR Distance</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Ground Distance</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Total Loss</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">RSL</th>
                    <th className="border border-gray-300 px-3 py-2 text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {table1Data.map((port) => (
                    <tr key={port.id}>
                      {editingPort.table === 'table1' && editingPort.id === port.id ? (
                        // Edit mode
                        <>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.noPort}
                              onChange={(e) => setEditPortData({ ...editPortData, noPort: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.trafficName}
                              onChange={(e) => setEditPortData({ ...editPortData, trafficName: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.route}
                              onChange={(e) => setEditPortData({ ...editPortData, route: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.odcName}
                              onChange={(e) => setEditPortData({ ...editPortData, odcName: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.otdrDistance}
                              onChange={(e) => setEditPortData({ ...editPortData, otdrDistance: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.groundDistance}
                              onChange={(e) => setEditPortData({ ...editPortData, groundDistance: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.totalLoss}
                              onChange={(e) => setEditPortData({ ...editPortData, totalLoss: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.rsl}
                              onChange={(e) => setEditPortData({ ...editPortData, rsl: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <div className="flex gap-1 justify-center">
                              <Button size="sm" onClick={handleSaveEditPort}>
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline" onClick={handleCancelEditPort}>
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </>
                      ) : (
                        // View mode
                        <>
                          <td className="border border-gray-300 px-3 py-2">{port.noPort}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.trafficName}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.route}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.odcName}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.otdrDistance}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.groundDistance}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.totalLoss}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.rsl}</td>
                          <td className="border border-gray-300 px-3 py-2">
                            <div className="flex gap-1 justify-center">
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => handleStartEditPort('table1', port)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="sm" 
                                variant="destructive"
                                onClick={() => handleDeletePort('table1', port.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Table 2 Management */}
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-lg font-semibold">Table B - Port Data</CardTitle>
            <Button
              onClick={() => setShowAddForm({ ...showAddForm, table2: true })}
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Port
            </Button>
          </CardHeader>
          <CardContent>
            {/* Add Form for Table 2 */}
            {showAddForm.table2 && (
              <div className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                <h4 className="font-medium mb-3">Add New Port</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Input
                    placeholder="No Port"
                    value={newPortData.noPort}
                    onChange={(e) => setNewPortData({ ...newPortData, noPort: e.target.value })}
                  />
                  <Input
                    placeholder="Traffic Name"
                    value={newPortData.trafficName}
                    onChange={(e) => setNewPortData({ ...newPortData, trafficName: e.target.value })}
                  />
                  <Input
                    placeholder="Route"
                    value={newPortData.route}
                    onChange={(e) => setNewPortData({ ...newPortData, route: e.target.value })}
                  />
                  <Input
                    placeholder="ODC Name"
                    value={newPortData.odcName}
                    onChange={(e) => setNewPortData({ ...newPortData, odcName: e.target.value })}
                  />
                  <Input
                    placeholder="OTDR Distance"
                    value={newPortData.otdrDistance}
                    onChange={(e) => setNewPortData({ ...newPortData, otdrDistance: e.target.value })}
                  />
                  <Input
                    placeholder="Ground Distance"
                    value={newPortData.groundDistance}
                    onChange={(e) => setNewPortData({ ...newPortData, groundDistance: e.target.value })}
                  />
                  <Input
                    placeholder="Total Loss"
                    value={newPortData.totalLoss}
                    onChange={(e) => setNewPortData({ ...newPortData, totalLoss: e.target.value })}
                  />
                  <Input
                    placeholder="RSL"
                    value={newPortData.rsl}
                    onChange={(e) => setNewPortData({ ...newPortData, rsl: e.target.value })}
                  />
                </div>
                <div className="flex gap-2 mt-3">
                  <Button size="sm" onClick={() => handleAddPort('table2')}>
                    <Check className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setShowAddForm({ ...showAddForm, table2: false })}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                </div>
              </div>
            )}
            
            {/* Table 2 Data */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border border-gray-300 px-3 py-2 text-left">No Port</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Traffic Name</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Route</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">ODC Name</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">OTDR Distance</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Ground Distance</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">Total Loss</th>
                    <th className="border border-gray-300 px-3 py-2 text-left">RSL</th>
                    <th className="border border-gray-300 px-3 py-2 text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {table2Data.map((port) => (
                    <tr key={port.id}>
                      {editingPort.table === 'table2' && editingPort.id === port.id ? (
                        // Edit mode
                        <>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.noPort}
                              onChange={(e) => setEditPortData({ ...editPortData, noPort: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.trafficName}
                              onChange={(e) => setEditPortData({ ...editPortData, trafficName: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.route}
                              onChange={(e) => setEditPortData({ ...editPortData, route: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.odcName}
                              onChange={(e) => setEditPortData({ ...editPortData, odcName: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.otdrDistance}
                              onChange={(e) => setEditPortData({ ...editPortData, otdrDistance: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.groundDistance}
                              onChange={(e) => setEditPortData({ ...editPortData, groundDistance: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.totalLoss}
                              onChange={(e) => setEditPortData({ ...editPortData, totalLoss: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              value={editPortData.rsl}
                              onChange={(e) => setEditPortData({ ...editPortData, rsl: e.target.value })}
                              className="w-full"
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <div className="flex gap-1 justify-center">
                              <Button size="sm" onClick={handleSaveEditPort}>
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline" onClick={handleCancelEditPort}>
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </>
                      ) : (
                        // View mode
                        <>
                          <td className="border border-gray-300 px-3 py-2">{port.noPort}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.trafficName}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.route}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.odcName}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.otdrDistance}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.groundDistance}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.totalLoss}</td>
                          <td className="border border-gray-300 px-3 py-2">{port.rsl}</td>
                          <td className="border border-gray-300 px-3 py-2">
                            <div className="flex gap-1 justify-center">
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => handleStartEditPort('table2', port)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="sm" 
                                variant="destructive"
                                onClick={() => handleDeletePort('table2', port.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PortManagementPage;