import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Edit, Plus, Search, Trash2, UserPlus } from 'lucide-react';
import { User, Role } from '../types/auth';
import ProtectedRoute from '../components/ProtectedRoute';

// Sample data for users
const sampleUsers: Omit<User, 'password_hash'>[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: 'Administrator',
    is_active: true,
    created_at: new Date('2023-01-01'),
    updated_at: new Date('2023-01-01'),
    roles: [{ id: 1, name: 'admin', created_at: new Date(), updated_at: new Date() }],
  },
  {
    id: 2,
    username: 'operator',
    email: '<EMAIL>',
    full_name: 'System Operator',
    is_active: true,
    created_at: new Date('2023-01-15'),
    updated_at: new Date('2023-01-15'),
    roles: [{ id: 2, name: 'operator', created_at: new Date(), updated_at: new Date() }],
  },
  {
    id: 3,
    username: 'viewer',
    email: '<EMAIL>',
    full_name: 'Data Viewer',
    is_active: false,
    created_at: new Date('2023-02-01'),
    updated_at: new Date('2023-03-15'),
    roles: [{ id: 3, name: 'viewer', created_at: new Date(), updated_at: new Date() }],
  },
];

// Sample data for roles
const sampleRoles: Role[] = [
  {
    id: 1,
    name: 'admin',
    description: 'Administrator with full system access',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 2,
    name: 'operator',
    description: 'System operator with access to operational features',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 3,
    name: 'viewer',
    description: 'User with view-only access to data',
    created_at: new Date(),
    updated_at: new Date(),
  },
  {
    id: 4,
    name: 'maintenance',
    description: 'Maintenance staff with access to maintenance features',
    created_at: new Date(),
    updated_at: new Date(),
  },
];

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<Omit<User, 'password_hash'>[]>(sampleUsers);
  const [roles] = useState<Role[]>(sampleRoles);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<Omit<User, 'password_hash'> | null>(null);
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    full_name: '',
    password: '',
    confirm_password: '',
    is_active: true,
    selectedRoles: [] as number[],
  });
  const { toast } = useToast();

  // Filter users based on search keywords
  const filteredUsers = users.filter(
    (user) =>
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.full_name && user.full_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Function to handle changes in add user form
  const handleAddUserChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setNewUser({
      ...newUser,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Function to handle role changes in add user form
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setNewUser({
        ...newUser,
        selectedRoles: [...newUser.selectedRoles, roleId],
      });
    } else {
      setNewUser({
        ...newUser,
        selectedRoles: newUser.selectedRoles.filter((id) => id !== roleId),
      });
    }
  };

  // Function to add new user
  const handleAddUser = () => {
    // Form validation
    if (!newUser.username || !newUser.email || !newUser.password) {
      toast({
        title: 'Validation Failed',
        description: 'Username, email, and password are required',
        variant: 'destructive',
      });
      return;
    }

    if (newUser.password !== newUser.confirm_password) {
      toast({
        title: 'Validation Failed',
        description: 'Password and confirmation password do not match',
        variant: 'destructive',
      });
      return;
    }

    // Create new user
    const userRoles = roles.filter((role) => newUser.selectedRoles.includes(role.id));
    const newUserData: Omit<User, 'password_hash'> = {
      id: users.length + 1,
      username: newUser.username,
      email: newUser.email,
      full_name: newUser.full_name,
      is_active: newUser.is_active,
      created_at: new Date(),
      updated_at: new Date(),
      roles: userRoles,
    };

    // Add new user to user list
    setUsers([...users, newUserData]);

    // Reset form and close dialog
    setNewUser({
      username: '',
      email: '',
      full_name: '',
      password: '',
      confirm_password: '',
      is_active: true,
      selectedRoles: [],
    });
    setIsAddUserDialogOpen(false);

    toast({
      title: 'User Added',
      description: `User ${newUserData.username} has been successfully added`,
    });
  };

  // Function to edit user
  const handleEditUser = (user: Omit<User, 'password_hash'>) => {
    setSelectedUser(user);
    setNewUser({
      username: user.username,
      email: user.email,
      full_name: user.full_name || '',
      password: '',
      confirm_password: '',
      is_active: user.is_active,
      selectedRoles: user.roles?.map((role) => role.id) || [],
    });
    setIsEditUserDialogOpen(true);
  };

  // Function to save user changes
  const handleSaveUser = () => {
    if (!selectedUser) return;

    // Form validation
    if (!newUser.username || !newUser.email) {
      toast({
        title: 'Validation Failed',
        description: 'Username and email are required',
        variant: 'destructive',
      });
      return;
    }

    if (newUser.password && newUser.password !== newUser.confirm_password) {
      toast({
        title: 'Validation Failed',
        description: 'Password and confirmation password do not match',
        variant: 'destructive',
      });
      return;
    }

    // Update user
    const userRoles = roles.filter((role) => newUser.selectedRoles.includes(role.id));
    const updatedUser: Omit<User, 'password_hash'> = {
      ...selectedUser,
      username: newUser.username,
      email: newUser.email,
      full_name: newUser.full_name,
      is_active: newUser.is_active,
      updated_at: new Date(),
      roles: userRoles,
    };

    // Update user list
    setUsers(users.map((user) => (user.id === selectedUser.id ? updatedUser : user)));

    // Reset form and close dialog
    setSelectedUser(null);
    setNewUser({
      username: '',
      email: '',
      full_name: '',
      password: '',
      confirm_password: '',
      is_active: true,
      selectedRoles: [],
    });
    setIsEditUserDialogOpen(false);

    toast({
      title: 'User Updated',
      description: `User ${updatedUser.username} has been successfully updated`,
    });
  };

  // Function to delete user
  const handleDeleteUser = (userId: number) => {
    // Confirm deletion
    if (!window.confirm('Are you sure you want to delete this user?')) return;

    // Remove user from list
    setUsers(users.filter((user) => user.id !== userId));

    toast({
      title: 'User Deleted',
      description: 'User has been successfully removed from the system',
    });
  };

  return (
    <ProtectedRoute requiredPermission="manage_users">
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">User Management</h1>
          <Button onClick={() => setIsAddUserDialogOpen(true)} className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Add User
          </Button>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>User List</CardTitle>
            <CardDescription>Manage users and their roles in the system</CardDescription>
            <div className="mt-4 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Username</TableHead>
                  <TableHead>Full Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Roles</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4 text-gray-500">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.username}</TableCell>
                      <TableCell>{user.full_name || '-'}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.roles?.map((role) => (
                            <Badge key={role.id} variant="outline">
                              {role.name}
                            </Badge>
                          ))}
                          {!user.roles?.length && '-'}
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.is_active ? (
                          <Badge variant="success">Active</Badge>
                        ) : (
                          <Badge variant="destructive">Inactive</Badge>
                        )}
                      </TableCell>
                      <TableCell>{new Date(user.created_at).toLocaleDateString()}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditUser(user)}
                            title="Edit User"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteUser(user.id)}
                            title="Delete User"
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Add User Dialog */}
        <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
              <DialogDescription>
                Fill out the following form to add a new user to the system
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username *</Label>
                <Input
                  id="username"
                  name="username"
                  value={newUser.username}
                  onChange={handleAddUserChange}
                  placeholder="Enter username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={newUser.email}
                  onChange={handleAddUserChange}
                  placeholder="Enter email"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  name="full_name"
                  value={newUser.full_name}
                  onChange={handleAddUserChange}
                  placeholder="Enter full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password *</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={newUser.password}
                  onChange={handleAddUserChange}
                  placeholder="Enter password"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm_password">Confirm Password *</Label>
                <Input
                  id="confirm_password"
                  name="confirm_password"
                  type="password"
                  value={newUser.confirm_password}
                  onChange={handleAddUserChange}
                  placeholder="Confirm password"
                />
              </div>
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Checkbox
                    checked={newUser.is_active}
                    onCheckedChange={(checked) =>
                      setNewUser({ ...newUser, is_active: checked as boolean })
                    }
                  />
                  <span>Active</span>
                </Label>
              </div>
              <div className="space-y-2">
                <Label>Roles</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {roles.map((role) => (
                    <Label key={role.id} className="flex items-center gap-2 text-sm">
                      <Checkbox
                        checked={newUser.selectedRoles.includes(role.id)}
                        onCheckedChange={(checked) =>
                          handleRoleChange(role.id, checked as boolean)
                        }
                      />
                      <span>{role.name}</span>
                    </Label>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddUserDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddUser}>Add User</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit User Dialog */}
        <Dialog open={isEditUserDialogOpen} onOpenChange={setIsEditUserDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Edit User</DialogTitle>
              <DialogDescription>
                Edit user information and their roles in the system
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-username">Username *</Label>
                <Input
                  id="edit-username"
                  name="username"
                  value={newUser.username}
                  onChange={handleAddUserChange}
                  placeholder="Enter username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-email">Email *</Label>
                <Input
                  id="edit-email"
                  name="email"
                  type="email"
                  value={newUser.email}
                  onChange={handleAddUserChange}
                  placeholder="Enter email"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-full_name">Full Name</Label>
                <Input
                  id="edit-full_name"
                  name="full_name"
                  value={newUser.full_name}
                  onChange={handleAddUserChange}
                  placeholder="Enter full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-password">Password (leave blank if not changing)</Label>
                <Input
                  id="edit-password"
                  name="password"
                  type="password"
                  value={newUser.password}
                  onChange={handleAddUserChange}
                  placeholder="Enter new password"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-confirm_password">Confirm Password</Label>
                <Input
                  id="edit-confirm_password"
                  name="confirm_password"
                  type="password"
                  value={newUser.confirm_password}
                  onChange={handleAddUserChange}
                  placeholder="Confirm new password"
                />
              </div>
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Checkbox
                    checked={newUser.is_active}
                    onCheckedChange={(checked) =>
                      setNewUser({ ...newUser, is_active: checked as boolean })
                    }
                  />
                  <span>Active</span>
                </Label>
              </div>
              <div className="space-y-2">
                <Label>Roles</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {roles.map((role) => (
                    <Label key={role.id} className="flex items-center gap-2 text-sm">
                      <Checkbox
                        checked={newUser.selectedRoles.includes(role.id)}
                        onCheckedChange={(checked) =>
                          handleRoleChange(role.id, checked as boolean)
                        }
                      />
                      <span>{role.name}</span>
                    </Label>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditUserDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveUser}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </ProtectedRoute>
  );
};

export default UserManagementPage;