import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full text-center">
        <div className="flex justify-center mb-4">
          <AlertTriangle className="h-16 w-16 text-yellow-500" />
        </div>
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Sorry, you do not have the required permissions to access this page.
        </p>
        <div className="flex flex-col space-y-2">
          <Button onClick={() => navigate('/dashboard')} variant="default">
            Back to Dashboard
          </Button>
          <Button onClick={() => navigate(-1)} variant="outline">
            Back to Previous Page
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UnauthorizedPage;