import React, { createContext, useContext, useState, useEffect } from 'react';
import { AuthContext as AuthContextType, AuthCredentials, AuthResponse, User, Role } from '../types/auth';

// Default values for authentication context
const defaultAuthContext: AuthContextType = {
  user: null,
  roles: [],
  permissions: [],
  isAuthenticated: false,
  login: async () => { throw new Error('AuthContext not initialized'); },
  logout: async () => { throw new Error('AuthContext not initialized'); },
  checkPermission: () => false,
};

// Create authentication context
const AuthContext = createContext<AuthContextType>(defaultAuthContext);

// Hook to use authentication context
export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: React.ReactNode;
}

// Provider for authentication context
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<Omit<User, 'password_hash'> | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Check if user is already logged in when app loads
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Check token in localStorage
        const token = localStorage.getItem('auth_token');
        console.log('Auth check - token:', token);
        if (!token) {
          console.log('No auth token found');
          return;
        }

        // Implementation: Validate token and get user data
        // Contoh implementasi sederhana:
        const userData = JSON.parse(localStorage.getItem('auth_user') || '{}');
        const userRoles = JSON.parse(localStorage.getItem('auth_roles') || '[]');
        const userPermissions = JSON.parse(localStorage.getItem('auth_permissions') || '[]');

        console.log('Auth check - userData:', userData);
        console.log('Auth check - permissions:', userPermissions);

        if (userData && Object.keys(userData).length > 0) {
          setUser(userData);
          setRoles(userRoles);
          setPermissions(userPermissions);
          setIsAuthenticated(true);
          console.log('Auth restored successfully');
        } else {
          // Auto-login for development
          console.log('No existing auth, performing auto-login for development');
          const mockUser = {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            full_name: 'Admin User',
            is_active: true,
            created_at: new Date(),
            updated_at: new Date(),
          };

          const mockRoles = [
            {
              id: 1,
              name: 'admin',
              description: 'Administrator with full system access',
              created_at: new Date(),
              updated_at: new Date(),
            },
          ];

          const mockPermissions = [
            'view_dashboard',
            'manage_users',
            'view_tickets',
            'create_tickets',
            'update_tickets',
            'delete_tickets',
            'view_maintenance',
            'schedule_maintenance',
            'update_maintenance',
            'view_reports',
            'generate_reports',
          ];

          // Save authentication data
          localStorage.setItem('auth_token', 'auto_login_token');
          localStorage.setItem('auth_user', JSON.stringify(mockUser));
          localStorage.setItem('auth_roles', JSON.stringify(mockRoles));
          localStorage.setItem('auth_permissions', JSON.stringify(mockPermissions));

          // Update state
          setUser(mockUser);
          setRoles(mockRoles);
          setPermissions(mockPermissions);
          setIsAuthenticated(true);
          console.log('Auto-login completed');
        }
      } catch (error) {
        console.error('Failed to restore auth state:', error);
        // Remove authentication data if error occurs
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_roles');
        localStorage.removeItem('auth_permissions');
      }
    };

    checkAuthStatus();
  }, []);

  // Fungsi login
  const login = async (credentials: AuthCredentials): Promise<AuthResponse> => {
    try {
      // Implementation: Send request to API for login
      // Simple implementation example (replace with actual implementation):
      
      // Simulasi request API
      // In actual implementation, this would be a fetch to login endpoint
      const mockResponse: AuthResponse = {
        user: {
          id: 1,
          username: credentials.username,
          email: `${credentials.username}@example.com`,
          full_name: 'User Demo',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
        token: 'mock_jwt_token',
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 jam
      };

      // Simulate roles and permissions
      const mockRoles: Role[] = [
        {
          id: 1,
          name: 'admin',
          description: 'Administrator with full system access',
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      const mockPermissions = [
        'view_dashboard',
        'manage_users',
        'view_tickets',
        'create_tickets',
        'update_tickets',
        'delete_tickets',
        'view_maintenance',
        'schedule_maintenance',
        'update_maintenance',
        'view_reports',
        'generate_reports',
      ];

      // Save authentication data
      localStorage.setItem('auth_token', mockResponse.token);
      localStorage.setItem('auth_user', JSON.stringify(mockResponse.user));
      localStorage.setItem('auth_roles', JSON.stringify(mockRoles));
      localStorage.setItem('auth_permissions', JSON.stringify(mockPermissions));

      // Update state
      setUser(mockResponse.user);
      setRoles(mockRoles);
      setPermissions(mockPermissions);
      setIsAuthenticated(true);

      return mockResponse;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  // Fungsi logout
  const logout = async (): Promise<void> => {
    try {
      // Implementation: Send request to API for logout (if needed)
      
      // Remove authentication data
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      localStorage.removeItem('auth_roles');
      localStorage.removeItem('auth_permissions');

      // Reset state
      setUser(null);
      setRoles([]);
      setPermissions([]);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  };

  // Function to check if user has specific permission
  const checkPermission = (permission: string): boolean => {
    const hasPermission = permissions.includes(permission);
    console.log('checkPermission:', {
      permission,
      hasPermission,
      allPermissions: permissions
    });
    return hasPermission;
  };

  // Context value to be provided
  const contextValue: AuthContextType = {
    user,
    roles,
    permissions,
    isAuthenticated,
    login,
    logout,
    checkPermission,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;