import React, { createContext, useContext, useState, useEffect } from 'react';
import { AuthContext as AuthContextType, AuthCredentials, AuthResponse, User, Role } from '../types/auth';
import { supabase, authHelpers } from '../lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

// Default values for authentication context
const defaultAuthContext: AuthContextType = {
  user: null,
  roles: [],
  permissions: [],
  isAuthenticated: false,
  login: async () => { throw new Error('AuthContext not initialized'); },
  logout: async () => { throw new Error('AuthContext not initialized'); },
  checkPermission: () => false,
};

// Create authentication context
const AuthContext = createContext<AuthContextType>(defaultAuthContext);

// Hook to use authentication context
export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: React.ReactNode;
}

// Provider for authentication context
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<Omit<User, 'password_hash'> | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);

  // Check if user is already logged in when app loads
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setLoading(true);

        // Get current session from Supabase
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          return;
        }

        if (session?.user) {
          await loadUserData(session.user);
        }
      } catch (error) {
        console.error('Failed to restore auth state:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: any, session: any) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await loadUserData(session.user);
        } else if (event === 'SIGNED_OUT') {
          clearUserData();
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // Load user data from Supabase
  const loadUserData = async (supabaseUser: SupabaseUser) => {
    try {
      // Get user profile
      const profile = await authHelpers.getCurrentUserProfile();

      // Get user roles
      const userRoles = await authHelpers.getUserRoles();

      // Get user permissions
      const userPermissions: string[] = [];

      // Get all permissions for user's roles
      const { data: permissionsData } = await supabase
        .from('user_permissions_view')
        .select('permission_name')
        .eq('user_id', supabaseUser.id);

      if (permissionsData) {
        permissionsData.forEach(p => {
          if (p.permission_name && !userPermissions.includes(p.permission_name)) {
            userPermissions.push(p.permission_name);
          }
        });
      }

      // Convert to our User type
      const userData: Omit<User, 'password_hash'> = {
        id: parseInt(supabaseUser.id.replace(/-/g, '').substring(0, 8), 16), // Convert UUID to number for compatibility
        username: profile?.username || supabaseUser.email?.split('@')[0] || '',
        email: supabaseUser.email || '',
        full_name: profile?.full_name || '',
        avatar_url: profile?.avatar_url,
        is_active: profile?.is_active ?? true,
        last_login: profile?.last_login ? new Date(profile.last_login) : undefined,
        created_at: new Date(supabaseUser.created_at),
        updated_at: profile?.updated_at ? new Date(profile.updated_at) : new Date(),
      };

      // Convert roles to our Role type
      const rolesData: Role[] = userRoles.map((role: any) => ({
        id: parseInt(role.role_name?.replace(/\D/g, '') || '1'), // Simple conversion for compatibility
        name: role.role_name || '',
        description: role.role_description || '',
        created_at: new Date(),
        updated_at: new Date(),
      }));

      setUser(userData);
      setRoles(rolesData);
      setPermissions(userPermissions);
      setIsAuthenticated(true);

      // Update last login
      await authHelpers.updateLastLogin();

    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  // Clear user data
  const clearUserData = () => {
    setUser(null);
    setRoles([]);
    setPermissions([]);
    setIsAuthenticated(false);
  };

  // Fungsi login
  const login = async (credentials: AuthCredentials): Promise<AuthResponse> => {
    try {
      // Use Supabase auth for login
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email || credentials.username,
        password: credentials.password,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('Login failed: No user data received');
      }

      // Load user data will be handled by the auth state change listener
      // But we need to return a response for compatibility
      const profile = await authHelpers.getCurrentUserProfile();

      const response: AuthResponse = {
        user: {
          id: parseInt(data.user.id.replace(/-/g, '').substring(0, 8), 16),
          username: profile?.username || data.user.email?.split('@')[0] || '',
          email: data.user.email || '',
          full_name: profile?.full_name || '',
          avatar_url: profile?.avatar_url,
          is_active: profile?.is_active ?? true,
          last_login: profile?.last_login ? new Date(profile.last_login) : undefined,
          created_at: new Date(data.user.created_at),
          updated_at: profile?.updated_at ? new Date(profile.updated_at) : new Date(),
        },
        token: data.session?.access_token || '',
        expires_at: data.session?.expires_at ? new Date(data.session.expires_at * 1000) : new Date(),
      };

      // Log the login activity
      await authHelpers.logActivity('user_login', 'auth', data.user.id);

      return response;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  // Fungsi logout
  const logout = async (): Promise<void> => {
    try {
      // Log the logout activity before signing out
      await authHelpers.logActivity('user_logout', 'auth');

      // Use Supabase auth for logout
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Logout error:', error);
        // Continue with local cleanup even if server logout fails
      }

      // Clear user data (will also be handled by auth state change listener)
      clearUserData();
    } catch (error) {
      console.error('Logout failed:', error);
      // Force local cleanup even if there's an error
      clearUserData();
      throw error;
    }
  };

  // Function to check if user has specific permission
  const checkPermission = (permission: string): boolean => {
    return permissions.includes(permission);
  };

  // Context value to be provided
  const contextValue: AuthContextType = {
    user,
    roles,
    permissions,
    isAuthenticated,
    login,
    logout,
    checkPermission,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;