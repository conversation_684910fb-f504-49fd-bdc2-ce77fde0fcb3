"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { CalendarDays, <PERSON>ch, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface MaintenanceActivity {
  name: string;
  estimatedDuration: number; // in minutes
  realDuration: number | null;
  pic: string;
  result: string;
  notes: string;
  status?: "completed" | "in-progress" | "pending";
}

interface MaintenanceEvent {
  id: string;
  type: "patrol" | "repair" | "outage";
  description: string;
  date: string;
  status?: "completed" | "scheduled" | "in-progress";
  duration?: number; // in hours
  progress?: number; // percentage 0-100
  startTime?: string;
  endTime?: string;
  activities?: MaintenanceActivity[];
}

const sampleEvents: MaintenanceEvent[] = [
  { 
    id: "M-001", 
    type: "patrol", 
    description: "Routine fiber patrol - Sector A", 
    date: "2024-08-01", 
    status: "completed", 
    duration: 4, 
    progress: 100, 
    startTime: "08:00", 
    endTime: "12:00",
    activities: [
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 25, pic: "John Doe", result: "Team ready", notes: "Equipment checked", status: "completed" },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: 40, pic: "John Doe", result: "Arrived on site", notes: "Traffic was light", status: "completed" },
      { name: "Patrol Execution", estimatedDuration: 120, realDuration: 130, pic: "John Doe", result: "Patrol completed", notes: "No issues found", status: "completed" },
      { name: "Documentation", estimatedDuration: 45, realDuration: 45, pic: "John Doe", result: "Report submitted", notes: "All documentation complete", status: "completed" },
    ]
  },
  { 
    id: "M-002", 
    type: "repair", 
    description: "Scheduled repair - Link FR-003", 
    date: "2024-08-05", 
    status: "in-progress", 
    duration: 6, 
    progress: 65, 
    startTime: "09:00", 
    endTime: "15:00",
    activities: [
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 35, pic: "Jane Smith", result: "Team ready", notes: "Equipment checked", status: "completed" },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: 50, pic: "Jane Smith", result: "Arrived on site", notes: "Traffic was heavy", status: "completed" },
      { name: "Fault Diagnosis", estimatedDuration: 60, realDuration: 70, pic: "Jane Smith", result: "Fault identified", notes: "Fiber break at junction B", status: "completed" },
      { name: "Repair Execution", estimatedDuration: 120, realDuration: null, pic: "Jane Smith", result: "", notes: "Splicing in progress", status: "in-progress" },
      { name: "Testing", estimatedDuration: 45, realDuration: null, pic: "Jane Smith", result: "", notes: "", status: "pending" },
      { name: "Documentation", estimatedDuration: 60, realDuration: null, pic: "Jane Smith", result: "", notes: "", status: "pending" },
    ]
  },
  { 
    id: "M-003", 
    type: "outage", 
    description: "Emergency outage - Link FR-004 (Fiber Cut)", 
    date: "2024-08-06", 
    status: "completed", 
    duration: 8, 
    progress: 100, 
    startTime: "14:00", 
    endTime: "22:00",
    activities: [
      { name: "Emergency Response", estimatedDuration: 15, realDuration: 10, pic: "Team Alpha", result: "Response initiated", notes: "High priority", status: "completed" },
      { name: "Travel to Site", estimatedDuration: 30, realDuration: 25, pic: "Team Alpha", result: "Arrived on site", notes: "Emergency vehicle used", status: "completed" },
      { name: "Damage Assessment", estimatedDuration: 45, realDuration: 50, pic: "Team Alpha", result: "Assessment complete", notes: "Major fiber cut due to construction", status: "completed" },
      { name: "Repair Planning", estimatedDuration: 30, realDuration: 35, pic: "Team Alpha", result: "Plan approved", notes: "Required full replacement", status: "completed" },
      { name: "Repair Execution", estimatedDuration: 180, realDuration: 200, pic: "Team Alpha", result: "Repair completed", notes: "New fiber installed", status: "completed" },
      { name: "Testing", estimatedDuration: 60, realDuration: 45, pic: "Team Alpha", result: "Tests passed", notes: "Signal strength optimal", status: "completed" },
      { name: "Documentation", estimatedDuration: 60, realDuration: 55, pic: "Team Alpha", result: "Report submitted", notes: "Incident documented", status: "completed" },
    ]
  },
  { 
    id: "M-004", 
    type: "patrol", 
    description: "Routine fiber patrol - Sector B", 
    date: "2024-08-08", 
    status: "completed", 
    duration: 3, 
    progress: 100, 
    startTime: "07:00", 
    endTime: "10:00",
    activities: [
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 25, pic: "John Doe", result: "Team ready", notes: "Equipment checked", status: "completed" },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: 40, pic: "John Doe", result: "Arrived on site", notes: "Traffic was light", status: "completed" },
      { name: "Patrol Execution", estimatedDuration: 90, realDuration: 85, pic: "John Doe", result: "Patrol completed", notes: "No issues found", status: "completed" },
      { name: "Documentation", estimatedDuration: 30, realDuration: 30, pic: "John Doe", result: "Report submitted", notes: "All documentation complete", status: "completed" },
    ]
  },
  { 
    id: "M-005", 
    type: "repair", 
    description: "Scheduled upgrade - OLT-01", 
    date: "2024-08-12", 
    status: "scheduled", 
    duration: 12, 
    progress: 0, 
    startTime: "20:00", 
    endTime: "08:00",
    activities: [
      { name: "Pre-upgrade Testing", estimatedDuration: 60, realDuration: null, pic: "System Team", result: "", notes: "", status: "pending" },
      { name: "Backup Configuration", estimatedDuration: 30, realDuration: null, pic: "System Team", result: "", notes: "", status: "pending" },
      { name: "Software Upgrade", estimatedDuration: 120, realDuration: null, pic: "System Team", result: "", notes: "", status: "pending" },
      { name: "Hardware Upgrade", estimatedDuration: 180, realDuration: null, pic: "System Team", result: "", notes: "", status: "pending" },
      { name: "Post-upgrade Testing", estimatedDuration: 120, realDuration: null, pic: "System Team", result: "", notes: "", status: "pending" },
      { name: "Documentation", estimatedDuration: 60, realDuration: null, pic: "System Team", result: "", notes: "", status: "pending" },
    ]
  },
  { 
    id: "M-006", 
    type: "outage", 
    description: "Emergency outage - Power failure at Node X", 
    date: "2024-08-15", 
    status: "in-progress", 
    duration: 2, 
    progress: 30, 
    startTime: "16:00", 
    endTime: "18:00",
    activities: [
      { name: "Emergency Response", estimatedDuration: 15, realDuration: 10, pic: "Team Beta", result: "Response initiated", notes: "High priority", status: "completed" },
      { name: "Travel to Site", estimatedDuration: 30, realDuration: 25, pic: "Team Beta", result: "Arrived on site", notes: "Emergency vehicle used", status: "completed" },
      { name: "Power Assessment", estimatedDuration: 30, realDuration: null, pic: "Team Beta", result: "", notes: "Identifying power source issue", status: "in-progress" },
      { name: "Generator Setup", estimatedDuration: 45, realDuration: null, pic: "Team Beta", result: "", notes: "", status: "pending" },
      { name: "Power Restoration", estimatedDuration: 60, realDuration: null, pic: "Team Beta", result: "", notes: "", status: "pending" },
      { name: "System Testing", estimatedDuration: 30, realDuration: null, pic: "Team Beta", result: "", notes: "", status: "pending" },
      { name: "Documentation", estimatedDuration: 30, realDuration: null, pic: "Team Beta", result: "", notes: "", status: "pending" },
    ]
  },
];

const MaintenanceTimeline: React.FC = () => {
  const getEventTypeIcon = (type: MaintenanceEvent["type"]) => {
    switch (type) {
      case "patrol":
        return <CalendarDays className="w-5 h-5 text-green-500" />;
      case "repair":
        return <Wrench className="w-5 h-5 text-blue-500" />;
      case "outage":
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  const getEventColor = (type: MaintenanceEvent["type"]) => {
    switch (type) {
      case "patrol":
        return "border-green-500";
      case "repair":
        return "border-blue-500";
      case "outage":
        return "border-red-500";
      default:
        return "border-gray-500";
    }
  };

  const getProgressColor = (type: MaintenanceEvent["type"]) => {
    switch (type) {
      case "patrol":
        return "bg-green-500";
      case "repair":
        return "bg-blue-500";
      case "outage":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusBadgeColor = (status: MaintenanceEvent["status"]) => {
    switch (status) {
      case "completed":
        return "text-green-600 border-green-600";
      case "in-progress":
        return "text-yellow-600 border-yellow-600";
      case "scheduled":
        return "text-blue-600 border-blue-600";
      default:
        return "text-gray-600 border-gray-600";
    }
  };

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle className="text-xl">Maintenance Timeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6 max-h-96 overflow-y-auto pr-2">
          {sampleEvents.map((event, index) => (
            <div key={event.id} className="space-y-3">
              <div className="flex items-start space-x-4">
                <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center border-2 ${getEventColor(event.type)}`}>
                  {getEventTypeIcon(event.type)}
                </div>
                <div className="flex-grow">
                  <div className="flex justify-between items-center">
                    <p className="font-semibold">{event.description}</p>
                    <span className="text-sm text-muted-foreground">{event.date}</span>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    {event.status && (
                      <Badge variant="outline" className={getStatusBadgeColor(event.status)}>
                        {event.status.charAt(0).toUpperCase() + event.status.slice(1).replace('-', ' ')}
                      </Badge>
                    )}
                    {event.duration && (
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        <span>{event.duration}h</span>
                      </div>
                    )}
                    {event.startTime && event.endTime && (
                      <span className="text-sm text-muted-foreground">
                        {event.startTime} - {event.endTime}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Time Bar Progress */}
              {event.progress !== undefined && (
                <div className="ml-14 space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className="font-medium">{event.progress}%</span>
                  </div>
                  <div className="relative">
                    <Progress 
                      value={event.progress} 
                      className="h-2"
                    />
                    <div 
                      className={`absolute top-0 left-0 h-2 rounded-full transition-all duration-300 ${getProgressColor(event.type)}`}
                      style={{ width: `${event.progress}%` }}
                    />
                  </div>
                  {event.status === "in-progress" && (
                    <div className="text-xs text-muted-foreground">
                      Estimated completion: {event.endTime}
                    </div>
                  )}
                </div>
              )}
              
              {/* Stage Status Indicator */}
              {event.activities && event.activities.length > 0 && (
                <div className="ml-14 mt-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Stage Status</span>
                    <span className="text-xs text-muted-foreground">
                      {event.activities.filter(a => a.status === "completed").length} / {event.activities.length} completed
                    </span>
                  </div>
                  
                  {/* Stage Status Bar */}
                  <div className="mb-4 bg-gray-100 dark:bg-gray-800 rounded-lg p-3 shadow-inner">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium">Current Stage:</span>
                      <span className="text-xs font-bold">
                        {event.activities.find(a => a.status === "in-progress")?.name || 
                         (event.activities.every(a => a.status === "completed") ? "All Stages Completed" : 
                          (event.activities.some(a => a.status === "completed") ? "Waiting for Next Stage" : "Not Started"))}
                      </span>
                    </div>
                    
                    <div className="relative pt-1">
                      <div className="flex mb-2 items-center justify-between">
                        <div>
                          <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200">
                            {Math.round((event.activities.filter(a => a.status === "completed").length / event.activities.length) * 100)}%
                          </span>
                        </div>
                      </div>
                      <div className="flex h-2 mb-4 overflow-hidden text-xs bg-gray-200 rounded-full">
                        <div 
                          style={{ width: `${(event.activities.filter(a => a.status === "completed").length / event.activities.length) * 100}%` }} 
                          className="flex flex-col justify-center text-center text-white bg-green-500 shadow-none whitespace-nowrap transition-all duration-500"
                        ></div>
                      </div>
                      
                      {/* Stage Boxes */}
                      <div className="flex justify-between mb-1">
                        {event.activities.map((activity, idx) => {
                          const isCompleted = activity.status === "completed";
                          const isInProgress = activity.status === "in-progress";
                          const isPending = activity.status === "pending" || !activity.status;
                          
                          return (
                            <div 
                              key={idx} 
                              className={`flex-1 mx-1 text-center ${idx === 0 ? 'ml-0' : ''} ${idx === event.activities.length - 1 ? 'mr-0' : ''}`}
                            >
                              <div 
                                className={`h-8 flex items-center justify-center rounded-md text-xs font-medium ${isCompleted 
                                  ? 'bg-green-500 text-white' 
                                  : isInProgress 
                                  ? 'bg-yellow-500 text-white animate-pulse' 
                                  : 'bg-gray-300 text-gray-700 dark:bg-gray-700 dark:text-gray-300'}`}
                              >
                                Stage {idx + 1}
                              </div>
                              <div className="text-xs mt-1 truncate" title={activity.name}>{activity.name}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                  
                  {/* Activity List */}
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Activity List</span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {event.activities.map((activity, actIndex) => {
                      const isCompleted = activity.status === "completed";
                      const isInProgress = activity.status === "in-progress";
                      const isPending = activity.status === "pending" || !activity.status;
                      
                      return (
                        <div 
                          key={actIndex}
                          className={`p-3 rounded-md border shadow-sm ${isCompleted 
                            ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' 
                            : isInProgress 
                            ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800' 
                            : 'bg-gray-50 border-gray-200 dark:bg-gray-800/50 dark:border-gray-700'}`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div className="font-medium text-sm">{activity.name}</div>
                            <div className={`text-xs px-2 py-1 rounded-full ${isCompleted 
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-300' 
                              : isInProgress 
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-300 animate-pulse' 
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400'}`}>
                              {isCompleted ? 'Completed' : isInProgress ? 'In Progress' : 'Pending'}
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="text-muted-foreground">Est. Duration:</span>
                              <span className="ml-1 font-medium">{activity.estimatedDuration} min</span>
                            </div>
                            {activity.realDuration !== null && (
                              <div>
                                <span className="text-muted-foreground">Actual:</span>
                                <span className="ml-1 font-medium">{activity.realDuration} min</span>
                              </div>
                            )}
                            {activity.pic && (
                              <div className="col-span-2">
                                <span className="text-muted-foreground">PIC:</span>
                                <span className="ml-1">{activity.pic}</span>
                              </div>
                            )}
                            {activity.result && (
                              <div className="col-span-2">
                                <span className="text-muted-foreground">Result:</span>
                                <span className="ml-1">{activity.result}</span>
                              </div>
                            )}
                            {activity.notes && (
                              <div className="col-span-2 italic text-muted-foreground">
                                {activity.notes}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
              
              {index < sampleEvents.length - 1 && (
                <Separator className="mt-4" />
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default MaintenanceTimeline;