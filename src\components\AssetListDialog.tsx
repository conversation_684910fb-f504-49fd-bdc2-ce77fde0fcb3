import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "./ui/dialog";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Asset, AssetDetail } from "../data/networkRoutes";
import { Package, Cable, Zap, Building, Eye } from "lucide-react";
import AssetDetailDialog from "./AssetDetailDialog";

interface AssetListDialogProps {
  isOpen: boolean;
  onClose: () => void;
  asset: Asset | null;
  onAssetUpdate?: (updatedAsset: AssetDetail) => void;
}

const getAssetIcon = (assetType: string) => {
  switch (assetType.toLowerCase()) {
    case "fiber optic cable":
      return <Cable className="h-4 w-4" />;
    case "odf":
      return <Zap className="h-4 w-4" />;
    case "handhole":
    case "pole":
    case "jc":
      return <Building className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
};

const getConditionColor = (condition: string) => {
  switch (condition.toLowerCase()) {
    case "excellent":
      return "bg-green-100 text-green-800";
    case "good":
      return "bg-blue-100 text-blue-800";
    case "fair":
      return "bg-yellow-100 text-yellow-800";
    case "poor":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function AssetListDialog({ isOpen, onClose, asset, onAssetUpdate }: AssetListDialogProps) {
  const [selectedAssetDetail, setSelectedAssetDetail] = useState<AssetDetail | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  const handleViewDetail = (detail: AssetDetail) => {
    setSelectedAssetDetail(detail);
    setIsDetailOpen(true);
  };

  const handleDetailClose = () => {
    setIsDetailOpen(false);
    setSelectedAssetDetail(null);
  };

  if (!asset) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {getAssetIcon(asset.type)}
              <span>{asset.type} Assets</span>
              <Badge variant="outline">
                {asset.details?.length || 0} of {asset.count} items
              </Badge>
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {asset.details && asset.details.length > 0 ? (
              <div className="grid gap-4">
                {asset.details.map((detail, index) => (
                  <div 
                    key={detail.id} 
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                        {detail.photo ? (
                          <img 
                            src={detail.photo} 
                            alt={detail.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <div className={`${detail.photo ? 'hidden' : ''} text-gray-400`}>
                          {getAssetIcon(asset.type)}
                        </div>
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{detail.name}</h3>
                        <p className="text-sm text-gray-600">{detail.location.address}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={getConditionColor(detail.condition)} variant="secondary">
                            {detail.condition}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {detail.brand} - {detail.model}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="text-right text-sm">
                        <p className="text-gray-600">Installed:</p>
                        <p className="font-medium">{detail.installationDate}</p>
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleViewDetail(detail)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No detailed information available for this asset type.</p>
                <p className="text-sm">Total count: {asset.count} items</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
      
      <AssetDetailDialog 
        isOpen={isDetailOpen}
        onClose={handleDetailClose}
        assetDetail={selectedAssetDetail}
        onAssetUpdate={onAssetUpdate}
      />
    </>
  );
}