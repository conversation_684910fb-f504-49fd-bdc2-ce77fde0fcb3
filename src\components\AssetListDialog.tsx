import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "./ui/dialog";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Checkbox } from "./ui/checkbox";
import { Asset, AssetDetail } from "../data/networkRoutes";
import { Package, Cable, Zap, Building, Eye, Printer, CheckSquare, Square } from "lucide-react";
import AssetDetailDialog from "./AssetDetailDialog";

interface AssetListDialogProps {
  isOpen: boolean;
  onClose: () => void;
  asset: Asset | null;
  onAssetUpdate?: (updatedAsset: AssetDetail) => void;
}

const getAssetIcon = (assetType: string) => {
  switch (assetType.toLowerCase()) {
    case "fiber optic cable":
      return <Cable className="h-4 w-4" />;
    case "odf":
      return <Zap className="h-4 w-4" />;
    case "handhole":
    case "pole":
    case "jc":
      return <Building className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
};

const getConditionColor = (condition: string) => {
  switch (condition.toLowerCase()) {
    case "excellent":
      return "bg-green-100 text-green-800";
    case "good":
      return "bg-blue-100 text-blue-800";
    case "fair":
      return "bg-yellow-100 text-yellow-800";
    case "poor":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function AssetListDialog({ isOpen, onClose, asset, onAssetUpdate }: AssetListDialogProps) {
  const [selectedAssetDetail, setSelectedAssetDetail] = useState<AssetDetail | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedAssets, setSelectedAssets] = useState<Set<string>>(new Set());
  const [isSelectMode, setIsSelectMode] = useState(false);

  const handleViewDetail = (detail: AssetDetail) => {
    setSelectedAssetDetail(detail);
    setIsDetailOpen(true);
  };

  const handleDetailClose = () => {
    setIsDetailOpen(false);
    setSelectedAssetDetail(null);
  };

  const handleAssetDetailUpdate = (updatedAsset: AssetDetail) => {
    // Update the selected asset detail in local state
    setSelectedAssetDetail(updatedAsset);
    // Propagate the update to parent component
    if (onAssetUpdate) {
      onAssetUpdate(updatedAsset);
    }
  };

  const toggleSelectMode = () => {
    setIsSelectMode(!isSelectMode);
    setSelectedAssets(new Set());
  };

  const toggleAssetSelection = (assetId: string) => {
    const newSelected = new Set(selectedAssets);
    if (newSelected.has(assetId)) {
      newSelected.delete(assetId);
    } else {
      newSelected.add(assetId);
    }
    setSelectedAssets(newSelected);
  };

  const selectAllAssets = () => {
    if (!asset?.details) return;
    const allIds = new Set(asset.details.map(detail => detail.id));
    setSelectedAssets(allIds);
  };

  const clearSelection = () => {
    setSelectedAssets(new Set());
  };

  const handleBulkPrint = () => {
    if (!asset?.details || selectedAssets.size === 0) return;

    const selectedAssetDetails = asset.details.filter(detail => selectedAssets.has(detail.id));

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      handleBulkPrintFallback(selectedAssetDetails);
      return;
    }

    const htmlContent = generateBulkPrintHTML(selectedAssetDetails);
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const generateBulkPrintHTML = (assets: AssetDetail[]) => {
    const assetsHtml = assets.map((assetDetail, index) => {
      const photosHtml = assetDetail.photos && assetDetail.photos.length > 0
        ? `
          <div style="margin-top: 8px; page-break-inside: avoid;">
            <h4 style="color: #1f2937; margin-bottom: 4px; font-size: 9px; font-weight: 600;">Photos</h4>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 2px; max-width: 100%;">
              ${assetDetail.photos.slice(0, 4).map((photo, photoIndex) => `
                <div style="position: relative; border: 1px solid #e5e7eb; border-radius: 2px; overflow: hidden; aspect-ratio: 1; width: 100%; max-width: 40px;">
                  <img src="${photo}" alt="Photo ${photoIndex + 1}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'" />
                  <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 1px; background: rgba(0,0,0,0.7); color: white; text-align: center; font-size: 6px;">${photoIndex + 1}</div>
                </div>
              `).join('')}
            </div>
            ${assetDetail.photos.length > 4 ? `<div style="font-size: 7px; color: #6b7280; margin-top: 2px;">+${assetDetail.photos.length - 4} more photos</div>` : ''}
          </div>
        `
        : '<div style="margin-top: 8px; padding: 6px; background: #f9fafb; border-radius: 4px; text-align: center; color: #6b7280; font-size: 7px;">No photos available</div>';

      return `
        <div style="margin-bottom: 20px; page-break-inside: avoid; border: 1px solid #e5e7eb; border-radius: 8px; padding: 12px; background: white;">
          <div style="border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; margin-bottom: 12px;">
            <h3 style="font-size: 12px; font-weight: bold; color: #1f2937; margin: 0;">${assetDetail.name}</h3>
            <div style="font-size: 8px; color: #6b7280; margin-top: 2px;">Asset ID: ${assetDetail.id} | Category: ${assetDetail.category}</div>
          </div>

          <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 8px;">
            <div>
              <div style="font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px;">Brand</div>
              <div style="font-size: 8px; color: #1f2937;">${assetDetail.brand || 'N/A'}</div>
            </div>
            <div>
              <div style="font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px;">Model</div>
              <div style="font-size: 8px; color: #1f2937;">${assetDetail.model || 'N/A'}</div>
            </div>
            <div>
              <div style="font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px;">Condition</div>
              <div style="font-size: 8px; color: #1f2937; text-transform: capitalize;">${assetDetail.condition || 'N/A'}</div>
            </div>
          </div>

          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin-bottom: 8px;">
            <div>
              <div style="font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px;">Installation Date</div>
              <div style="font-size: 8px; color: #1f2937;">${assetDetail.installationDate ? new Date(assetDetail.installationDate).toLocaleDateString() : 'N/A'}</div>
            </div>
            <div>
              <div style="font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px;">Last Maintenance</div>
              <div style="font-size: 8px; color: #1f2937;">${assetDetail.lastMaintenance ? new Date(assetDetail.lastMaintenance).toLocaleDateString() : 'N/A'}</div>
            </div>
          </div>

          ${photosHtml}
        </div>
      `;
    }).join('');

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Asset Summary Report - ${asset?.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 10px; color: #333; }
            .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
            .title { font-size: 16px; font-weight: bold; color: #1f2937; margin-bottom: 4px; }
            .subtitle { font-size: 10px; color: #6b7280; }
            .summary { margin-bottom: 20px; padding: 10px; background: #f8fafc; border-radius: 6px; }
            .footer { margin-top: 30px; text-align: center; font-size: 8px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 10px; }
            @media print {
              body { margin: 0; }
              .header { page-break-after: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">ASSET SUMMARY REPORT</div>
            <div class="subtitle">Route: ${asset?.name} | Generated on ${new Date().toLocaleString('id-ID', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
          </div>

          <div class="summary">
            <div style="font-size: 10px; font-weight: 600; color: #1f2937; margin-bottom: 4px;">Summary</div>
            <div style="font-size: 8px; color: #6b7280;">Total Assets: ${assets.length} | Route: ${asset?.name}</div>
          </div>

          ${assetsHtml}

          <div class="footer">
            <div>Asset Management System - Summary Report</div>
            <div>Report generated for ${assets.length} asset(s) from route: ${asset?.name}</div>
          </div>
        </body>
      </html>
    `;
  };

  const handleBulkPrintFallback = (assets: AssetDetail[]) => {
    const reportContent = `
ASSET SUMMARY REPORT
${'='.repeat(60)}

Route: ${asset?.name}
Generated on: ${new Date().toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}

SUMMARY
${'-'.repeat(30)}
Total Assets: ${assets.length}
Route: ${asset?.name}

ASSET DETAILS
${'-'.repeat(30)}
${assets.map((assetDetail, index) => `
${index + 1}. ${assetDetail.name}
   Asset ID: ${assetDetail.id}
   Category: ${assetDetail.category}
   Brand: ${assetDetail.brand || 'N/A'}
   Model: ${assetDetail.model || 'N/A'}
   Condition: ${assetDetail.condition || 'N/A'}
   Installation Date: ${assetDetail.installationDate ? new Date(assetDetail.installationDate).toLocaleDateString() : 'N/A'}
   Last Maintenance: ${assetDetail.lastMaintenance ? new Date(assetDetail.lastMaintenance).toLocaleDateString() : 'N/A'}
   Photos: ${assetDetail.photos?.length || 0} photo(s) available
`).join('\n')}

${'='.repeat(60)}
Asset Management System - Summary Report
Report generated for ${assets.length} asset(s) from route: ${asset?.name}
    `;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `asset-summary-${asset?.name?.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (!asset) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center space-x-2">
                {getAssetIcon(asset.type)}
                <span>{asset.type} Assets</span>
                <Badge variant="outline">
                  {asset.details?.length || 0} of {asset.count} items
                </Badge>
              </DialogTitle>

              <div className="flex items-center space-x-2">
                {!isSelectMode ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleSelectMode}
                  >
                    <CheckSquare className="h-4 w-4 mr-2" />
                    Select Multiple
                  </Button>
                ) : (
                  <>
                    <div className="text-sm text-gray-600">
                      {selectedAssets.size} selected
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={selectAllAssets}
                      disabled={selectedAssets.size === asset.details?.length}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearSelection}
                      disabled={selectedAssets.size === 0}
                    >
                      Clear
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleBulkPrint}
                      disabled={selectedAssets.size === 0}
                    >
                      <Printer className="h-4 w-4 mr-2" />
                      Print ({selectedAssets.size})
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleSelectMode}
                    >
                      Cancel
                    </Button>
                  </>
                )}
              </div>
            </div>
          </DialogHeader>
          
          <div className="space-y-4">
            {asset.details && asset.details.length > 0 ? (
              <div className="grid gap-4">
                {asset.details.map((detail, index) => (
                  <div 
                    key={detail.id} 
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      {isSelectMode && (
                        <Checkbox
                          checked={selectedAssets.has(detail.id)}
                          onCheckedChange={() => toggleAssetSelection(detail.id)}
                        />
                      )}

                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden relative">
                        {(detail.photos && detail.photos.length > 0) || detail.photo ? (
                          <>
                            <img
                              src={detail.photos?.[0] || detail.photo}
                              alt={detail.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const fallback = target.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                if (fallback) {
                                  fallback.style.display = 'flex';
                                }
                              }}
                            />
                            <div className="fallback-icon absolute inset-0 flex items-center justify-center text-gray-400 bg-gray-200" style={{ display: 'none' }}>
                              {getAssetIcon(asset.type)}
                            </div>
                          </>
                        ) : (
                          <div className="text-gray-400">
                            {getAssetIcon(asset.type)}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{detail.name}</h3>
                        <p className="text-sm text-gray-600">{detail.location.address}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={getConditionColor(detail.condition)} variant="secondary">
                            {detail.condition}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {detail.brand} - {detail.model}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="text-right text-sm">
                        <p className="text-gray-600">Installed:</p>
                        <p className="font-medium">{detail.installationDate}</p>
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleViewDetail(detail)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No detailed information available for this asset type.</p>
                <p className="text-sm">Total count: {asset.count} items</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
      
      <AssetDetailDialog 
        isOpen={isDetailOpen}
        onClose={handleDetailClose}
        assetDetail={selectedAssetDetail}
        onAssetUpdate={handleAssetDetailUpdate}
      />
    </>
  );
}