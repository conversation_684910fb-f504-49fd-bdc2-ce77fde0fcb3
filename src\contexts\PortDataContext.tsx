"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface PortData {
  id: number;
  noPort: string;
  trafficName: string;
  route: string;
  odcName: string;
  otdrDistance: string;
  groundDistance: string;
  totalLoss: string;
  rsl: string;
}

interface PortDataContextType {
  table1Data: PortData[];
  table2Data: PortData[];
  setTable1Data: (data: PortData[]) => void;
  setTable2Data: (data: PortData[]) => void;
  updatePortData: (tableType: 'table1' | 'table2', data: PortData[]) => void;
  remarkContent: string;
  setRemarkContent: (content: string) => void;
  addNewRouteData: (newRoute: string) => void;
}

const PortDataContext = createContext<PortDataContextType | undefined>(undefined);

export const usePortData = () => {
  const context = useContext(PortDataContext);
  if (context === undefined) {
    throw new Error('usePortData must be used within a PortDataProvider');
  }
  return context;
};

interface PortDataProviderProps {
  children: ReactNode;
}

export const PortDataProvider: React.FC<PortDataProviderProps> = ({ children }) => {
  // State for remark content
  const [remarkContent, setRemarkContent] = useState<string>(
    "Additional notes and observations regarding the core network status and performance metrics."
  );

  // Initial data for table1 (Network Table A)
  const [table1Data, setTable1Data] = useState<PortData[]>([
    {
      id: 1,
      noPort: "P001",
      trafficName: "Traffic A",
      route: "Route-01",
      odcName: "ODC-Jakarta-01",
      otdrDistance: "12.5 km",
      groundDistance: "13.2 km",
      totalLoss: "0.8 dB",
      rsl: "-15.2 dBm"
    },
    {
      id: 2,
      noPort: "P002",
      trafficName: "Traffic B",
      route: "Route-01",
      odcName: "ODC-Jakarta-01",
      otdrDistance: "8.3 km",
      groundDistance: "9.1 km",
      totalLoss: "0.6 dB",
      rsl: "-12.8 dBm"
    },
    {
      id: 3,
      noPort: "P003",
      trafficName: "Traffic C",
      route: "Route-01",
      odcName: "ODC-Jakarta-01",
      otdrDistance: "15.7 km",
      groundDistance: "16.4 km",
      totalLoss: "1.2 dB",
      rsl: "-18.5 dBm"
    }
  ]);

  // Initial data for table2 (Network Table B)
  const [table2Data, setTable2Data] = useState<PortData[]>([
    {
      id: 1,
      noPort: "P101",
      trafficName: "Traffic X",
      route: "Route-01",
      odcName: "ODC-Bandung-01",
      otdrDistance: "22.1 km",
      groundDistance: "23.8 km",
      totalLoss: "1.5 dB",
      rsl: "-20.3 dBm"
    },
    {
      id: 2,
      noPort: "P102",
      trafficName: "Traffic Y",
      route: "Route-01",
      odcName: "ODC-Bandung-01",
      otdrDistance: "18.9 km",
      groundDistance: "19.6 km",
      totalLoss: "1.1 dB",
      rsl: "-17.9 dBm"
    },
    {
      id: 3,
      noPort: "P103",
      trafficName: "Traffic Z",
      route: "Route-01",
      odcName: "ODC-Bandung-01",
      otdrDistance: "25.4 km",
      groundDistance: "26.2 km",
      totalLoss: "1.8 dB",
      rsl: "-22.1 dBm"
    }
  ]);

  const updatePortData = (tableType: 'table1' | 'table2', data: PortData[]) => {
    if (tableType === 'table1') {
      setTable1Data(data);
    } else {
      setTable2Data(data);
    }
  };

  // Function to add new route data automatically
  const addNewRouteData = (newRoute: string) => {
    // Generate new data for table1 with new route
    const newTable1Data: PortData[] = [
      {
        id: Math.max(...table1Data.map(p => p.id), 0) + 1,
        noPort: `P${String(Math.max(...table1Data.map(p => parseInt(p.noPort.replace('P', ''))), 0) + 1).padStart(3, '0')}`,
        trafficName: `Traffic ${String.fromCharCode(65 + table1Data.length)}`,
        route: newRoute,
        odcName: "ODC-Jakarta-01",
        otdrDistance: `${(Math.random() * 20 + 5).toFixed(1)} km`,
        groundDistance: `${(Math.random() * 22 + 6).toFixed(1)} km`,
        totalLoss: `${(Math.random() * 1.5 + 0.5).toFixed(1)} dB`,
        rsl: `${(Math.random() * -10 - 10).toFixed(1)} dBm`
      },
      {
        id: Math.max(...table1Data.map(p => p.id), 0) + 2,
        noPort: `P${String(Math.max(...table1Data.map(p => parseInt(p.noPort.replace('P', ''))), 0) + 2).padStart(3, '0')}`,
        trafficName: `Traffic ${String.fromCharCode(65 + table1Data.length + 1)}`,
        route: newRoute,
        odcName: "ODC-Jakarta-01",
        otdrDistance: `${(Math.random() * 20 + 5).toFixed(1)} km`,
        groundDistance: `${(Math.random() * 22 + 6).toFixed(1)} km`,
        totalLoss: `${(Math.random() * 1.5 + 0.5).toFixed(1)} dB`,
        rsl: `${(Math.random() * -10 - 10).toFixed(1)} dBm`
      }
    ];

    // Generate new data for table2 with new route
    const newTable2Data: PortData[] = [
      {
        id: Math.max(...table2Data.map(p => p.id), 0) + 1,
        noPort: `P${String(Math.max(...table2Data.map(p => parseInt(p.noPort.replace('P', ''))), 0) + 1).padStart(3, '0')}`,
        trafficName: `Traffic ${String.fromCharCode(88 + table2Data.length)}`, // Start from X, Y, Z
        route: newRoute,
        odcName: "ODC-Bandung-01",
        otdrDistance: `${(Math.random() * 25 + 15).toFixed(1)} km`,
        groundDistance: `${(Math.random() * 27 + 16).toFixed(1)} km`,
        totalLoss: `${(Math.random() * 2 + 1).toFixed(1)} dB`,
        rsl: `${(Math.random() * -15 - 15).toFixed(1)} dBm`
      },
      {
        id: Math.max(...table2Data.map(p => p.id), 0) + 2,
        noPort: `P${String(Math.max(...table2Data.map(p => parseInt(p.noPort.replace('P', ''))), 0) + 2).padStart(3, '0')}`,
        trafficName: `Traffic ${String.fromCharCode(88 + table2Data.length + 1)}`,
        route: newRoute,
        odcName: "ODC-Bandung-01",
        otdrDistance: `${(Math.random() * 25 + 15).toFixed(1)} km`,
        groundDistance: `${(Math.random() * 27 + 16).toFixed(1)} km`,
        totalLoss: `${(Math.random() * 2 + 1).toFixed(1)} dB`,
        rsl: `${(Math.random() * -15 - 15).toFixed(1)} dBm`
      }
    ];

    // Add new data to existing tables (keep existing data)
    setTable1Data([...table1Data, ...newTable1Data]);
    setTable2Data([...table2Data, ...newTable2Data]);

    // Update remark content
    const currentDate = new Date().toLocaleDateString('id-ID');
    const newRemarkContent = `${remarkContent}\n\n[${currentDate}] New route "${newRoute}" has been added with 2 new ports in each table. Existing route data is preserved. System automatically generated traffic data for network monitoring.`;
    setRemarkContent(newRemarkContent);
  };

  const value = {
    table1Data,
    table2Data,
    setTable1Data,
    setTable2Data,
    updatePortData,
    remarkContent,
    setRemarkContent,
    addNewRouteData
  };

  return (
    <PortDataContext.Provider value={value}>
      {children}
    </PortDataContext.Provider>
  );
};