"use client";

import React, { useState, useEffect } from "react";
import { useForm, use<PERSON><PERSON><PERSON><PERSON><PERSON>, FormProvider, Controller } from "react-hook-form"; // Import FormProvider and Controller
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { showSuccess } from "@/utils/toast";
import ActivityFormSection from "./ActivityFormSection"; // Import the new component
import ClosureSplicingFormSection from "./ClosureSplicingFormSection"; // Import the new component
import { Trash2, PlusCircle } from "lucide-react"; // Import icons for material/splicing forms
import { TroubleTicket, Activity, Material, ClosureDetail } from "./TroubleTicketManagement"; // Import the TroubleTicket interface and Activity
import PhotoUpload from "@/components/PhotoUpload";

// Define Zod schema for Material (now used here at top level)
const materialSchema = z.object({
  name: z.string().min(1, { message: "Material name cannot be empty." }),
  quantity: z.coerce.number().min(0, { message: "Quantity must be non-negative." }),
  unit: z.string().min(1, { message: "Unit cannot be empty." }),
});

// Define Zod schema for ClosureDetail (now used here at top level)
const closureDetailSchema = z.object({
  closureId: z.string().min(1, { message: "Closure ID cannot be empty." }),
  splicedCores: z.coerce.number().min(0, { message: "Spliced cores must be non-negative." }),
  latitude: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().optional()
  ),
  longitude: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().optional()
  ),
});

// Define Activity schema (material fields removed)
const activitySchema = z.object({
  name: z.string().min(1, { message: "Activity name cannot be empty." }), // Make it explicitly required
  estimatedDuration: z.coerce.number().min(0, { message: "Must be non-negative." }), // Make it explicitly required
  
  realDuration: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().min(0, { message: "Must be non-negative." }).optional()
  ),
  
  pic: z.string().optional(),
  result: z.string().optional(), // Result of the activity
  notes: z.string().optional(), // Additional notes for the activity
});

// Define the schema for a trouble ticket for editing (material fields added here)
const editTicketSchema = z.object({
  id: z.string(),
  title: z.string().min(5, { message: "Title must be at least 5 characters." }),
  description: z.string().min(10, { message: "Description must be at least 10 characters." }),
  priority: z.enum(["high", "medium", "low"], { message: "Invalid priority selected." }),
  status: z.enum(["open", "in-progress", "closed"], { message: "Invalid status selected." }),
  assignedTo: z.string().min(2, { message: "Assigned to field cannot be empty." }),
  openedAt: z.string(), // Added openedAt
  closedAt: z.string().nullable().optional(), // Added closedAt
  duration: z.string().nullable().optional(), // Added duration
  routeName: z.string().min(2, { message: "Route name cannot be empty." }), // New field
  repairType: z.string().optional(), // New field for repair type
  repairNature: z.enum(["permanent", "temporary"], { message: "Invalid repair nature selected." }), // New field for repair nature
  temporaryReason: z.string().optional(), // New field for temporary reason
  activities: z.array(activitySchema), // Activities array

  // Material and Closure details moved to the main ticket level
  materialsUsed: z.array(materialSchema).optional(),
  numClosures: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().min(0).optional()
  ),
  numCables: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().min(0).optional()
  ),
  numHdpe: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().min(0).optional()
  ),
  otherCustomMaterials: z.string().optional(),
  closureSplicingDetails: z.array(closureDetailSchema).optional(),
}).refine((data) => {
  if (data.repairNature === "temporary" && (!data.temporaryReason || data.temporaryReason.trim() === "")) {
    return false;
  }
  return true;
}, {
  message: "Temporary reason is required when repair nature is temporary.",
  path: ["temporaryReason"],
});

interface EditTroubleTicketDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ticket: TroubleTicket | null;
  onUpdate: (ticket: TroubleTicket) => void;
}

const EditTroubleTicketDialog: React.FC<EditTroubleTicketDialogProps> = ({
  isOpen,
  onClose,
  ticket,
  onUpdate,
}) => {
  const [photos, setPhotos] = useState<File[]>([]);

  // Helper function to convert files to base64
  const convertFilesToBase64 = async (files: File[]): Promise<string[]> => {
    const promises = files.map((file) => {
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    });
    return Promise.all(promises);
  };

  const methods = useForm<z.infer<typeof editTicketSchema>>({
    resolver: zodResolver(editTicketSchema),
    defaultValues: {
      id: "",
      title: "",
      description: "",
      priority: "medium",
      status: "open",
      assignedTo: "",
      openedAt: "",
      closedAt: null,
      duration: null,
      routeName: "",
      repairType: "", // Initialize repair type
      repairNature: "permanent", // Initialize repair nature
      temporaryReason: "", // Initialize temporary reason
      activities: [],
      materialsUsed: [], // Initialize
      numClosures: 0, // Initialize
      numCables: 0, // Initialize
      numHdpe: 0, // Initialize
      otherCustomMaterials: "", // Initialize
      closureSplicingDetails: [], // Initialize
    },
  });

  const { fields: activityFields } = useFieldArray({
    control: methods.control,
    name: "activities",
  });

  // New useFieldArray hooks for materials and closure splicing at the ticket level
  const { fields: materialsFields, append: appendMaterial, remove: removeMaterial } = useFieldArray({
    control: methods.control,
    name: "materialsUsed",
  });

  const { fields: closureSplicingFields, append: appendClosureSplicing, remove: removeClosureSplicing } = useFieldArray({
    control: methods.control,
    name: "closureSplicingDetails",
  });

  React.useEffect(() => {
    if (ticket) {
      methods.reset({
        id: ticket.id,
        title: ticket.title,
        description: ticket.description,
        priority: ticket.priority,
        status: ticket.status,
        assignedTo: ticket.assignedTo,
        openedAt: ticket.openedAt,
        closedAt: ticket.closedAt ?? undefined,
        duration: ticket.duration ?? undefined,
        routeName: ticket.routeName,
        repairType: ticket.repairType ?? "", // Set repair type
        repairNature: ticket.repairNature ?? "permanent", // Set repair nature
        temporaryReason: ticket.temporaryReason ?? "", // Set temporary reason
        activities: ticket.activities.map((activity): Activity => ({
          name: activity.name,
          estimatedDuration: activity.estimatedDuration,
          realDuration: activity.realDuration ?? undefined,
          pic: activity.pic ?? undefined,
          result: activity.result ?? undefined,
          notes: activity.notes ?? undefined,
        })),
        // Set material and splicing details from ticket object
        materialsUsed: ticket.materialsUsed?.map(m => ({name: m.name, quantity: m.quantity, unit: m.unit})) ?? [],
        numClosures: ticket.numClosures ?? undefined,
        numCables: ticket.numCables ?? undefined,
        numHdpe: ticket.numHdpe ?? undefined,
        otherCustomMaterials: ticket.otherCustomMaterials ?? undefined,
        closureSplicingDetails: ticket.closureSplicingDetails?.map(cd => ({
          closureId: cd.closureId, 
          splicedCores: cd.splicedCores,
          latitude: cd.latitude ?? undefined, // Set latitude
          longitude: cd.longitude ?? undefined, // Set longitude
        })) ?? [],
      });
    }
  }, [ticket, methods]);

  const handleSubmit = async (values: z.infer<typeof editTicketSchema>) => {
    if (ticket) {
      // Convert photos to base64
      const photosBase64 = await convertFilesToBase64(photos);
      
      const updatedTicket: TroubleTicket = {
        ...ticket,
        ...values,
        photos: photosBase64,
        closedAt: values.closedAt || null,
        duration: values.duration || null,
        activities: values.activities.map(activity => ({
          name: activity.name,
          estimatedDuration: activity.estimatedDuration,
          realDuration: activity.realDuration === undefined ? null : activity.realDuration,
          pic: activity.pic || "",
          result: activity.result || "",
          notes: activity.notes || "",
        })),
        // Ensure material and splicing details are correctly saved
        materialsUsed: values.materialsUsed?.map(m => ({name: m.name, quantity: m.quantity, unit: m.unit})) || [],
        numClosures: values.numClosures ?? null,
        numCables: values.numCables ?? null,
        numHdpe: values.numHdpe ?? null,
        otherCustomMaterials: values.otherCustomMaterials || "",
        closureSplicingDetails: values.closureSplicingDetails?.map(cd => ({
          closureId: cd.closureId, 
          splicedCores: cd.splicedCores,
          latitude: cd.latitude ?? null, // Save latitude
          longitude: cd.longitude ?? null, // Save longitude
        })) || [],
      };
      onUpdate(updatedTicket);
      showSuccess("Trouble ticket updated successfully!");
      setPhotos([]);
      onClose();
    }
  };

  if (!ticket) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Trouble Ticket</DialogTitle>
          <DialogDescription>
            Make changes to the ticket details.
          </DialogDescription>
        </DialogHeader>
        <FormProvider {...methods}> {/* Wrap the form with FormProvider */}
          <form onSubmit={methods.handleSubmit(handleSubmit)} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">
                Title
              </Label>
              <Input id="title" {...methods.register("title")} />
              {methods.formState.errors.title && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.title.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">
                Description
              </Label>
              <Textarea id="description" {...methods.register("description")} rows={4} />
              {methods.formState.errors.description && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.description.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="priority">
                Priority
              </Label>
              <Select onValueChange={(value) => methods.setValue("priority", value as "high" | "medium" | "low")} value={methods.watch("priority")}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
              {methods.formState.errors.priority && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.priority.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">
                Status
              </Label>
              <Select onValueChange={(value) => methods.setValue("status", value as "open" | "in-progress" | "closed")} value={methods.watch("status")}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in-progress">In-Progress</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
              {methods.formState.errors.status && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.status.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="assignedTo">
                Assigned To
              </Label>
              <Input id="assignedTo" {...methods.register("assignedTo")} />
              {methods.formState.errors.assignedTo && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.assignedTo.message}</p>
              )}
            </div>

            {/* Read-only time fields */}
            <div className="space-y-2">
              <Label htmlFor="openedAt">Opened At</Label>
              <Input id="openedAt" value={methods.watch("openedAt")} readOnly className="bg-muted" />
            </div>
            {methods.watch("status") === "closed" && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="closedAt">Closed At</Label>
                  <Input id="closedAt" value={methods.watch("closedAt") || ""} readOnly className="bg-muted" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration</Label>
                  <Input id="duration" value={methods.watch("duration") || ""} readOnly className="bg-muted" />
                </div>
              </>
            )}
            <div className="space-y-2">
              <Label htmlFor="routeName">
                Route Name
              </Label>
              <Input id="routeName" {...methods.register("routeName")} />
              {methods.formState.errors.routeName && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.routeName.message}</p>
              )}
            </div>
            <div className="space-y-2"> {/* New field for repair type */}
              <Label htmlFor="repairType">
                Repair Type
              </Label>
              <Select onValueChange={(value) => methods.setValue("repairType", value)} value={methods.watch("repairType")}>
                <SelectTrigger>
                  <SelectValue placeholder="Select repair type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fiber-cut">Fiber Cut</SelectItem>
                  <SelectItem value="power-outage">Power Outage</SelectItem>
                  <SelectItem value="equipment-failure">Equipment Failure</SelectItem>
                  <SelectItem value="configuration-error">Configuration Error</SelectItem>
                  <SelectItem value="connectivity-issue">Connectivity Issue</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="upgrade">Upgrade</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              {methods.formState.errors.repairType && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.repairType.message}</p>
              )}
            </div>
            <div className="space-y-2"> {/* New field for repair nature */}
              <Label htmlFor="repairNature">
                Repair Nature
              </Label>
              <Select onValueChange={(value) => methods.setValue("repairNature", value as "permanent" | "temporary")} value={methods.watch("repairNature")}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select repair nature" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="permanent">Permanent</SelectItem>
                  <SelectItem value="temporary">Temporary</SelectItem>
                </SelectContent>
              </Select>
              {methods.formState.errors.repairNature && (
                <p className="text-sm text-red-500 mt-1">{methods.formState.errors.repairNature.message}</p>
              )}
            </div>
            {methods.watch("repairNature") === "temporary" && (
              <div className="space-y-2"> {/* Conditional field for temporary reason */}
                <Label htmlFor="temporaryReason">
                    Temporary Reason
                  </Label>
                  <Textarea id="temporaryReason" {...methods.register("temporaryReason")} rows={3} placeholder="Explain why the repair is temporary..." />
                {methods.formState.errors.temporaryReason && (
                  <p className="text-sm text-red-500 mt-1">{methods.formState.errors.temporaryReason.message}</p>
                )}
              </div>
            )}

            {/* Material Usage Section (moved here) */}
            <div className="col-span-4 border-t pt-4 mt-4">
              <h3 className="text-lg font-semibold mb-3">Material Usage</h3>
              {materialsFields.map((materialField, materialIndex) => (
                <div key={materialField.id} className="grid grid-cols-1 md:grid-cols-4 items-center gap-2 border p-2 rounded-md mb-2">
                  <Label htmlFor={`materialsUsed.${materialIndex}.name`} className="text-right md:text-left text-sm">
                    Name
                  </Label>
                  <Input id={`materialsUsed.${materialIndex}.name`} {...methods.register(`materialsUsed.${materialIndex}.name`)} className="col-span-3" />
                  {methods.formState.errors.materialsUsed?.[materialIndex]?.name && (
                    <p className="col-span-4 text-right text-xs text-red-500">{methods.formState.errors.materialsUsed[materialIndex]?.name?.message}</p>
                  )}

                  <Label htmlFor={`materialsUsed.${materialIndex}.quantity`} className="text-right md:text-left text-sm">
                    Quantity
                  </Label>
                  <Input id={`materialsUsed.${materialIndex}.quantity`} type="number" step="0.01" {...methods.register(`materialsUsed.${materialIndex}.quantity`, { valueAsNumber: true })} className="col-span-3" />
                  {methods.formState.errors.materialsUsed?.[materialIndex]?.quantity && (
                    <p className="col-span-4 text-right text-xs text-red-500">{methods.formState.errors.materialsUsed[materialIndex]?.quantity?.message}</p>
                  )}

                  <Label htmlFor={`materialsUsed.${materialIndex}.unit`} className="text-right md:text-left text-sm">
                    Unit
                  </Label>
                  <Input id={`materialsUsed.${materialIndex}.unit`} {...methods.register(`materialsUsed.${materialIndex}.unit`)} className="col-span-3" />
                  {methods.formState.errors.materialsUsed?.[materialIndex]?.unit && (
                    <p className="col-span-4 text-right text-xs text-red-500">{methods.formState.errors.materialsUsed[materialIndex]?.unit?.message}</p>
                  )}

                  <div className="col-span-4 flex justify-end">
                    <Button type="button" variant="destructive" size="sm" onClick={() => removeMaterial(materialIndex)}>
                      <Trash2 className="h-3 w-3 mr-1" /> Remove Material
                    </Button>
                  </div>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => appendMaterial({ name: "", quantity: 0, unit: "" })}
                className="w-full mt-2"
              >
                <PlusCircle className="h-4 w-4 mr-2" /> Add Material
              </Button>
            </div>

            {/* Other Material Counts (moved here) */}
            <div className="col-span-4 border-t pt-4 mt-4">
              <h4 className="font-semibold text-md mb-2">Component Counts</h4>
              <div className="space-y-2">
                <div className="grid grid-cols-4 items-center gap-2">
                  <Label htmlFor="numClosures" className="text-right md:text-left">
                    Number of Closures
                  </Label>
                  <Input 
                    id="numClosures" 
                    type="number" 
                    {...methods.register("numClosures")}
                    value={methods.watch("numClosures") ?? ""}
                    className="col-span-3" 
                  />
                  {methods.formState.errors.numClosures && (
                    <p className="col-span-4 text-right text-sm text-red-500">{methods.formState.errors.numClosures.message}</p>
                  )}
                </div>
                <div className="grid grid-cols-4 items-center gap-2">
                  <Label htmlFor="numCables" className="text-right md:text-left">
                    Number of Cables
                  </Label>
                  <Input 
                    id="numCables" 
                    type="number" 
                    {...methods.register("numCables")}
                    value={methods.watch("numCables") ?? ""}
                    className="col-span-3" 
                  />
                  {methods.formState.errors.numCables && (
                    <p className="col-span-4 text-right text-sm text-red-500">{methods.formState.errors.numCables.message}</p>
                  )}
                </div>
                <div className="grid grid-cols-4 items-center gap-2">
                  <Label htmlFor="numHdpe" className="text-right md:text-left">
                    Number of HDPE
                  </Label>
                  <Input 
                    id="numHdpe" 
                    type="number" 
                    {...methods.register("numHdpe")}
                    value={methods.watch("numHdpe") ?? ""}
                    className="col-span-3" 
                  />
                  {methods.formState.errors.numHdpe && (
                    <p className="col-span-4 text-right text-sm text-red-500">{methods.formState.errors.numHdpe.message}</p>
                  )}
                </div>
                <div className="grid grid-cols-4 items-center gap-2">
                  <Label htmlFor="otherCustomMaterials" className="text-right md:text-left">
                    Other Custom Materials
                  </Label>
                  <Textarea id="otherCustomMaterials" {...methods.register("otherCustomMaterials")} rows={2} className="col-span-3" />
                  {methods.formState.errors.otherCustomMaterials && (
                    <p className="col-span-4 text-right text-sm text-red-500">{methods.formState.errors.otherCustomMaterials.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Closure Splicing Details Section (moved here) */}
            <div className="col-span-4 border-t pt-4 mt-4">
              <h4 className="font-semibold text-md mb-2">Closure Splicing Details</h4>
              {closureSplicingFields.map((closureField, closureIndex) => (
                <ClosureSplicingFormSection
                  key={closureField.id}
                  closureIndex={closureIndex}
                  removeClosureSplicing={removeClosureSplicing}
                />
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => appendClosureSplicing({ closureId: "", splicedCores: 0, latitude: undefined, longitude: undefined })}
                className="w-full mt-2"
              >
                <PlusCircle className="h-4 w-4 mr-2" /> Add Closure Splicing
              </Button>
            </div>

            {/* Activities Section - Keep existing grid for this section as it's more complex */}
            <div className="col-span-4 border-t pt-4 mt-4">
              <h3 className="text-lg font-semibold mb-3">Activity Details</h3>
              {activityFields.map((field, index) => (
                <ActivityFormSection
                  key={field.id}
                  activityIndex={index}
                  activityName={field.name}
                />
              ))}
            </div>

            {/* Photo Upload Section */}
            <div className="col-span-4 border-t pt-4 mt-4">
              <PhotoUpload
                label="Trouble Ticket Photos (Maximum 4 photos)"
                photos={photos}
                onPhotosChange={setPhotos}
                maxPhotos={4}
              />
            </div>

            <DialogFooter>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

export default EditTroubleTicketDialog;