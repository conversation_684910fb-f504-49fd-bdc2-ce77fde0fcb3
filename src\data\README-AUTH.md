# Implementasi Authentication dengan Supabase untuk MS Dashboard

## Overview

Sistem authentication ini menggunakan Supabase sebagai backend dengan fitur-fitur:
- User registration & login
- Role-based access control (RBAC)
- Permission-based authorization
- Row Level Security (RLS)
- Audit logging
- Session management

## File Structure

```
src/
├── data/
│   ├── auth_schema.sql          # Database schema untuk Supabase
│   ├── supabase-setup.md        # Panduan setup Supabase
│   └── README-AUTH.md           # Dokumentasi ini
├── lib/
│   └── supabase.ts              # Supabase client & helper functions
├── contexts/
│   └── AuthContext.tsx          # React context untuk auth state
├── types/
│   └── auth.ts                  # TypeScript types untuk auth
└── components/
    └── ProtectedRoute.tsx       # Component untuk route protection
```

## Setup Steps

### 1. Supabase Project Setup

1. **Buat project baru di Supabase**
   - Kunjungi [supabase.com](https://supabase.com)
   - <PERSON><PERSON> "New Project"
   - Pilih organization dan beri nama project
   - Set password database yang kuat

2. **Jalankan Database Schema**
   - Buka SQL Editor di dashboard Supabase
   - Copy paste isi file `auth_schema.sql`
   - <PERSON><PERSON> "Run" untuk eksekusi

### 2. Environment Configuration

1. **Buat file `.env.local`**
   ```env
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. **Get Supabase credentials**
   - URL: Dashboard > Settings > API > Project URL
   - Anon Key: Dashboard > Settings > API > Project API keys > anon public

### 3. Install Dependencies

```bash
npm install @supabase/supabase-js --legacy-peer-deps
```

## Database Schema

### Core Tables

1. **profiles** - Extended user data
   - Linked to `auth.users` via foreign key
   - Stores additional user info (username, department, etc.)

2. **roles** - System roles
   - admin, manager, technician, operator, viewer

3. **permissions** - Granular permissions
   - Organized by resource and action
   - Examples: view_dashboard, create_tickets, etc.

4. **user_roles** - User-Role mapping
5. **role_permissions** - Role-Permission mapping
6. **audit_logs** - Activity tracking

### Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Policies** configured for role-based access
- **Functions** for permission checking
- **Triggers** for auto-profile creation and audit logging

## Usage Examples

### 1. Login User

```typescript
import { useAuth } from '../contexts/AuthContext';

const LoginComponent = () => {
  const { login } = useAuth();

  const handleLogin = async () => {
    try {
      await login({
        username: '<EMAIL>', // Can use email
        password: 'password123'
      });
    } catch (error) {
      console.error('Login failed:', error);
    }
  };
};
```

### 2. Check Permissions

```typescript
import { useAuth } from '../contexts/AuthContext';

const SomeComponent = () => {
  const { checkPermission } = useAuth();

  if (!checkPermission('view_dashboard')) {
    return <div>Access denied</div>;
  }

  return <div>Dashboard content</div>;
};
```

### 3. Protected Routes

```typescript
import ProtectedRoute from '../components/ProtectedRoute';

const App = () => {
  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute requiredPermission="view_dashboard">
            <DashboardPage />
          </ProtectedRoute>
        } 
      />
    </Routes>
  );
};
```

### 4. Admin Operations

```typescript
import { adminHelpers } from '../lib/supabase';

// Assign role to user
await adminHelpers.assignRole(userId, roleId);

// Get all users
const users = await adminHelpers.getAllUsers();

// Get audit logs
const logs = await adminHelpers.getAuditLogs(50);
```

## Role Hierarchy

### Admin
- Full system access
- User management
- All CRUD operations
- System configuration

### Manager
- Dashboard and analytics
- User management (limited)
- Ticket management
- Report generation

### Technician
- Dashboard access
- Ticket handling
- Maintenance scheduling
- Asset updates

### Operator
- Dashboard access
- Ticket creation/updates
- Patrol reporting
- Basic operations

### Viewer
- Read-only access
- Dashboard viewing
- Report viewing
- No modifications

## Permission System

Permissions are organized by resource and action:

### Format: `{action}_{resource}`

**Dashboard:**
- view_dashboard
- view_analytics

**Users:**
- view_users
- create_users
- update_users
- delete_users
- manage_user_roles

**Tickets:**
- view_tickets
- create_tickets
- update_tickets
- delete_tickets
- assign_tickets
- close_tickets

**Assets:**
- view_assets
- create_assets
- update_assets
- delete_assets

And so on for other resources...

## Security Best Practices

1. **Environment Variables**
   - Never commit API keys to repository
   - Use `.env.local` for local development
   - Use secure environment variables in production

2. **Row Level Security**
   - All tables have RLS enabled
   - Policies enforce role-based access
   - Users can only access their own data unless admin

3. **Password Security**
   - Supabase handles password hashing
   - Enforce strong password policies
   - Support password reset functionality

4. **Session Management**
   - Automatic token refresh
   - Secure session storage
   - Proper logout handling

5. **Audit Logging**
   - All important actions are logged
   - Includes user, action, timestamp, and changes
   - Admin can review audit trails

## Troubleshooting

### Common Issues

1. **"relation auth.users does not exist"**
   - Ensure Authentication is enabled in Supabase
   - Refresh browser and retry

2. **Permission denied errors**
   - Check if user has required role
   - Verify RLS policies are correct
   - Ensure user is properly authenticated

3. **Environment variable errors**
   - Verify `.env.local` file exists
   - Check variable names match exactly
   - Restart development server after changes

### Debug Tips

1. **Check Supabase logs**
   - Dashboard > Logs > API logs
   - Look for authentication errors

2. **Verify user roles**
   ```sql
   SELECT * FROM user_roles_summary WHERE email = '<EMAIL>';
   ```

3. **Test permissions**
   ```sql
   SELECT user_has_permission('user-uuid', 'permission_name');
   ```

## Migration from Mock Auth

If migrating from existing mock authentication:

1. **Update login logic**
   - Replace mock API calls with Supabase auth
   - Update credential handling

2. **Update user data structure**
   - Map existing user fields to new schema
   - Handle UUID vs integer ID differences

3. **Update permission checking**
   - Replace local permission arrays with database queries
   - Use new permission naming convention

4. **Test thoroughly**
   - Verify all auth flows work
   - Test role assignments
   - Validate permission checks

## Next Steps

1. **Setup Supabase project** following the setup guide
2. **Run database schema** to create tables and policies
3. **Configure environment variables** with your Supabase credentials
4. **Test authentication flow** with a test user
5. **Assign roles** to users as needed
6. **Implement permission checks** in your components

For detailed setup instructions, see `supabase-setup.md`.
