import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import UserProfile from '@/components/UserProfile';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

const ProfilePage: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-6">
        <div>
          <h1 className="text-3xl font-bold">User Profile</h1>
        <p className="text-gray-500 mt-1">View and manage your profile information</p>
        </div>
        
        <Separator />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            <UserProfile />
          </div>
          
          <div className="space-y-6">
            <Card className="p-6">
              <h3 className="text-lg font-medium mb-4">Aktivitas Terbaru</h3>
              <div className="space-y-4">
                {isAuthenticated ? (
                  <>
                    <div className="text-sm">
                      <p className="text-gray-500">Last login</p>
                      <p>Today, 10:45</p>
                    </div>
                    <div className="text-sm">
                      <p className="text-gray-500">Last password change</p>
                      <p>2 weeks ago</p>
                    </div>
                    <div className="text-sm">
                      <p className="text-gray-500">Tickets created</p>
                <p>3 days ago</p>
                    </div>
                  </>
                ) : (
                  <p>Please login to view your activities</p>
                )}
              </div>
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-medium mb-4">Account Security</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Two-Factor Authentication</span>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Login Notifications</span>
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Trusted Devices</span>
                <span className="text-xs">2 Devices</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;