"use client";

import React, { useState, useMemo, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Edit, Check, X, Settings, Upload, Download, Printer } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useNavigate } from "react-router-dom";
import ConnectedTablesCard from "@/components/ConnectedTablesCard";
import { usePortData } from "@/contexts/PortDataContext";


const CoreManagementPage: React.FC = () => {
  const cardTitle = "Core Network Status";
  const navigate = useNavigate();
  
  // Use shared data from context
  const { remarkContent, setRemarkContent, table1Data, table2Data, setTable1Data, setTable2Data } = usePortData();

  // Static active connections value for display
  const activeConnections = 3;

  // State untuk Remark card editing
  const [isEditingRemark, setIsEditingRemark] = useState(false);
  const [tempRemark, setTempRemark] = useState(remarkContent);

  // State untuk Import/Export
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [exportType, setExportType] = useState<'all' | 'table1' | 'table2'>('all');

  // Update tempRemark when remarkContent changes
  useEffect(() => {
    setTempRemark(remarkContent);
  }, [remarkContent]);

  // Export functions
  const handleExportCSV = () => {
    let csvContent = '';
    let filename = '';

    switch (exportType) {
      case 'table1':
        csvContent = generateTable1CSV();
        filename = `core-network-table1-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'table2':
        csvContent = generateTable2CSV();
        filename = `core-network-table2-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'all':
      default:
        csvContent = generateAllDataCSV();
        filename = `core-network-data-${new Date().toISOString().split('T')[0]}.csv`;
        break;
    }

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setShowExportDialog(false);
  };

  const handleExportXLS = () => {
    let xlsContent = '';
    let filename = '';

    switch (exportType) {
      case 'table1':
        xlsContent = generateTable1XLS();
        filename = `core-network-table1-${new Date().toISOString().split('T')[0]}.xls`;
        break;
      case 'table2':
        xlsContent = generateTable2XLS();
        filename = `core-network-table2-${new Date().toISOString().split('T')[0]}.xls`;
        break;
      case 'all':
      default:
        xlsContent = generateAllDataXLS();
        filename = `core-network-data-${new Date().toISOString().split('T')[0]}.xls`;
        break;
    }

    const blob = new Blob([xlsContent], { type: 'application/vnd.ms-excel' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setShowExportDialog(false);
  };

  const generateTable1CSV = () => {
    const headers = ['Port No', 'Traffic Name', 'Route', 'ODC Name', 'OTDR Distance', 'Ground Distance', 'Total Loss', 'RSL'];
    const rows = table1Data.map(port => [
      port.noPort,
      port.trafficName,
      port.route,
      port.odcName,
      port.otdrDistance,
      port.groundDistance,
      port.totalLoss,
      port.rsl
    ]);
    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  };

  const generateTable2CSV = () => {
    const headers = ['Port No', 'Traffic Name', 'Route', 'ODC Name', 'OTDR Distance', 'Ground Distance', 'Total Loss', 'RSL'];
    const rows = table2Data.map(port => [
      port.noPort,
      port.trafficName,
      port.route,
      port.odcName,
      port.otdrDistance,
      port.groundDistance,
      port.totalLoss,
      port.rsl
    ]);
    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  };

  const generateAllDataCSV = () => {
    let csvContent = 'NETWORK TABLE A\n';
    csvContent += generateTable1CSV();
    csvContent += '\n\nNETWORK TABLE B\n';
    csvContent += generateTable2CSV();
    return csvContent;
  };

  const generateTable1XLS = () => {
    const headers = ['Port No', 'Traffic Name', 'Route', 'ODC Name', 'OTDR Distance', 'Ground Distance', 'Total Loss', 'RSL'];
    const rows = table1Data.map(port => [
      port.noPort,
      port.trafficName,
      port.route,
      port.odcName,
      port.otdrDistance,
      port.groundDistance,
      port.totalLoss,
      port.rsl
    ]);
    return [headers, ...rows].map(row => row.join('\t')).join('\n');
  };

  const generateTable2XLS = () => {
    const headers = ['Port No', 'Traffic Name', 'Route', 'ODC Name', 'OTDR Distance', 'Ground Distance', 'Total Loss', 'RSL'];
    const rows = table2Data.map(port => [
      port.noPort,
      port.trafficName,
      port.route,
      port.odcName,
      port.otdrDistance,
      port.groundDistance,
      port.totalLoss,
      port.rsl
    ]);
    return [headers, ...rows].map(row => row.join('\t')).join('\n');
  };

  const generateAllDataXLS = () => {
    let xlsContent = 'NETWORK TABLE A\n';
    xlsContent += generateTable1XLS();
    xlsContent += '\n\nNETWORK TABLE B\n';
    xlsContent += generateTable2XLS();
    return xlsContent;
  };

  // Print function
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow popups to print report');
      return;
    }

    let contentHtml = '';
    let title = '';

    switch (exportType) {
      case 'table1':
        contentHtml = generateTable1PDF();
        title = 'Core Network Table A Report';
        break;
      case 'table2':
        contentHtml = generateTable2PDF();
        title = 'Core Network Table B Report';
        break;
      case 'all':
      default:
        contentHtml = generateAllDataPDF();
        title = 'Complete Core Network Report';
        break;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3b82f6; padding-bottom: 15px; }
            .title { font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 5px; }
            .subtitle { font-size: 14px; color: #6b7280; }
            .section { margin-bottom: 30px; page-break-inside: avoid; }
            .section-title { font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 15px; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px; }
            .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .table th, .table td { border: 1px solid #e5e7eb; padding: 8px; text-align: left; font-size: 12px; }
            .table th { background-color: #f9fafb; font-weight: 600; }
            .table tr:nth-child(even) { background-color: #f9fafb; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 15px; }
            @media print { .page-break { page-break-before: always; } }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">${title}</div>
            <div class="subtitle">Generated on ${new Date().toLocaleString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
          </div>
          ${contentHtml}
          <div class="footer">
            <div>Core Management System - ${title}</div>
            <div>Total Records: ${getTotalRecords()}</div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);

    setShowExportDialog(false);
  };

  const generateTable1PDF = () => {
    return `
      <div class="section">
        <div class="section-title">Network Table A (${table1Data.length} records)</div>
        <table class="table">
          <thead>
            <tr>
              <th>Port No</th>
              <th>Traffic Name</th>
              <th>Route</th>
              <th>ODC Name</th>
              <th>OTDR Distance</th>
              <th>Ground Distance</th>
              <th>Total Loss</th>
              <th>RSL</th>
            </tr>
          </thead>
          <tbody>
            ${table1Data.map(port => `
              <tr>
                <td>${port.noPort}</td>
                <td>${port.trafficName}</td>
                <td>${port.route}</td>
                <td>${port.odcName}</td>
                <td>${port.otdrDistance}</td>
                <td>${port.groundDistance}</td>
                <td>${port.totalLoss}</td>
                <td>${port.rsl}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  };

  const generateTable2PDF = () => {
    return `
      <div class="section">
        <div class="section-title">Network Table B (${table2Data.length} records)</div>
        <table class="table">
          <thead>
            <tr>
              <th>Port No</th>
              <th>Traffic Name</th>
              <th>Route</th>
              <th>ODC Name</th>
              <th>OTDR Distance</th>
              <th>Ground Distance</th>
              <th>Total Loss</th>
              <th>RSL</th>
            </tr>
          </thead>
          <tbody>
            ${table2Data.map(port => `
              <tr>
                <td>${port.noPort}</td>
                <td>${port.trafficName}</td>
                <td>${port.route}</td>
                <td>${port.odcName}</td>
                <td>${port.otdrDistance}</td>
                <td>${port.groundDistance}</td>
                <td>${port.totalLoss}</td>
                <td>${port.rsl}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  };

  const generateAllDataPDF = () => {
    return `
      ${generateTable1PDF()}
      <div class="page-break"></div>
      ${generateTable2PDF()}
    `;
  };

  const getTotalRecords = () => {
    switch (exportType) {
      case 'table1': return table1Data.length;
      case 'table2': return table2Data.length;
      case 'all':
      default: return table1Data.length + table2Data.length;
    }
  };

  // Import functions
  const handleImportData = async () => {
    if (!importFile) return;

    try {
      const text = await importFile.text();
      const lines = text.split('\n').filter(line => line.trim());

      if (lines.length < 2) {
        alert('CSV file must have at least a header row and one data row.');
        return;
      }

      // Detect table type based on content
      const content = text.toLowerCase();
      if (content.includes('network table a')) {
        importTable1FromCSV(lines);
      } else if (content.includes('network table b')) {
        importTable2FromCSV(lines);
      } else {
        // Try to import as general port data
        importGeneralPortData(lines);
      }

      alert('Data imported successfully!');
      setShowImportDialog(false);
      setImportFile(null);
    } catch (error) {
      console.error('Error importing data:', error);
      alert('Error importing data. Please check the file format.');
    }
  };

  const importTable1FromCSV = (lines: string[]) => {
    const headerIndex = lines.findIndex(line => line.toLowerCase().includes('port no'));
    if (headerIndex === -1) return;

    const headers = lines[headerIndex].split(',').map(h => h.replace(/"/g, '').trim());
    const newData = [];

    for (let i = headerIndex + 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());

      if (values.length >= headers.length && values[0]) {
        const port = {
          id: Math.max(...table1Data.map(p => p.id), 0) + newData.length + 1,
          noPort: values[headers.indexOf('Port No')] || `P${String(Date.now()).slice(-3)}`,
          trafficName: values[headers.indexOf('Traffic Name')] || 'Imported Traffic',
          route: values[headers.indexOf('Route')] || 'Imported Route',
          odcName: values[headers.indexOf('ODC Name')] || 'ODC-Imported',
          otdrDistance: values[headers.indexOf('OTDR Distance')] || '0 km',
          groundDistance: values[headers.indexOf('Ground Distance')] || '0 km',
          totalLoss: values[headers.indexOf('Total Loss')] || '0 dB',
          rsl: values[headers.indexOf('RSL')] || '0 dBm'
        };
        newData.push(port);
      }
    }

    if (newData.length > 0) {
      setTable1Data([...table1Data, ...newData]);
    }
  };

  const importTable2FromCSV = (lines: string[]) => {
    const headerIndex = lines.findIndex(line => line.toLowerCase().includes('port no'));
    if (headerIndex === -1) return;

    const headers = lines[headerIndex].split(',').map(h => h.replace(/"/g, '').trim());
    const newData = [];

    for (let i = headerIndex + 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());

      if (values.length >= headers.length && values[0]) {
        const port = {
          id: Math.max(...table2Data.map(p => p.id), 0) + newData.length + 1,
          noPort: values[headers.indexOf('Port No')] || `P${String(Date.now()).slice(-3)}`,
          trafficName: values[headers.indexOf('Traffic Name')] || 'Imported Traffic',
          route: values[headers.indexOf('Route')] || 'Imported Route',
          odcName: values[headers.indexOf('ODC Name')] || 'ODC-Imported',
          otdrDistance: values[headers.indexOf('OTDR Distance')] || '0 km',
          groundDistance: values[headers.indexOf('Ground Distance')] || '0 km',
          totalLoss: values[headers.indexOf('Total Loss')] || '0 dB',
          rsl: values[headers.indexOf('RSL')] || '0 dBm'
        };
        newData.push(port);
      }
    }

    if (newData.length > 0) {
      setTable2Data([...table2Data, ...newData]);
    }
  };

  const importGeneralPortData = (lines: string[]) => {
    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
    const newData = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());

      if (values.length >= headers.length && values[0]) {
        const port = {
          id: Math.max(...table1Data.map(p => p.id), 0) + newData.length + 1,
          noPort: values[headers.indexOf('Port No')] || `P${String(Date.now()).slice(-3)}`,
          trafficName: values[headers.indexOf('Traffic Name')] || 'Imported Traffic',
          route: values[headers.indexOf('Route')] || 'Imported Route',
          odcName: values[headers.indexOf('ODC Name')] || 'ODC-Imported',
          otdrDistance: values[headers.indexOf('OTDR Distance')] || '0 km',
          groundDistance: values[headers.indexOf('Ground Distance')] || '0 km',
          totalLoss: values[headers.indexOf('Total Loss')] || '0 dB',
          rsl: values[headers.indexOf('RSL')] || '0 dBm'
        };
        newData.push(port);
      }
    }

    if (newData.length > 0) {
      setTable1Data([...table1Data, ...newData]);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
    }
  };









  // Fungsi untuk edit Remark
  const handleEditRemark = () => {
    setTempRemark(remarkContent);
    setIsEditingRemark(true);
  };

  const handleSaveRemark = () => {
    setRemarkContent(tempRemark);
    setIsEditingRemark(false);
  };

  const handleCancelRemarkEdit = () => {
    setTempRemark(remarkContent);
    setIsEditingRemark(false);
  };

  const handleRemarkKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveRemark();
    } else if (e.key === 'Escape') {
      handleCancelRemarkEdit();
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Core Management</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowImportDialog(true)}
          >
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExportDialog(true)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setExportType('all');
              handlePrint();
            }}
          >
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      {/* Main Card Container */}
      <Card className="w-full border-2 border-gray-300">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">{cardTitle}</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <Card className="border-2 border-gray-400 bg-gray-50">
            <CardContent className="pt-6">
              {/* Core Network Status Cards Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* Card 1 - Network Availability */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Network Availability</p>
                        <p className="text-2xl font-bold text-green-600">99.8%</p>
                      </div>
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-green-500 rounded-full"></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Last 24 hours</p>
                  </CardContent>
                </Card>

                {/* Card 2 - Active Connections */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Active Connections</p>
                        <p className="text-2xl font-bold text-blue-600">{activeConnections}</p>
                      </div>
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-blue-500 rounded-full"></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Current active</p>
                  </CardContent>
                </Card>

                {/* Card 3 - Core Availability */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Core Availability</p>
                        <p className="text-2xl font-bold text-yellow-600">24 cores</p>
                      </div>
                      <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-yellow-500 rounded-full"></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Available cores</p>
                  </CardContent>
                </Card>
              </div>

              <ConnectedTablesCard />
              
              {/* Remark Card */}
              <div className="mt-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-lg font-semibold">Remark</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleEditRemark}
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </CardHeader>
                  <CardContent>
                    {isEditingRemark ? (
                      <div className="space-y-2">
                        <Textarea
                          value={tempRemark}
                          onChange={(e) => setTempRemark(e.target.value)}
                          onKeyDown={handleRemarkKeyPress}
                          className="min-h-[100px] resize-none"
                          placeholder="Enter remark..."
                          autoFocus
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={handleSaveRemark}>
                            <Check className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleCancelRemarkEdit}>
                            <X className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {remarkContent}
                      </p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Import Core Network Data</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="import-file">Select CSV File</Label>
              <input
                id="import-file"
                type="file"
                accept=".csv,.xls,.xlsx"
                onChange={handleFileSelect}
                className="mt-2 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>

            {importFile && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800">
                  <strong>Selected:</strong> {importFile.name}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  Size: {(importFile.size / 1024).toFixed(2)} KB
                </p>
              </div>
            )}

            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">Import Guidelines:</h4>
              <ul className="text-xs text-yellow-700 space-y-1">
                <li>• File must be in CSV format (exported from this system)</li>
                <li>• Data will be added to existing tables</li>
                <li>• System will auto-detect table type</li>
                <li>• Backup your data before importing</li>
              </ul>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => {
                setShowImportDialog(false);
                setImportFile(null);
              }}>
                Cancel
              </Button>
              <Button
                onClick={handleImportData}
                disabled={!importFile}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import Data
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Download className="h-5 w-5" />
              <span>Export Core Network Data</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="export-type">Data Type</Label>
              <Select value={exportType} onValueChange={(value: any) => setExportType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Data (Table A + Table B)</SelectItem>
                  <SelectItem value="table1">Network Table A Only</SelectItem>
                  <SelectItem value="table2">Network Table B Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Export Information:</h4>
              <div className="text-xs text-blue-700 space-y-1">
                <p>• <strong>Network Table A:</strong> {table1Data.length} records</p>
                <p>• <strong>Network Table B:</strong> {table2Data.length} records</p>
                <p>• <strong>Total Records:</strong> {table1Data.length + table2Data.length}</p>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowExportDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleExportCSV} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                CSV
              </Button>
              <Button onClick={handleExportXLS} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                XLS
              </Button>
              <Button onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CoreManagementPage;