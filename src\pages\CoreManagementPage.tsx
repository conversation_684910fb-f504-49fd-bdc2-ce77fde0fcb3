"use client";

import React, { useState, useMemo, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Edit, Check, X, Settings } from "lucide-react";
import { useNavigate } from "react-router-dom";
import ConnectedTablesCard from "@/components/ConnectedTablesCard";
import { usePortData } from "@/contexts/PortDataContext";


const CoreManagementPage: React.FC = () => {
  const cardTitle = "Core Network Status";
  const navigate = useNavigate();
  
  // Use shared data from context
  const { remarkContent, setRemarkContent } = usePortData();
  
  // Static active connections value for display
  const activeConnections = 3;
  
  // State untuk Remark card editing
  const [isEditingRemark, setIsEditingRemark] = useState(false);
  const [tempRemark, setTempRemark] = useState(remarkContent);

  // Update tempRemark when remarkContent changes
  useEffect(() => {
    setTempRemark(remarkContent);
  }, [remarkContent]);









  // Fungsi untuk edit Remark
  const handleEditRemark = () => {
    setTempRemark(remarkContent);
    setIsEditingRemark(true);
  };

  const handleSaveRemark = () => {
    setRemarkContent(tempRemark);
    setIsEditingRemark(false);
  };

  const handleCancelRemarkEdit = () => {
    setTempRemark(remarkContent);
    setIsEditingRemark(false);
  };

  const handleRemarkKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveRemark();
    } else if (e.key === 'Escape') {
      handleCancelRemarkEdit();
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Core Management</h1>
      </div>

      {/* Main Card Container */}
      <Card className="w-full border-2 border-gray-300">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">{cardTitle}</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <Card className="border-2 border-gray-400 bg-gray-50">
            <CardContent className="pt-6">
              {/* Core Network Status Cards Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* Card 1 - Network Availability */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Network Availability</p>
                        <p className="text-2xl font-bold text-green-600">99.8%</p>
                      </div>
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-green-500 rounded-full"></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Last 24 hours</p>
                  </CardContent>
                </Card>

                {/* Card 2 - Active Connections */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Active Connections</p>
                        <p className="text-2xl font-bold text-blue-600">{activeConnections}</p>
                      </div>
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-blue-500 rounded-full"></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Current active</p>
                  </CardContent>
                </Card>

                {/* Card 3 - Core Availability */}
                <Card className="bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Core Availability</p>
                        <p className="text-2xl font-bold text-yellow-600">24 cores</p>
                      </div>
                      <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-yellow-500 rounded-full"></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Available cores</p>
                  </CardContent>
                </Card>
              </div>

              <ConnectedTablesCard />
              
              {/* Remark Card */}
              <div className="mt-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-lg font-semibold">Remark</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleEditRemark}
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </CardHeader>
                  <CardContent>
                    {isEditingRemark ? (
                      <div className="space-y-2">
                        <Textarea
                          value={tempRemark}
                          onChange={(e) => setTempRemark(e.target.value)}
                          onKeyDown={handleRemarkKeyPress}
                          className="min-h-[100px] resize-none"
                          placeholder="Enter remark..."
                          autoFocus
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={handleSaveRemark}>
                            <Check className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleCancelRemarkEdit}>
                            <X className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {remarkContent}
                      </p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};

export default CoreManagementPage;