-- =====================================================
-- SUPABASE AUTH SCHEMA UNTUK MS DASHBOARD
-- =====================================================
-- Catatan: Supabase sudah menyediakan tabel auth.users bawaan
-- Kita akan membuat tabel tambahan untuk extend fungsionalitas

-- Enable Row Level Security (RLS) untuk semua tabel
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- TABEL PROFILES (Extend dari auth.users)
-- =====================================================
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    full_name VARCHAR(100),
    avatar_url VARCHAR(255),
    phone VARCHAR(20),
    department VARCHAR(100),
    position VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS untuk profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Policy untuk profiles - user hanya bisa melihat dan edit profile sendiri
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Policy untuk admin - bisa melihat semua profiles
CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'admin'
        )
    );

-- =====================================================
-- TABEL ROLES
-- =====================================================
CREATE TABLE public.roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS untuk roles
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;

-- Policy untuk roles - semua authenticated user bisa melihat
CREATE POLICY "Authenticated users can view roles" ON public.roles
    FOR SELECT TO authenticated USING (true);

-- Policy untuk admin - hanya admin yang bisa manage roles
CREATE POLICY "Only admins can manage roles" ON public.roles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'admin'
        )
    );

-- =====================================================
-- TABEL PERMISSIONS
-- =====================================================
CREATE TABLE public.permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS untuk permissions
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;

-- Policy untuk permissions - semua authenticated user bisa melihat
CREATE POLICY "Authenticated users can view permissions" ON public.permissions
    FOR SELECT TO authenticated USING (true);

-- Policy untuk admin - hanya admin yang bisa manage permissions
CREATE POLICY "Only admins can manage permissions" ON public.permissions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'admin'
        )
    );

-- =====================================================
-- TABEL ROLE_PERMISSIONS (Many-to-Many)
-- =====================================================
CREATE TABLE public.role_permissions (
    role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES public.permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (role_id, permission_id)
);

-- Enable RLS untuk role_permissions
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- Policy untuk role_permissions
CREATE POLICY "Authenticated users can view role permissions" ON public.role_permissions
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Only admins can manage role permissions" ON public.role_permissions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'admin'
        )
    );

-- =====================================================
-- TABEL USER_ROLES (Many-to-Many)
-- =====================================================
CREATE TABLE public.user_roles (
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (user_id, role_id)
);

-- Enable RLS untuk user_roles
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Policy untuk user_roles - user bisa melihat role sendiri
CREATE POLICY "Users can view own roles" ON public.user_roles
    FOR SELECT USING (auth.uid() = user_id);

-- Policy untuk admin - bisa manage semua user roles
CREATE POLICY "Admins can manage all user roles" ON public.user_roles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'admin'
        )
    );

-- =====================================================
-- TABEL AUDIT_LOGS (untuk tracking aktivitas)
-- =====================================================
CREATE TABLE public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(100),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS untuk audit_logs
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Policy untuk audit_logs - hanya admin yang bisa melihat
CREATE POLICY "Only admins can view audit logs" ON public.audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'admin'
        )
    );

-- =====================================================
-- FUNCTIONS DAN TRIGGERS
-- =====================================================

-- Function untuk update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers untuk auto-update updated_at
CREATE TRIGGER handle_updated_at_profiles
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_roles
    BEFORE UPDATE ON public.roles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_permissions
    BEFORE UPDATE ON public.permissions
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Function untuk auto-create profile saat user baru register
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger untuk auto-create profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function untuk check user permissions
CREATE OR REPLACE FUNCTION public.user_has_permission(
    user_uuid UUID,
    permission_name TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.user_roles ur
        JOIN public.role_permissions rp ON ur.role_id = rp.role_id
        JOIN public.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = user_uuid
        AND p.name = permission_name
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function untuk get user roles
CREATE OR REPLACE FUNCTION public.get_user_roles(user_uuid UUID)
RETURNS TABLE(role_name TEXT, role_description TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT r.name, r.description
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_uuid
    AND r.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- SEED DATA
-- =====================================================

-- Insert default roles
INSERT INTO public.roles (name, description) VALUES
('admin', 'Administrator dengan akses penuh ke seluruh sistem'),
('manager', 'Manager dengan akses ke fitur manajemen dan pelaporan'),
('technician', 'Teknisi dengan akses ke fitur maintenance dan perbaikan'),
('operator', 'Operator dengan akses ke operasional harian'),
('viewer', 'User dengan akses view-only ke data');

-- Insert permissions untuk MS Dashboard
INSERT INTO public.permissions (name, description, resource, action) VALUES
-- Dashboard permissions
('view_dashboard', 'Melihat dashboard utama', 'dashboard', 'view'),
('view_analytics', 'Melihat analytics dan metrics', 'dashboard', 'analytics'),

-- User management permissions
('view_users', 'Melihat daftar user', 'users', 'view'),
('create_users', 'Membuat user baru', 'users', 'create'),
('update_users', 'Mengupdate data user', 'users', 'update'),
('delete_users', 'Menghapus user', 'users', 'delete'),
('manage_user_roles', 'Mengelola role user', 'users', 'manage_roles'),

-- Trouble ticket permissions
('view_tickets', 'Melihat trouble tickets', 'tickets', 'view'),
('create_tickets', 'Membuat trouble ticket baru', 'tickets', 'create'),
('update_tickets', 'Mengupdate trouble tickets', 'tickets', 'update'),
('delete_tickets', 'Menghapus trouble tickets', 'tickets', 'delete'),
('assign_tickets', 'Assign trouble tickets', 'tickets', 'assign'),
('close_tickets', 'Menutup trouble tickets', 'tickets', 'close'),

-- Asset management permissions
('view_assets', 'Melihat asset management', 'assets', 'view'),
('create_assets', 'Membuat asset baru', 'assets', 'create'),
('update_assets', 'Mengupdate data asset', 'assets', 'update'),
('delete_assets', 'Menghapus asset', 'assets', 'delete'),

-- Route management permissions
('view_routes', 'Melihat route management', 'routes', 'view'),
('create_routes', 'Membuat route baru', 'routes', 'create'),
('update_routes', 'Mengupdate route', 'routes', 'update'),
('delete_routes', 'Menghapus route', 'routes', 'delete'),

-- Maintenance permissions
('view_maintenance', 'Melihat jadwal maintenance', 'maintenance', 'view'),
('schedule_maintenance', 'Menjadwalkan maintenance', 'maintenance', 'create'),
('update_maintenance', 'Mengupdate jadwal maintenance', 'maintenance', 'update'),
('cancel_maintenance', 'Membatalkan maintenance', 'maintenance', 'cancel'),

-- Patrol permissions
('view_patrol', 'Melihat data patrol', 'patrol', 'view'),
('create_patrol', 'Membuat laporan patrol', 'patrol', 'create'),
('update_patrol', 'Mengupdate laporan patrol', 'patrol', 'update'),

-- Port management permissions
('view_ports', 'Melihat port management', 'ports', 'view'),
('manage_ports', 'Mengelola port configuration', 'ports', 'manage'),

-- Core network permissions
('view_core_network', 'Melihat core network', 'core_network', 'view'),
('manage_core_network', 'Mengelola core network', 'core_network', 'manage'),

-- Reports permissions
('view_reports', 'Melihat laporan', 'reports', 'view'),
('generate_reports', 'Generate laporan', 'reports', 'create'),
('export_reports', 'Export laporan', 'reports', 'export'),

-- Settings permissions
('view_settings', 'Melihat pengaturan sistem', 'settings', 'view'),
('manage_settings', 'Mengelola pengaturan sistem', 'settings', 'manage'),

-- SOR permissions
('view_sor', 'Melihat SOR (Statement of Requirement)', 'sor', 'view'),
('manage_sor', 'Mengelola SOR', 'sor', 'manage');

-- Assign permissions to roles
DO $$
DECLARE
    admin_role_id UUID;
    manager_role_id UUID;
    technician_role_id UUID;
    operator_role_id UUID;
    viewer_role_id UUID;
BEGIN
    -- Get role IDs
    SELECT id INTO admin_role_id FROM public.roles WHERE name = 'admin';
    SELECT id INTO manager_role_id FROM public.roles WHERE name = 'manager';
    SELECT id INTO technician_role_id FROM public.roles WHERE name = 'technician';
    SELECT id INTO operator_role_id FROM public.roles WHERE name = 'operator';
    SELECT id INTO viewer_role_id FROM public.roles WHERE name = 'viewer';

    -- Admin gets all permissions
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT admin_role_id, id FROM public.permissions;

    -- Manager permissions
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT manager_role_id, id FROM public.permissions
    WHERE name IN (
        'view_dashboard', 'view_analytics', 'view_users', 'create_users', 'update_users', 'manage_user_roles',
        'view_tickets', 'create_tickets', 'update_tickets', 'assign_tickets', 'close_tickets',
        'view_assets', 'create_assets', 'update_assets',
        'view_routes', 'create_routes', 'update_routes',
        'view_maintenance', 'schedule_maintenance', 'update_maintenance',
        'view_patrol', 'create_patrol', 'update_patrol',
        'view_ports', 'manage_ports',
        'view_core_network', 'manage_core_network',
        'view_reports', 'generate_reports', 'export_reports',
        'view_settings', 'view_sor', 'manage_sor'
    );

    -- Technician permissions
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT technician_role_id, id FROM public.permissions
    WHERE name IN (
        'view_dashboard', 'view_tickets', 'create_tickets', 'update_tickets',
        'view_assets', 'update_assets',
        'view_routes', 'update_routes',
        'view_maintenance', 'schedule_maintenance', 'update_maintenance',
        'view_patrol', 'create_patrol', 'update_patrol',
        'view_ports', 'view_core_network',
        'view_reports', 'view_sor'
    );

    -- Operator permissions
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT operator_role_id, id FROM public.permissions
    WHERE name IN (
        'view_dashboard', 'view_tickets', 'create_tickets', 'update_tickets',
        'view_assets', 'view_routes',
        'view_maintenance', 'view_patrol', 'create_patrol',
        'view_ports', 'view_core_network',
        'view_reports', 'view_sor'
    );

    -- Viewer permissions (read-only)
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT viewer_role_id, id FROM public.permissions
    WHERE name IN (
        'view_dashboard', 'view_tickets', 'view_assets', 'view_routes',
        'view_maintenance', 'view_patrol', 'view_ports', 'view_core_network',
        'view_reports', 'view_sor'
    );
END $$;

-- =====================================================
-- INDEXES untuk Performance
-- =====================================================
CREATE INDEX idx_profiles_username ON public.profiles(username);
CREATE INDEX idx_profiles_department ON public.profiles(department);
CREATE INDEX idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON public.user_roles(role_id);
CREATE INDEX idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON public.role_permissions(permission_id);
CREATE INDEX idx_permissions_resource_action ON public.permissions(resource, action);
CREATE INDEX idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON public.audit_logs(created_at);
CREATE INDEX idx_audit_logs_action ON public.audit_logs(action);

-- =====================================================
-- VIEWS untuk kemudahan query
-- =====================================================

-- View untuk user dengan roles dan permissions
CREATE VIEW public.user_permissions_view AS
SELECT
    u.id as user_id,
    u.email,
    p.username,
    p.full_name,
    p.department,
    p.position,
    r.name as role_name,
    r.description as role_description,
    perm.name as permission_name,
    perm.description as permission_description,
    perm.resource,
    perm.action
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
LEFT JOIN public.user_roles ur ON u.id = ur.user_id
LEFT JOIN public.roles r ON ur.role_id = r.id
LEFT JOIN public.role_permissions rp ON r.id = rp.role_id
LEFT JOIN public.permissions perm ON rp.permission_id = perm.id
WHERE r.is_active = true;

-- View untuk summary user roles
CREATE VIEW public.user_roles_summary AS
SELECT
    u.id as user_id,
    u.email,
    p.username,
    p.full_name,
    p.department,
    p.is_active,
    ARRAY_AGG(r.name ORDER BY r.name) as roles,
    COUNT(DISTINCT r.id) as role_count,
    p.last_login,
    p.created_at
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
LEFT JOIN public.user_roles ur ON u.id = ur.user_id
LEFT JOIN public.roles r ON ur.role_id = r.id AND r.is_active = true
GROUP BY u.id, u.email, p.username, p.full_name, p.department, p.is_active, p.last_login, p.created_at;