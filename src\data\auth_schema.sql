-- Struktur Tabel untuk Autentikasi User dan Role

-- Tabel Users
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100),
    avatar_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Roles
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Permissions
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(255),
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Role_Permissions (Many-to-Many)
CREATE TABLE role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- Tabel User_Roles (Many-to-Many)
CREATE TABLE user_roles (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Tabel untuk Session Management
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabel untuk Password Reset
CREATE TABLE password_reset_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Contoh Data untuk Roles
INSERT INTO roles (name, description) VALUES
('admin', 'Administrator with full system access'),
('manager', 'Manager with access to management and reporting features'),
('technician', 'Technician with access to maintenance and repair features'),
('viewer', 'User with view-only access to data');

-- Contoh Data untuk Permissions
INSERT INTO permissions (name, description, resource, action) VALUES
('view_dashboard', 'View dashboard', 'dashboard', 'view'),
('manage_users', 'Manage users', 'users', 'manage'),
('view_tickets', 'View tickets', 'tickets', 'view'),
('create_tickets', 'Create new tickets', 'tickets', 'create'),
('update_tickets', 'Update tickets', 'tickets', 'update'),
('delete_tickets', 'Delete tickets', 'tickets', 'delete'),
('view_maintenance', 'View maintenance schedule', 'maintenance', 'view'),
('schedule_maintenance', 'Schedule maintenance', 'maintenance', 'create'),
('update_maintenance', 'Update maintenance schedule', 'maintenance', 'update'),
('view_reports', 'View reports', 'reports', 'view'),
('generate_reports', 'Generate reports', 'reports', 'create');

-- Contoh Relasi Role-Permission
INSERT INTO role_permissions (role_id, permission_id) VALUES
-- Admin memiliki semua permission
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10), (1, 11),
-- Manager
(2, 1), (2, 3), (2, 4), (2, 5), (2, 7), (2, 8), (2, 9), (2, 10), (2, 11),
-- Technician
(3, 1), (3, 3), (3, 4), (3, 5), (3, 7), (3, 8), (3, 9),
-- Viewer
(4, 1), (4, 3), (4, 7), (4, 10);