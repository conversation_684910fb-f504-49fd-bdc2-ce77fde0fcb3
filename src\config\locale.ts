// Global locale configuration
export const LOCALE_CONFIG = {
  // Default locale for the application
  DEFAULT_LOCALE: 'en-US',
  
  // Date and time formatting
  DATE_FORMAT: {
    locale: 'en-US',
    options: {
      year: 'numeric' as const,
      month: 'long' as const,
      day: 'numeric' as const,
      hour: '2-digit' as const,
      minute: '2-digit' as const
    }
  },
  
  // Short date format
  SHORT_DATE_FORMAT: {
    locale: 'en-US',
    options: {
      year: 'numeric' as const,
      month: 'short' as const,
      day: 'numeric' as const
    }
  },
  
  // Time only format
  TIME_FORMAT: {
    locale: 'en-US',
    options: {
      hour: '2-digit' as const,
      minute: '2-digit' as const,
      hour12: true
    }
  },
  
  // Number formatting
  NUMBER_FORMAT: {
    locale: 'en-US',
    options: {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }
  },
  
  // Currency formatting (if needed)
  CURRENCY_FORMAT: {
    locale: 'en-US',
    options: {
      style: 'currency' as const,
      currency: 'USD'
    }
  }
};

// Utility functions for consistent formatting
export const formatDateTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString(LOCALE_CONFIG.DATE_FORMAT.locale, LOCALE_CONFIG.DATE_FORMAT.options);
};

export const formatDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(LOCALE_CONFIG.SHORT_DATE_FORMAT.locale, LOCALE_CONFIG.SHORT_DATE_FORMAT.options);
};

export const formatTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleTimeString(LOCALE_CONFIG.TIME_FORMAT.locale, LOCALE_CONFIG.TIME_FORMAT.options);
};

export const formatNumber = (num: number): string => {
  return num.toLocaleString(LOCALE_CONFIG.NUMBER_FORMAT.locale, LOCALE_CONFIG.NUMBER_FORMAT.options);
};

// Text constants in English
export const TEXT_CONSTANTS = {
  // Common actions
  ACTIONS: {
    ADD: 'Add',
    EDIT: 'Edit',
    DELETE: 'Delete',
    SAVE: 'Save',
    CANCEL: 'Cancel',
    UPDATE: 'Update',
    VIEW: 'View',
    PRINT: 'Print',
    EXPORT: 'Export',
    IMPORT: 'Import',
    SEARCH: 'Search',
    FILTER: 'Filter',
    CLEAR: 'Clear',
    SELECT_ALL: 'Select All',
    SELECT_MULTIPLE: 'Select Multiple'
  },
  
  // Status labels
  STATUS: {
    ACTIVE: 'Active',
    INACTIVE: 'Inactive',
    PENDING: 'Pending',
    COMPLETED: 'Completed',
    CANCELLED: 'Cancelled',
    OPERATIONAL: 'Operational',
    DOWN: 'Down',
    MAINTENANCE: 'Maintenance'
  },
  
  // Condition labels
  CONDITION: {
    EXCELLENT: 'Excellent',
    GOOD: 'Good',
    FAIR: 'Fair',
    POOR: 'Poor'
  },
  
  // Asset types
  ASSET_TYPES: {
    HANDHOLE: 'Handhole',
    POLE: 'Pole',
    JUNCTION_BOX: 'Junction Box',
    FIBER_CABLE: 'Fiber Optic Cable',
    SPLICE_CLOSURE: 'Splice Closure',
    ODF: 'ODF'
  },
  
  // Photo related
  PHOTOS: {
    UPLOAD_PHOTOS: 'Upload Photos',
    PHOTO_PREVIEW: 'Photo Preview',
    VIEW_ALL: 'View All',
    NO_PHOTOS: 'No photos available',
    PHOTOS_AVAILABLE: 'photo(s) available'
  },
  
  // Reports
  REPORTS: {
    ASSET_DETAIL_REPORT: 'ASSET DETAIL REPORT',
    ASSET_SUMMARY_REPORT: 'ASSET SUMMARY REPORT',
    GENERATED_ON: 'Generated on',
    REPORT_GENERATED_FOR: 'Report generated for',
    ASSET_MANAGEMENT_SYSTEM: 'Asset Management System',
    DETAILED_REPORT: 'Detailed Report',
    SUMMARY_REPORT: 'Summary Report'
  },
  
  // Form labels
  FORM: {
    NAME: 'Name',
    CATEGORY: 'Category',
    BRAND: 'Brand',
    MODEL: 'Model',
    CONDITION: 'Condition',
    LOCATION: 'Location',
    INSTALLATION_DATE: 'Installation Date',
    LAST_MAINTENANCE: 'Last Maintenance',
    NOTES: 'Notes',
    PHOTOS: 'Photos'
  },
  
  // Messages
  MESSAGES: {
    NO_DATA: 'No data available',
    LOADING: 'Loading...',
    ERROR: 'An error occurred',
    SUCCESS: 'Operation completed successfully',
    CONFIRM_DELETE: 'Are you sure you want to delete this item?'
  }
};

export default LOCALE_CONFIG;
