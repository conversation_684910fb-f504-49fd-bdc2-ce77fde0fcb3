"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import { format } from "date-fns";

interface RouteSLAData {
  date: string;
  [key: string]: number | string; // Dynamic key for route SLA
}

// Configuration for each route, including base SLA and fluctuation
// All baseSLA are set below 15 hours
const routeConfigs = [
  { name: "Route JKT-001", baseSLA: 14.5, fluctuation: 1.0 },
  { name: "Route TGR-002", baseSLA: 13.0, fluctuation: 1.5 },
  { name: "Route BKS-003", baseSLA: 10.0, fluctuation: 2.0 },
  { name: "Route BDG-004", baseSLA: 14.0, fluctuation: 1.2 },
  { name: "Route SBY-005", baseSLA: 11.5, fluctuation: 1.8 },
  { name: "Route DPS-006", baseSLA: 8.0, fluctuation: 2.5 }
];

// Function to generate sample SLA data for 30 days
const generateSampleSLAData = (): RouteSLAData[] => {
  const data: RouteSLAData[] = [];
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30); // Start 30 days ago

  for (let i = 0; i < 30; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);
    const dateString = format(currentDate, "MMM dd");

    const dayData: RouteSLAData = { date: dateString };

    routeConfigs.forEach(routeConfig => {
      // Generate SLA values around baseSLA with fluctuation
      let slaHours = routeConfig.baseSLA + (Math.random() - 0.5) * routeConfig.fluctuation * 2;
      // Ensure SLA value is not less than 0 and not more than 24
      slaHours = Math.max(0, Math.min(24, slaHours));
      dayData[routeConfig.name] = parseFloat(slaHours.toFixed(1)); // Format to one decimal place
    });
    data.push(dayData);
  }
  return data;
};

const sampleRouteSLAData = generateSampleSLAData();
const routeNames = routeConfigs.map(config => config.name);

// Different colors for each route line: green, blue, yellow, red, purple, dark gray
const colors = [
  "hsl(142.1 76.2% 36.3%)", // Green
    "hsl(217.2 91.2% 59.8%)", // Blue
    "hsl(48 96% 50%)",       // Yellow
    "hsl(0 84.2% 60.2%)",    // Red
    "hsl(262.1 83.3% 57.2%)", // Purple
    "hsl(240 5.9% 10%)"      // Dark Gray
];

const RouteSLAChart: React.FC = () => {
  return (
    <Card className="col-span-1 lg:col-span-3">
      <CardHeader>
        <CardTitle className="text-xl">Route SLA Performance (Last 30 Days)</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full"> {/* Tinggi diubah menjadi 400px */}
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={sampleRouteSLAData}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5, // Bottom margin can be adjusted if more space is needed
              }}
            >
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis dataKey="date" stroke="hsl(var(--foreground))" />
              <YAxis
                stroke="hsl(var(--foreground))"
                label={{ value: "SLA (hour)", angle: -90, position: "insideLeft", fill: "hsl(var(--foreground))" }}
                domain={[0, 24]} // Ensure Y-axis from 0 to 24 hours
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "hsl(var(--card))",
                  borderColor: "hsl(var(--border))",
                  borderRadius: "0.5rem",
                }}
                labelStyle={{ color: "hsl(var(--foreground))" }}
                itemStyle={{ color: "hsl(var(--foreground))" }}
              />
              <Legend />
              {routeNames.map((route, index) => (
                <Line
                  key={route}
                  type="monotone"
                  dataKey={route}
                  stroke={colors[index % colors.length]}
                  activeDot={{ r: 5 }}
                  strokeWidth={2}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default RouteSLAChart;