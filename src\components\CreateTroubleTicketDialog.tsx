"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { showSuccess } from "@/utils/toast";
import PhotoUpload from "@/components/PhotoUpload";

// Define the schema for a new trouble ticket
const newTicketSchema = z.object({
  title: z.string().min(5, { message: "Title must be at least 5 characters." }),
  description: z.string().min(10, { message: "Description must be at least 10 characters." }),
  priority: z.enum(["high", "medium", "low"], { message: "Invalid priority selected." }),
  assignedTo: z.string().min(2, { message: "Assigned to field cannot be empty." }),
  routeName: z.string().min(2, { message: "Route name cannot be empty." }), // New field
  repairType: z.string().min(1, { message: "Repair type must be selected." }), // New field for repair type
  repairNature: z.enum(["permanent", "temporary"], { message: "Repair nature must be selected." }), // New field for repair nature
  temporaryReason: z.string().optional(), // New field for temporary reason
}).refine((data) => {
  if (data.repairNature === "temporary" && (!data.temporaryReason || data.temporaryReason.trim() === "")) {
    return false;
  }
  return true;
}, {
  message: "Reason must be provided for temporary repair",
  path: ["temporaryReason"],
});

interface CreateTroubleTicketDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate: (ticket: {
    title: string;
    description: string;
    priority: "high" | "medium" | "low";
    assignedTo: string;
    routeName: string; // New field
    repairType: string; // New field for repair type
    repairNature: "permanent" | "temporary"; // New field for repair nature
    temporaryReason?: string; // New field for temporary reason
    photos?: string[]; // New field for photos
  }) => void;
}

const CreateTroubleTicketDialog: React.FC<CreateTroubleTicketDialogProps> = ({
  isOpen,
  onClose,
  onCreate,
}) => {
  const [photos, setPhotos] = useState<File[]>([]);

  const form = useForm<z.infer<typeof newTicketSchema>>({
    resolver: zodResolver(newTicketSchema),
    defaultValues: {
      title: "",
      description: "",
      priority: "medium", // Default priority
      assignedTo: "",
      routeName: "", // Default route name
      repairType: "", // Default repair type
      repairNature: "permanent", // Default repair nature
      temporaryReason: "", // Default temporary reason
    },
  });

  // Helper function to convert File to base64
  const convertFilesToBase64 = async (files: File[]): Promise<string[]> => {
    const promises = files.map(file => {
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    });
    return Promise.all(promises);
  };

  const handleSubmit = async (values: z.infer<typeof newTicketSchema>) => {
    const photoBase64 = await convertFilesToBase64(photos);
    // Explicitly create an object to ensure correct type inference for onCreate
    const ticketData = {
      title: values.title,
      description: values.description,
      priority: values.priority,
      assignedTo: values.assignedTo,
      routeName: values.routeName, // Pass routeName
      repairType: values.repairType, // Pass repairType
      repairNature: values.repairNature, // Pass repairNature
      temporaryReason: values.temporaryReason, // Pass temporaryReason
      photos: photoBase64, // Pass photos
    };
    onCreate(ticketData);
    showSuccess("New trouble ticket created!");
    form.reset(); // Reset form after submission
    setPhotos([]); // Reset photos
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Create New Trouble Ticket</DialogTitle>
          <DialogDescription>
            Fill in the details for the new trouble ticket.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="title">
              Title
            </Label>
            <Input id="title" {...form.register("title")} />
            {form.formState.errors.title && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.title.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">
              Description
            </Label>
            <Textarea id="description" {...form.register("description")} rows={4} />
            {form.formState.errors.description && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.description.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="priority">
              Priority
            </Label>
            <Select onValueChange={(value) => form.setValue("priority", value as "high" | "medium" | "low")} defaultValue={form.getValues("priority")}>
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.priority && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.priority.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="assignedTo">
              Assigned To
            </Label>
            <Input id="assignedTo" {...form.register("assignedTo")} />
            {form.formState.errors.assignedTo && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.assignedTo.message}</p>
            )}
          </div>
          <div className="space-y-2"> {/* New field for route name */}
            <Label htmlFor="routeName">
              Route Name
            </Label>
            <Input id="routeName" {...form.register("routeName")} />
            {form.formState.errors.routeName && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.routeName.message}</p>
            )}
          </div>
          <div className="space-y-2"> {/* New field for repair type */}
            <Label htmlFor="repairType">
              Repair Type
            </Label>
            <Select onValueChange={(value) => form.setValue("repairType", value)} defaultValue={form.getValues("repairType")}>
              <SelectTrigger>
                <SelectValue placeholder="Select repair type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fiber-cut">Fiber Cut</SelectItem>
                <SelectItem value="power-outage">Power Outage</SelectItem>
                <SelectItem value="equipment-failure">Equipment Failure</SelectItem>
                <SelectItem value="configuration-error">Configuration Error</SelectItem>
                <SelectItem value="connectivity-issue">Connectivity Issue</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="upgrade">Upgrade</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.repairType && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.repairType.message}</p>
            )}
          </div>
          <div className="space-y-2"> {/* New field for repair nature */}
            <Label htmlFor="repairNature">
              Repair Nature
            </Label>
            <Select onValueChange={(value) => form.setValue("repairNature", value as "permanent" | "temporary")} defaultValue={form.getValues("repairNature")}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select repair nature" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="permanent">Permanent</SelectItem>
                <SelectItem value="temporary">Temporary</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.repairNature && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.repairNature.message}</p>
            )}
          </div>
          {form.watch("repairNature") === "temporary" && (
            <div className="space-y-2"> {/* Conditional field for temporary reason */}
              <Label htmlFor="temporaryReason">
                    Temporary Reason
                  </Label>
                  <Textarea id="temporaryReason" {...form.register("temporaryReason")} rows={3} placeholder="Explain why the repair is temporary" />
              {form.formState.errors.temporaryReason && (
                <p className="text-sm text-red-500 mt-1">{form.formState.errors.temporaryReason.message}</p>
              )}
            </div>
          )}
          <div className="space-y-2">
            <Label>Trouble Ticket Photos (Maximum 10 photos)</Label>
            <PhotoUpload
              photos={photos}
              onPhotosChange={setPhotos}
              maxPhotos={10}
            />
          </div>
          <DialogFooter>
            <Button type="submit">Create Ticket</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTroubleTicketDialog;