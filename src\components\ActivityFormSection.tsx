"use client";

import React from "react";
import { useFormContext, useFieldArray } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, PlusCircle } from "lucide-react";
import * as z from "zod";

// Define Zod schema for Material (re-defined here for local scope, or import if shared)
// Removed materialSchema and closureDetailSchema as they are no longer used here.

// Define Activity schema for Zod (material fields removed)
const activitySchema = z.object({
  name: z.string().min(1, { message: "Activity name cannot be empty." }),
  estimatedDuration: z.coerce.number().min(0, { message: "Must be non-negative." }),
  realDuration: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().min(0, { message: "Must be non-negative." }).optional()
  ),
  pic: z.string().optional(),
  result: z.string().optional(),
  notes: z.string().optional(),
  // Removed material-related fields from activitySchema
});

interface ActivityFormSectionProps {
  activityIndex: number;
  activityName: string;
}

const ActivityFormSection: React.FC<ActivityFormSectionProps> = ({ activityIndex, activityName }) => {
  const { control, register, formState, watch } = useFormContext();
  const { errors } = formState;

  // Removed useFieldArray for materials and closure splicing as they are no longer per activity.

  const currentActivity = watch(`activities.${activityIndex}`);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-2 border p-3 rounded-md mb-3">
      <Label className="col-span-4 font-medium text-left mb-2">{activityName}</Label>
      
      <Label htmlFor={`activities.${activityIndex}.estimatedDuration`} className="text-right md:text-left">
        Est. Duration (min)
      </Label>
      <Input
        id={`activities.${activityIndex}.estimatedDuration`}
        type="number"
        {...register(`activities.${activityIndex}.estimatedDuration`, { valueAsNumber: true })}
        className="col-span-3"
      />
      {errors.activities?.[activityIndex]?.estimatedDuration && (
        <p className="col-span-4 text-right text-sm text-red-500">{errors.activities[activityIndex]?.estimatedDuration?.message}</p>
      )}

      <Label htmlFor={`activities.${activityIndex}.realDuration`} className="text-right md:text-left">
        Real Duration (min)
      </Label>
      <Input
        id={`activities.${activityIndex}.realDuration`}
        type="number"
        placeholder="N/A"
        {...register(`activities.${activityIndex}.realDuration`)}
        value={currentActivity.realDuration ?? ""}
        className="col-span-3"
      />
      {errors.activities?.[activityIndex]?.realDuration && (
        <p className="col-span-4 text-right text-sm text-red-500">{errors.activities[activityIndex]?.realDuration?.message}</p>
      )}

      <Label htmlFor={`activities.${activityIndex}.pic`} className="text-right md:text-left">
        PIC
      </Label>
      <Input
        id={`activities.${activityIndex}.pic`}
        placeholder="Person In Charge"
        {...register(`activities.${activityIndex}.pic`)}
        className="col-span-3"
      />
      {errors.activities?.[activityIndex]?.pic && (
        <p className="col-span-4 text-right text-sm text-red-500">{errors.activities[activityIndex]?.pic?.message}</p>
      )}

      <Label htmlFor={`activities.${activityIndex}.result`} className="text-right md:text-left">
        Result
      </Label>
      <Input
        id={`activities.${activityIndex}.result`}
        placeholder="Activity result"
        {...register(`activities.${activityIndex}.result`)}
        className="col-span-3"
      />
      {errors.activities?.[activityIndex]?.result && (
        <p className="col-span-4 text-right text-sm text-red-500">{errors.activities[activityIndex]?.result?.message}</p>
      )}

      <Label htmlFor={`activities.${activityIndex}.notes`} className="text-right md:text-left">
        Notes
      </Label>
      <Textarea
        id={`activities.${activityIndex}.notes`}
        placeholder="Additional notes"
        {...register(`activities.${activityIndex}.notes`)}
        rows={2}
        className="col-span-3"
      />
      {errors.activities?.[activityIndex]?.notes && (
        <p className="col-span-4 text-right text-sm text-red-500">{errors.activities[activityIndex]?.notes?.message}</p>
      )}

      {/* Material Usage Section - Removed from here */}
      {/* Other Material Counts - Removed from here */}
      {/* Closure Splicing Details Section - Removed from here */}
    </div>
  );
};

export default ActivityFormSection;