"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { ArrowRight, ArrowLeft } from "lucide-react";

interface CoreNetworkFlowCardProps {
  routeName: string; // New prop for route name
}

const CoreNetworkFlowCard: React.FC<CoreNetworkFlowCardProps> = ({ routeName }) => {
  return (
    <Card className="flex flex-col items-center justify-center">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-primary">{routeName} Flow</CardTitle>
      </CardHeader>
      <CardContent className="relative flex flex-col md:flex-row items-center justify-center gap-8 md:gap-16 w-full">
        {/* Card 1: Data Ingress Point */}
        <Card className="w-full md:w-80 p-6 text-center shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl">Data Ingress Point</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Entry point for all network traffic from access layers.</p>
          </CardContent>
        </Card>

        {/* Arrows Container */}
        <div className="relative flex flex-col items-center justify-center w-full md:w-40 h-32 md:h-auto">
          {/* Top Arrow (Right) */}
          <div className="relative w-full h-1 bg-blue-500 rounded-full mb-4 md:mb-0 md:absolute md:top-8 md:left-0">
            <ArrowRight className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 text-blue-500" />
          </div>
          {/* Bottom Arrow (Left) */}
          <div className="relative w-full h-1 bg-green-500 rounded-full mt-4 md:mt-0 md:absolute md:bottom-8 md:left-0">
            <ArrowLeft className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 text-green-500" />
          </div>
        </div>

        {/* Card 2: Core Processing Unit */}
        <Card className="w-full md:w-80 p-6 text-center shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl">Core Processing Unit</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">High-speed data processing, routing, and aggregation.</p>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  );
};

export default CoreNetworkFlowCard;