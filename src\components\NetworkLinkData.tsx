"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";


interface NetworkLink {
  id: string;
  name: string;
  segment: string;
  assets: string[];
  bandwidth: string;
  status: "active" | "warning" | "critical";
  coordinates?: [number, number]; // Optional coordinates for map
}

const sampleLinks: NetworkLink[] = [
  {
    id: "FR-001",
    name: "Main Backbone Link A",
    segment: "Core-East",
    assets: ["OLT-01", "SP-101", "SP-102"],
    bandwidth: "85%",
    status: "active",
    coordinates: [-6.2088, 106.8456], // Jakarta
  },
  {
    id: "FR-002",
    name: "Metro Ring Link B",
    segment: "Metro-North",
    assets: ["OLT-02", "SP-103"],
    bandwidth: "60%",
    status: "active",
    coordinates: [-6.1754, 106.8272], // Central Jakarta
  },
  {
    id: "FR-003",
    name: "Rural Extension C",
    segment: "Access-South",
    assets: ["OLT-03", "SP-104", "SP-105"],
    bandwidth: "95%",
    status: "warning",
    coordinates: [-6.2297, 106.6479], // Tangerang
  },
  {
    id: "FR-004",
    name: "Data Center Link D",
    segment: "Core-West",
    assets: ["OLT-04", "SP-106"],
    bandwidth: "99%",
    status: "critical",
    coordinates: [-6.2383, 107.0000], // Bekasi
  },
  {
    id: "FR-005",
    name: "Business Park Link E",
    segment: "Access-Central",
    assets: ["OLT-05", "SP-107"],
    bandwidth: "70%",
    status: "active",
    coordinates: [-6.2692, 106.7892], // South Jakarta
  },
];

const NetworkLinkData: React.FC = () => {
  const [searchTerm, setSearchTerm] = React.useState("");

  const getStatusColor = (status: NetworkLink["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-500 hover:bg-green-600";
      case "warning":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "critical":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500";
    }
  };

  const filteredLinks = sampleLinks.filter(
    (link) =>
      link.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      link.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Card className="col-span-1 md:col-span-2 lg:col-span-3">
      <CardHeader>
        <CardTitle className="text-xl">Network Link Data</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 gap-6">
        {/* Map section removed */}
        <div className="overflow-auto max-h-96">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search links by ID or name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 pr-3"
            />
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Route ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Segment</TableHead>
                <TableHead>Bandwidth</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLinks.map((link) => (
                <TableRow key={link.id}>
                  <TableCell className="font-medium">{link.id}</TableCell>
                  <TableCell>{link.name}</TableCell>
                  <TableCell>{link.segment}</TableCell>
                  <TableCell>{link.bandwidth}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(link.status)}>
                      {link.status.charAt(0).toUpperCase() + link.status.slice(1)}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default NetworkLinkData;