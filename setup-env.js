#!/usr/bin/env node

// Script untuk setup environment variables
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🔧 MS Dashboard - Supabase Environment Setup\n');

async function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupEnvironment() {
  try {
    console.log('This script will help you configure Supabase environment variables.\n');
    
    // Check if .env.local already exists
    const envPath = path.join(process.cwd(), '.env.local');
    if (fs.existsSync(envPath)) {
      const overwrite = await question('⚠️  .env.local already exists. Overwrite? (y/N): ');
      if (overwrite.toLowerCase() !== 'y') {
        console.log('Setup cancelled.');
        rl.close();
        return;
      }
    }
    
    console.log('\n📋 Please provide your Supabase project details:');
    console.log('   You can find these in your Supabase Dashboard > Settings > API\n');
    
    // Get Supabase URL
    const supabaseUrl = await question('🔗 Supabase Project URL: ');
    if (!supabaseUrl || !supabaseUrl.includes('supabase.co')) {
      console.log('❌ Invalid Supabase URL. Should be like: https://your-project.supabase.co');
      rl.close();
      return;
    }
    
    // Get Anon Key
    const anonKey = await question('🔑 Supabase Anon Key: ');
    if (!anonKey || anonKey.length < 100) {
      console.log('❌ Invalid Anon Key. Should be a long string (100+ characters)');
      rl.close();
      return;
    }
    
    // Optional Service Role Key
    const serviceKey = await question('🔐 Service Role Key (optional, press Enter to skip): ');
    
    // Create .env.local content
    let envContent = `# Supabase Configuration
# Generated by setup-env.js on ${new Date().toISOString()}

# Supabase Project URL
VITE_SUPABASE_URL=${supabaseUrl}

# Supabase Anon Key
VITE_SUPABASE_ANON_KEY=${anonKey}
`;

    if (serviceKey) {
      envContent += `
# Supabase Service Role Key (for admin operations)
SUPABASE_SERVICE_ROLE_KEY=${serviceKey}
`;
    }
    
    // Write .env.local file
    fs.writeFileSync(envPath, envContent);
    
    console.log('\n✅ Environment variables configured successfully!');
    console.log('📁 Created: .env.local');
    console.log('\n🚀 Next steps:');
    console.log('   1. Restart your development server (npm run dev)');
    console.log('   2. Setup your Supabase database schema');
    console.log('   3. Test the authentication flow');
    console.log('\n📖 For detailed setup instructions, see:');
    console.log('   - src/data/supabase-setup.md');
    console.log('   - src/data/README-AUTH.md');
    
  } catch (error) {
    console.error('❌ Error setting up environment:', error.message);
  } finally {
    rl.close();
  }
}

// Run setup
setupEnvironment();
