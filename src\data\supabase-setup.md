# Setup Database Auth untuk MS Dashboard di Supabase

## Langkah-langkah Setup

### 1. Persiapan Supabase Project

1. **Buat project baru di Supabase**
   - Kunjungi [supabase.com](https://supabase.com)
   - <PERSON>lik "New Project"
   - Pilih organization dan beri nama project
   - Pilih region terdekat (Singapore untuk Indonesia)
   - Set password database yang kuat

2. **Aktifkan Authentication**
   - Di dashboard Supabase, buka menu "Authentication"
   - Authentication sudah aktif secara default
   - Konfigurasi providers yang di<PERSON><PERSON>an (Email, Google, dll)

### 2. Eksekusi Schema Database

1. **Buka SQL Editor**
   - Di dashboard Supabase, klik "SQL Editor"
   - Klik "New Query"

2. **Jalankan Schema**
   - Copy seluruh isi file `auth_schema.sql`
   - Paste ke SQL Editor
   - Klik "Run" untuk eksekusi

### 3. Konfigurasi Environment Variables

Buat file `.env.local` di root project dengan:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Install Supabase Client

```bash
npm install @supabase/supabase-js
```

### 5. Setup Supabase Client

Buat file `src/lib/supabase.ts`:

```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

## Struktur Database yang Dibuat

### Tabel Utama

1. **profiles** - Extend dari auth.users
   - Menyimpan data tambahan user (username, full_name, department, dll)
   - Auto-created saat user register

2. **roles** - Daftar role sistem
   - admin, manager, technician, operator, viewer

3. **permissions** - Daftar permission
   - Granular permissions untuk setiap fitur

4. **user_roles** - Relasi user dengan roles
5. **role_permissions** - Relasi role dengan permissions
6. **audit_logs** - Log aktivitas user

### Functions yang Tersedia

1. **user_has_permission(user_uuid, permission_name)** - Check permission user
2. **get_user_roles(user_uuid)** - Get roles user
3. **handle_new_user()** - Auto-create profile saat register

### Views yang Tersedia

1. **user_permissions_view** - View lengkap user dengan roles dan permissions
2. **user_roles_summary** - Summary roles per user

## Row Level Security (RLS)

Semua tabel sudah dikonfigurasi dengan RLS untuk keamanan:

- User hanya bisa melihat/edit data sendiri
- Admin bisa melihat/edit semua data
- Policies sudah dikonfigurasi sesuai role

## Cara Penggunaan

### 1. Register User Baru

```typescript
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password',
  options: {
    data: {
      full_name: 'John Doe',
      username: 'johndoe'
    }
  }
})
```

### 2. Login User

```typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})
```

### 3. Assign Role ke User

```typescript
// Hanya admin yang bisa melakukan ini
const { data, error } = await supabase
  .from('user_roles')
  .insert({
    user_id: 'user-uuid',
    role_id: 'role-uuid'
  })
```

### 4. Check Permission User

```typescript
const { data, error } = await supabase
  .rpc('user_has_permission', {
    user_uuid: 'user-uuid',
    permission_name: 'view_dashboard'
  })
```

### 5. Get User Roles

```typescript
const { data, error } = await supabase
  .rpc('get_user_roles', {
    user_uuid: 'user-uuid'
  })
```

## Testing

Setelah setup selesai, test dengan:

1. Register user baru
2. Login dengan user tersebut
3. Assign role ke user (sebagai admin)
4. Test permissions

## Troubleshooting

### Error: relation "auth.users" does not exist
- Pastikan Authentication sudah aktif di Supabase
- Coba refresh browser dan jalankan ulang schema

### Error: permission denied for schema public
- Pastikan menggunakan service role key untuk setup awal
- Atau jalankan schema melalui SQL Editor di dashboard

### RLS Policy Error
- Pastikan user sudah memiliki role yang sesuai
- Check apakah policies sudah benar

## Security Best Practices

1. **Gunakan RLS** - Semua tabel sudah dikonfigurasi dengan RLS
2. **Principle of Least Privilege** - Berikan permission minimal yang dibutuhkan
3. **Audit Logs** - Semua aktivitas penting dicatat di audit_logs
4. **Strong Passwords** - Enforce password policy di Supabase Auth
5. **Environment Variables** - Jangan commit API keys ke repository

## Maintenance

### Backup Database
```sql
-- Export schema dan data
pg_dump -h your-db-host -U postgres -d postgres > backup.sql
```

### Monitor Performance
- Check slow queries di Supabase dashboard
- Monitor RLS policy performance
- Review audit logs secara berkala

### Update Permissions
Saat menambah fitur baru:
1. Tambah permission baru ke tabel permissions
2. Assign ke role yang sesuai di role_permissions
3. Update aplikasi untuk check permission baru
