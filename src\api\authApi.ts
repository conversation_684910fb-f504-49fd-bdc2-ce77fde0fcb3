import { AuthCredentials, AuthResponse, User, Role, Permission } from '../types/auth';

// Base API URL (replace with actual URL during implementation)
const API_BASE_URL = '/api';

/**
 * Function to perform login
 * @param credentials Login credentials (username and password)
 * @returns Promise with authentication response
 */
export const login = async (credentials: AuthCredentials): Promise<AuthResponse> => {
  try {
    // Actual implementation will use fetch or axios to call API
    // Example implementation with fetch:
    // const response = await fetch(`${API_BASE_URL}/auth/login`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify(credentials),
    // });
    // 
    // if (!response.ok) {
    //   const errorData = await response.json();
    //   throw new Error(errorData.message || 'Login failed');
    // }
    // 
    // return await response.json();

    // For now, use sample data
    return await mockLogin(credentials);
  } catch (error) {
    console.error('Login API error:', error);
    throw error;
  }
};

/**
 * Function to perform logout
 * @param token Authentication token
 * @returns Promise void
 */
export const logout = async (token: string): Promise<void> => {
  try {
    // Actual implementation will use fetch or axios to call API
    // Example implementation with fetch:
    // const response = await fetch(`${API_BASE_URL}/auth/logout`, {
    //   method: 'POST',
    //   headers: {
    //     'Authorization': `Bearer ${token}`,
    //     'Content-Type': 'application/json',
    //   },
    // });
    // 
    // if (!response.ok) {
    //   const errorData = await response.json();
    //   throw new Error(errorData.message || 'Logout failed');
    // }

    // For now, use sample data
    await mockLogout();
  } catch (error) {
    console.error('Logout API error:', error);
    throw error;
  }
};

/**
 * Function to get current user data
 * @param token Authentication token
 * @returns Promise with user data
 */
export const getCurrentUser = async (token: string): Promise<Omit<User, 'password_hash'>> => {
  try {
    // Actual implementation will use fetch or axios to call API
    // Example implementation with fetch:
    // const response = await fetch(`${API_BASE_URL}/auth/me`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${token}`,
    //     'Content-Type': 'application/json',
    //   },
    // });
    // 
    // if (!response.ok) {
    //   const errorData = await response.json();
    //   throw new Error(errorData.message || 'Failed to get user data');
    // }
    // 
    // return await response.json();

    // For now, use sample data
    return await mockGetCurrentUser();
  } catch (error) {
    console.error('Get current user API error:', error);
    throw error;
  }
};

/**
 * Function to get current user roles
 * @param token Authentication token
 * @returns Promise with user roles list
 */
export const getUserRoles = async (token: string): Promise<Role[]> => {
  try {
    // Actual implementation will use fetch or axios to call API
    // Example implementation with fetch:
    // const response = await fetch(`${API_BASE_URL}/auth/roles`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${token}`,
    //     'Content-Type': 'application/json',
    //   },
    // });
    // 
    // if (!response.ok) {
    //   const errorData = await response.json();
    //   throw new Error(errorData.message || 'Failed to get user roles');
    // }
    // 
    // return await response.json();

    // For now, use sample data
    return await mockGetUserRoles();
  } catch (error) {
    console.error('Get user roles API error:', error);
    throw error;
  }
};

/**
 * Function to get current user permissions
 * @param token Authentication token
 * @returns Promise with user permissions list
 */
export const getUserPermissions = async (token: string): Promise<string[]> => {
  try {
    // Actual implementation will use fetch or axios to call API
    // Example implementation with fetch:
    // const response = await fetch(`${API_BASE_URL}/auth/permissions`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${token}`,
    //     'Content-Type': 'application/json',
    //   },
    // });
    // 
    // if (!response.ok) {
    //   const errorData = await response.json();
    //   throw new Error(errorData.message || 'Failed to get user permissions');
    // }
    // 
    // return await response.json();

    // For now, use sample data
    return await mockGetUserPermissions();
  } catch (error) {
    console.error('Get user permissions API error:', error);
    throw error;
  }
};

// Mock functions for API simulation

const mockLogin = async (credentials: AuthCredentials): Promise<AuthResponse> => {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Check credentials
  if (!credentials.username || !credentials.password) {
    throw new Error('Username and password are required');
  }

  // Validate admin credentials
  if (credentials.username === 'admin' && credentials.password === 'admin') {
    // Create response for admin
    const response: AuthResponse = {
      user: {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        full_name: 'Administrator',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
        last_login: new Date(),
      },
      token: 'mock_jwt_token_admin',
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 jam
    };
    return response;
  }

  // If credentials are invalid
  throw new Error('Invalid username or password');
};

const mockLogout = async (): Promise<void> => {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  // Nothing needs to be returned for logout
  return;
};

const mockGetCurrentUser = async (): Promise<Omit<User, 'password_hash'>> => {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  // Create user data
  const user: Omit<User, 'password_hash'> = {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: 'Administrator',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
    last_login: new Date(),
  };

  return user;
};

const mockGetUserRoles = async (): Promise<Role[]> => {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  // Create roles list
  const roles: Role[] = [
    {
      id: 1,
      name: 'admin',
      description: 'Administrator with full system access',
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  return roles;
};

const mockGetUserPermissions = async (): Promise<string[]> => {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  // Create permissions list
  const permissions: string[] = [
    'view_dashboard',
    'manage_users',
    'view_tickets',
    'create_tickets',
    'update_tickets',
    'delete_tickets',
    'view_maintenance',
    'schedule_maintenance',
    'update_maintenance',
    'view_reports',
    'generate_reports',
  ];

  return permissions;
};