import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "./ui/dialog";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Badge } from "./ui/badge";
import { AssetDetail } from "../data/networkRoutes";
import { Package, Save, X, MapPin, Calendar, Trash2, Eye } from "lucide-react";
import PhotoUpload from './PhotoUpload';

interface EditAssetDialogProps {
  isOpen: boolean;
  onClose: () => void;
  assetDetail: AssetDetail | null;
  onSave: (updatedAsset: AssetDetail) => void;
}

interface FormData {
  name: string;
  category: string;
  brand: string;
  model: string;
  condition: string;
  area: string;
  installationDate: string;
  lastMaintenance: string;
  location: {
    address: string;
    latitude: number;
    longitude: number;
  };
  notes: string;
  serialNumber: string;
  warranty: string;
  photos: File[];
  existingPhotos: string[];
}

const conditionOptions = [
  { value: "excellent", label: "Excellent" },
  { value: "good", label: "Good" },
  { value: "fair", label: "Fair" },
  { value: "poor", label: "Poor" }
];

const categoryOptions = [
  { value: "fiber optic cable", label: "Fiber Optic Cable" },
  { value: "odf", label: "ODF" },
  { value: "handhole", label: "Handhole" },
  { value: "pole", label: "Pole" },
  { value: "jc", label: "Joint Closure" },
  { value: "cabinet", label: "Cabinet" },
  { value: "splitter", label: "Splitter" },
  { value: "patch cord", label: "Patch Cord" }
];

export default function EditAssetDialog({ isOpen, onClose, assetDetail, onSave }: EditAssetDialogProps) {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    category: "",
    brand: "",
    model: "",
    condition: "",
    area: "",
    installationDate: "",
    lastMaintenance: "",
    location: {
      address: "",
      latitude: 0,
      longitude: 0
    },
    notes: "",
    serialNumber: "",
    warranty: "",
    photos: [],
    existingPhotos: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewPhoto, setPreviewPhoto] = useState<string | null>(null);

  useEffect(() => {
    if (assetDetail) {
      setFormData({
        name: assetDetail.name || "",
        category: assetDetail.category || "",
        brand: assetDetail.brand || "",
        model: assetDetail.model || "",
        condition: assetDetail.condition || "",
        area: assetDetail.area || "",
        installationDate: assetDetail.installationDate ? new Date(assetDetail.installationDate).toISOString().split('T')[0] : "",
        lastMaintenance: assetDetail.lastMaintenance ? new Date(assetDetail.lastMaintenance).toISOString().split('T')[0] : "",
        location: {
          address: assetDetail.location?.address || "",
          latitude: assetDetail.location?.latitude || 0,
          longitude: assetDetail.location?.longitude || 0
        },
        notes: "",
        serialNumber: "",
        warranty: "",
        photos: [],
        existingPhotos: [
          ...(assetDetail.photos || []),
          ...(assetDetail.photo ? [assetDetail.photo] : [])
        ]
      });
    }
  }, [assetDetail]);

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof FormData],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handlePhotosChange = (photos: File[]) => {
    setFormData(prev => ({ ...prev, photos }));
  };

  const handleRemoveExistingPhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      existingPhotos: prev.existingPhotos.filter((_, i) => i !== index)
    }));
  };

  const handlePreviewPhoto = (photoUrl: string) => {
    setPreviewPhoto(photoUrl);
  };

  const closePreview = () => {
    setPreviewPhoto(null);
  };

  const convertFilesToBase64 = async (files: File[]): Promise<string[]> => {
    const promises = files.map(file => {
      return new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.readAsDataURL(file);
      });
    });
    return Promise.all(promises);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!assetDetail) return;

    setIsSubmitting(true);
    try {
      const photoBase64 = await convertFilesToBase64(formData.photos);
      const allPhotos = [...formData.existingPhotos, ...photoBase64];

      const updatedAsset: AssetDetail = {
        ...assetDetail,
        name: formData.name,
        category: formData.category,
        brand: formData.brand,
        model: formData.model,
        condition: formData.condition as "excellent" | "good" | "fair" | "poor",
        area: formData.area,
        installationDate: formData.installationDate,
        lastMaintenance: formData.lastMaintenance,
        location: formData.location,
        photo: allPhotos.length > 0 ? allPhotos[0] : undefined,
        photos: allPhotos
      };

      onSave(updatedAsset);
      onClose();
    } catch (error) {
      console.error('Error saving asset:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: "",
      category: "",
      brand: "",
      model: "",
      condition: "",
      area: "",
      installationDate: "",
      lastMaintenance: "",
      location: {
        address: "",
        latitude: 0,
        longitude: 0
      },
      notes: "",
      serialNumber: "",
      warranty: "",
      photos: [],
      existingPhotos: []
    });
    setPreviewPhoto(null);
    onClose();
  };

  if (!assetDetail) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Edit Asset - {assetDetail.name}</span>
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Asset Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter asset name"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="brand">Brand</Label>
                <Input
                  id="brand"
                  value={formData.brand}
                  onChange={(e) => handleInputChange('brand', e.target.value)}
                  placeholder="Enter brand"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Input
                  id="model"
                  value={formData.model}
                  onChange={(e) => handleInputChange('model', e.target.value)}
                  placeholder="Enter model"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="serialNumber">Serial Number</Label>
                <Input
                  id="serialNumber"
                  value={formData.serialNumber}
                  onChange={(e) => handleInputChange('serialNumber', e.target.value)}
                  placeholder="Enter serial number"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="condition">Condition *</Label>
                <Select value={formData.condition} onValueChange={(value) => handleInputChange('condition', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    {conditionOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Location Information</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={formData.location.address}
                  onChange={(e) => handleInputChange('location.address', e.target.value)}
                  placeholder="Enter full address"
                  rows={2}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="latitude">Latitude</Label>
                <Input
                  id="latitude"
                  type="number"
                  step="any"
                  value={formData.location.latitude}
                  onChange={(e) => handleInputChange('location.latitude', parseFloat(e.target.value) || 0)}
                  placeholder="Enter latitude"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="longitude">Longitude</Label>
                <Input
                  id="longitude"
                  type="number"
                  step="any"
                  value={formData.location.longitude}
                  onChange={(e) => handleInputChange('location.longitude', parseFloat(e.target.value) || 0)}
                  placeholder="Enter longitude"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="area">Area/Zone</Label>
                <Input
                  id="area"
                  value={formData.area}
                  onChange={(e) => handleInputChange('area', e.target.value)}
                  placeholder="Enter area or zone"
                />
              </div>
            </div>
          </div>

          {/* Dates and Maintenance */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Dates & Maintenance</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="installationDate">Installation Date</Label>
                <Input
                  id="installationDate"
                  type="date"
                  value={formData.installationDate}
                  onChange={(e) => handleInputChange('installationDate', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lastMaintenance">Last Maintenance</Label>
                <Input
                  id="lastMaintenance"
                  type="date"
                  value={formData.lastMaintenance}
                  onChange={(e) => handleInputChange('lastMaintenance', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="warranty">Warranty Until</Label>
                <Input
                  id="warranty"
                  type="date"
                  value={formData.warranty}
                  onChange={(e) => handleInputChange('warranty', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Photos */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Asset Photos</h3>
            
            {/* Existing Photos */}
            {formData.existingPhotos.length > 0 && (
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Current Photos:</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {formData.existingPhotos.map((photo, index) => (
                    <div key={index} className="relative group border rounded-lg overflow-hidden aspect-square">
                      <img
                        src={photo}
                        alt={`Current photo ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder.svg';
                        }}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center gap-1 opacity-0 group-hover:opacity-100">
                        <Button
                          type="button"
                          size="sm"
                          variant="secondary"
                          onClick={() => handlePreviewPhoto(photo)}
                          className="h-7 w-7 p-0"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          type="button"
                          size="sm"
                          variant="destructive"
                          onClick={() => handleRemoveExistingPhoto(index)}
                          className="h-7 w-7 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs p-1 text-center">
                        Photo {index + 1}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* New Photos Upload */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Add New Photos:</Label>
              <PhotoUpload
                photos={formData.photos}
                onPhotosChange={handlePhotosChange}
                maxPhotos={Math.max(0, 10 - formData.existingPhotos.length)}
                label={`Upload new photos (${formData.existingPhotos.length + formData.photos.length}/10 total)`}
              />
            </div>
            
            {/* Photo Summary */}
            <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
              <div className="flex justify-between items-center">
                <span>Current photos: {formData.existingPhotos.length}</span>
                <span>New photos: {formData.photos.length}</span>
                <span className="font-medium">Total: {formData.existingPhotos.length + formData.photos.length}/10</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Additional Notes</h3>
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Enter any additional notes or observations"
                rows={3}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.name || !formData.category || !formData.condition}
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </DialogContent>
      
      {/* Photo Preview Dialog */}
      <Dialog open={!!previewPhoto} onOpenChange={() => closePreview()}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Photo Preview</DialogTitle>
          </DialogHeader>
          {previewPhoto && (
            <div className="flex justify-center">
              <img
                src={previewPhoto}
                alt="Preview"
                className="max-w-full max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}