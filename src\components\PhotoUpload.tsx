import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, X, Eye, Camera } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface PhotoUploadProps {
  label: string;
  maxPhotos?: number;
  photos: File[];
  onPhotosChange: (photos: File[]) => void;
  disabled?: boolean;
}

const PhotoUpload: React.FC<PhotoUploadProps> = ({
  label,
  maxPhotos = 5,
  photos,
  onPhotosChange,
  disabled = false
}) => {
  const [previewPhoto, setPreviewPhoto] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB limit
      return isValidType && isValidSize;
    });

    const remainingSlots = maxPhotos - photos.length;
    const filesToAdd = validFiles.slice(0, remainingSlots);
    
    onPhotosChange([...photos, ...filesToAdd]);
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removePhoto = (index: number) => {
    const updatedPhotos = photos.filter((_, i) => i !== index);
    onPhotosChange(updatedPhotos);
  };

  const openPreview = (photo: File) => {
    const url = URL.createObjectURL(photo);
    setPreviewPhoto(url);
  };

  const closePreview = () => {
    if (previewPhoto) {
      URL.revokeObjectURL(previewPhoto);
      setPreviewPhoto(null);
    }
  };

  const canAddMore = photos.length < maxPhotos;

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium">{label}</Label>
      
      {/* Upload Button */}
      {canAddMore && !disabled && (
        <div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileSelect}
            className="hidden"
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            className="w-full border-dashed border-2 h-20 flex flex-col items-center justify-center gap-2 hover:bg-gray-50"
          >
            <Camera className="h-6 w-6 text-gray-400" />
            <span className="text-sm text-gray-600">
              Upload Foto ({photos.length}/{maxPhotos})
            </span>
          </Button>
        </div>
      )}

      {/* Photo Grid */}
      {photos.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {photos.map((photo, index) => {
            const photoUrl = URL.createObjectURL(photo);
            return (
              <Card key={index} className="relative group">
                <CardContent className="p-2">
                  <div className="relative aspect-square">
                    <img
                      src={photoUrl}
                      alt={`Photo ${index + 1}`}
                      className="w-full h-full object-cover rounded-md"
                      onLoad={() => URL.revokeObjectURL(photoUrl)}
                    />
                    
                    {/* Action Buttons */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-md flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100">
                      <Button
                        type="button"
                        size="sm"
                        variant="secondary"
                        onClick={() => openPreview(photo)}
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {!disabled && (
                        <Button
                          type="button"
                          size="sm"
                          variant="destructive"
                          onClick={() => removePhoto(index)}
                          className="h-8 w-8 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  {/* Photo Info */}
                  <div className="mt-2 text-xs text-gray-500 truncate">
                    {photo.name}
                  </div>
                  <div className="text-xs text-gray-400">
                    {(photo.size / 1024 / 1024).toFixed(1)} MB
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Photo Count Info */}
      <div className="text-xs text-gray-500">
        {photos.length === 0 && `No photos yet (maximum ${maxPhotos} photos)`}
        {photos.length > 0 && `${photos.length} of ${maxPhotos} photos`}
        {photos.length === maxPhotos && " - Maximum limit reached"}
      </div>

      {/* Preview Dialog */}
      <Dialog open={!!previewPhoto} onOpenChange={() => closePreview()}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Preview Foto</DialogTitle>
          </DialogHeader>
          {previewPhoto && (
            <div className="flex justify-center">
              <img
                src={previewPhoto}
                alt="Preview"
                className="max-w-full max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PhotoUpload;