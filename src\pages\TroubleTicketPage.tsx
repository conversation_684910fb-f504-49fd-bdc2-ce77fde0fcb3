"use client";

import React from "react";
import TroubleTicketManagement from "@/components/TroubleTicketManagement";
import { MadeWithDyad } from "@/components/made-with-dyad";
import { TroubleTicket } from "@/components/TroubleTicketManagement"; // Import TroubleTicket type

interface TroubleTicketPageProps {
  tickets: TroubleTicket[];
  setTickets: React.Dispatch<React.SetStateAction<TroubleTicket[]>>;
}

const TroubleTicketPage: React.FC<TroubleTicketPageProps> = ({ tickets, setTickets }) => {
  return (
    <div className="min-h-screen bg-background text-foreground p-4 sm:p-6 lg:p-8">
      <header className="mb-8">
        <h1 className="text-3xl sm:text-4xl font-bold text-center text-primary">
          Trouble Ticket Management
        </h1>
        <p className="text-center text-muted-foreground mt-2">
          Manage and track all network trouble tickets.
        </p>
      </header>

      <main className="container mx-auto">
        <TroubleTicketManagement tickets={tickets} setTickets={setTickets} />
      </main>

      <MadeWithDyad />
    </div>
  );
};

export default TroubleTicketPage;