"use client";

import React from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Clock, User, MapPin, AlertCircle, CheckCircle, Wrench } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MadeWithDyad } from "@/components/made-with-dyad";
import { TroubleTicket, Activity } from "@/components/TroubleTicketManagement"; // Import TroubleTicket and Activity types
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import MaterialUsageSummaryCard from "@/components/MaterialUsageSummaryCard"; // Import the new component

interface TroubleTicketDetailProps {
  tickets: TroubleTicket[]; // Receive tickets array as prop
}

const TroubleTicketDetail: React.FC<TroubleTicketDetailProps> = ({ tickets }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  // Find the ticket from the passed tickets array
  const ticketData = tickets.find(ticket => ticket.id === id);

  if (!ticketData) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background text-foreground p-4">
        <h1 className="text-3xl font-bold mb-4">Ticket Not Found</h1>
        <p className="text-lg text-muted-foreground mb-6">The trouble ticket with ID "{id}" could not be found.</p>
        <Button onClick={() => navigate('/trouble-tickets')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Trouble Tickets
        </Button>
        <MadeWithDyad />
      </div>
    );
  }

  const getPriorityColor = (priority: TroubleTicket["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-500 hover:bg-red-600";
      case "medium":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "low":
        return "bg-blue-500 hover:bg-blue-600";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusColor = (status: TroubleTicket["status"]) => {
    switch (status) {
      case "open":
        return "bg-orange-500 hover:bg-orange-600";
      case "in-progress":
        return "bg-blue-500 hover:bg-blue-600";
      case "closed":
        return "bg-green-500 hover:bg-green-600";
      default:
        return "bg-gray-500";
    }
  };

  const getActivityStatusIcon = (activity: Activity) => {
    if (activity.realDuration !== null && activity.realDuration !== undefined) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    return <Clock className="w-4 h-4 text-muted-foreground" />;
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 sm:p-6 lg:p-8">
      <header className="mb-8 flex items-center justify-between">
        <Button onClick={() => navigate('/trouble-tickets')} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to All Tickets
        </Button>
        <h1 className="text-3xl sm:text-4xl font-bold text-primary text-center flex-grow">
          Ticket Detail: {ticketData.id}
        </h1>
        <div className="w-24"></div> {/* Spacer to balance the back button */}
      </header>

      <main className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Overview Card */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl flex items-center">
              <AlertCircle className="mr-2 h-6 w-6 text-yellow-500" /> Ticket Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <p className="text-muted-foreground">Title:</p>
              <p className="font-semibold text-lg">{ticketData.title}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Description:</p>
              <p className="font-semibold text-lg">{ticketData.description}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Priority:</p>
              <Badge className={getPriorityColor(ticketData.priority)}>
                {ticketData.priority.charAt(0).toUpperCase() + ticketData.priority.slice(1)}
              </Badge>
            </div>
            <div>
              <p className="text-muted-foreground">Status:</p>
              <Badge className={getStatusColor(ticketData.status)}>
                {ticketData.status.charAt(0).toUpperCase() + ticketData.status.slice(1)}
              </Badge>
            </div>
            <div>
              <p className="text-muted-foreground">Assigned To:</p>
              <p className="font-semibold text-lg">{ticketData.assignedTo}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Route Name:</p>
              <p className="font-semibold text-lg">{ticketData.routeName || "N/A"}</p>
            </div>
            {ticketData.repairType && (
              <div>
                <p className="text-muted-foreground">Repair Type:</p>
                <Badge variant="outline" className="text-sm">
                  {ticketData.repairType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
              </div>
            )}
            {ticketData.repairNature && (
              <div>
                <p className="text-muted-foreground">Repair Nature:</p>
                <Badge variant={ticketData.repairNature === "permanent" ? "default" : "secondary"} className="text-sm">
                  {ticketData.repairNature === "permanent" ? "Permanent" : "Temporary"}
                </Badge>
              </div>
            )}
            {ticketData.repairNature === "temporary" && ticketData.temporaryReason && (
              <div className="md:col-span-2 lg:col-span-3">
                <p className="text-muted-foreground">Temporary Reason:</p>
                <p className="font-semibold text-lg">{ticketData.temporaryReason}</p>
              </div>
            )}
            <div>
              <p className="text-muted-foreground">Opened At:</p>
              <p className="font-semibold text-lg">{ticketData.openedAt}</p>
            </div>
            {ticketData.closedAt && (
              <div>
                <p className="text-muted-foreground">Closed At:</p>
                <p className="font-semibold text-lg">{ticketData.closedAt}</p>
              </div>
            )}
            {ticketData.duration && (
              <div>
                <p className="text-muted-foreground">Duration:</p>
                <p className="font-semibold text-lg">{ticketData.duration}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Material Usage Summary Card */}
        <MaterialUsageSummaryCard ticket={ticketData} />

        {/* RFO Report Card */}
        {ticketData.rfo && (
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="text-2xl flex items-center">
                <Wrench className="mr-2 h-6 w-6 text-blue-500" /> RFO Report
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-muted-foreground">Root Cause:</p>
                <p className="font-semibold">{ticketData.rfo.rootCause}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Time to Repair:</p>
                <p className="font-semibold">{ticketData.rfo.timeToRepair}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Notes:</p>
                <p className="font-semibold">{ticketData.rfo.notes}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Activities Card */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-2xl flex items-center">
              <Clock className="mr-2 h-6 w-6 text-purple-500" /> Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            {ticketData.activities && ticketData.activities.length > 0 ? (
              <div className="space-y-4">
                {ticketData.activities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0 mt-1">
                      {getActivityStatusIcon(activity)}
                    </div>
                    <div className="flex-grow">
                      <p className="font-semibold">{activity.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Estimated: {activity.estimatedDuration} min
                        {activity.realDuration !== null && activity.realDuration !== undefined && (
                          <span> | Real: {activity.realDuration} min</span>
                        )}
                        {activity.pic && <span> | PIC: {activity.pic}</span>}
                      </p>
                      {activity.result && (
                        <p className="text-sm text-muted-foreground mt-1">
                          <span className="font-medium">Result:</span> {activity.result}
                        </p>
                      )}
                      {activity.notes && activity.notes.trim() !== "" && activity.notes !== "System generated." && activity.notes !== "Automated release." && (
                        <p className="text-sm text-muted-foreground mt-1">
                          <span className="font-medium">Notes:</span> {activity.notes}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No activities recorded for this ticket.</p>
            )}
          </CardContent>
        </Card>
      </main>

      <MadeWithDyad />
    </div>
  );
};

export default TroubleTicketDetail;