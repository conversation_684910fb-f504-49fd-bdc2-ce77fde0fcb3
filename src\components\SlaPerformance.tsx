"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import CircularProgress from "./CircularProgress";
import { ArrowUp, ArrowDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface SlaPerformanceProps {
  overallSla: number;
  preventiveMaintenance: number;
  repairResponse: number;
  networkUptime: number;
  prevPeriodComparison: number; // Percentage change vs previous period
}

const SlaPerformance: React.FC<SlaPerformanceProps> = ({
  overallSla,
  preventiveMaintenance,
  repairResponse,
  networkUptime,
  prevPeriodComparison,
}) => {
  const comparisonColor = prevPeriodComparison >= 0 ? "text-green-500" : "text-red-500";
  const ComparisonIcon = prevPeriodComparison >= 0 ? ArrowUp : ArrowDown;

  return (
    <Card className="col-span-1 md:col-span-2 lg:col-span-3">
      <CardHeader>
        <CardTitle className="text-xl">SLA Performance</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="flex flex-col items-center justify-center p-4 border rounded-md bg-muted/20">
          <CircularProgress
            percentage={overallSla}
            size={150}
            strokeWidth={12}
            progressColor={overallSla >= 99.9 ? "hsl(var(--primary))" : overallSla >= 95 ? "hsl(var(--accent))" : "hsl(var(--destructive))"}
            textColor="hsl(var(--foreground))"
          />
          <p className="text-lg font-semibold mt-4">Overall SLA Compliance</p>
          <p className="text-sm text-muted-foreground">Target: 99.9%</p>
          <div className={cn("flex items-center mt-2 text-sm", comparisonColor)}>
            <ComparisonIcon className="w-4 h-4 mr-1" />
            <span>{Math.abs(prevPeriodComparison).toFixed(1)}% vs previous period</span>
          </div>
        </div>

        <div className="space-y-4 p-4">
          <div>
            <div className="flex justify-between text-sm font-medium mb-1">
              <span>Preventive Maintenance Completion</span>
              <span>{preventiveMaintenance}%</span>
            </div>
            <Progress value={preventiveMaintenance} className={cn("h-2", preventiveMaintenance >= 90 ? "bg-green-500" : preventiveMaintenance >= 70 ? "bg-yellow-500" : "bg-red-500")} />
          </div>
          <div>
            <div className="flex justify-between text-sm font-medium mb-1">
              <span>Repair Response Time</span>
              <span>{repairResponse}%</span>
            </div>
            <Progress value={repairResponse} className={cn("h-2", repairResponse >= 90 ? "bg-green-500" : repairResponse >= 70 ? "bg-yellow-500" : "bg-red-500")} />
          </div>
          <div>
            <div className="flex justify-between text-sm font-medium mb-1">
              <span>Network Uptime</span>
              <span>{networkUptime}%</span>
            </div>
            <Progress value={networkUptime} className={cn("h-2", networkUptime >= 99.9 ? "bg-green-500" : networkUptime >= 99 ? "bg-yellow-500" : "bg-red-500")} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SlaPerformance;