import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Edit, Trash2, FileText, Download, Upload } from 'lucide-react';
import { usePatrolData } from '@/contexts/PatrolDataContext';

interface SORData {
  id: string;
  otdrMeasurementId: string;
  route: string;
  core: string;
  sorNumber: string;
  createdDate: string;
  technician: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  findings: string;
  recommendations: string;
  attachments?: string[];
  approvedBy?: string;
  approvedDate?: string;
  rejectionReason?: string;
}

const SORPage: React.FC = () => {
  const { otdrMeasurements } = usePatrolData();
  const [sorData, setSorData] = useState<SORData[]>([
    {
      id: 'sor-001',
      otdrMeasurementId: 'otdr-001',
      route: 'Route-A',
      core: 'Core-1',
      sorNumber: 'SOR-2024-001',
      createdDate: '2024-01-15',
      technician: 'Ahmad Rizki',
      status: 'approved',
      findings: 'Loss increased at 12.5 km distance due to exposed cable. Reflectance shows -42 dB value which is still within tolerance limits.',
      recommendations: 'Immediate repair required for exposed cable area. Regular monitoring needed to ensure no further degradation.',
      attachments: ['otdr_trace_001.pdf', 'photo_damage_001.jpg'],
      approvedBy: 'Supervisor Network',
      approvedDate: '2024-01-16'
    },
    {
      id: 'sor-002',
      otdrMeasurementId: 'otdr-002',
      route: 'Route-B',
      core: 'Core-2',
      sorNumber: 'SOR-2024-002',
      createdDate: '2024-01-16',
      technician: 'Budi Santoso',
      status: 'submitted',
      findings: 'OTDR measurement shows normal condition on all segments. Total loss 0.28 dB is still within specification.',
      recommendations: 'No special action required. Continue routine monitoring as scheduled.',
      attachments: ['otdr_trace_002.pdf']
    }
  ]);

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<SORData | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [inputMode, setInputMode] = useState<'manual' | 'upload'>('manual');
  const [formData, setFormData] = useState({
    otdrMeasurementId: '',
    route: '',
    core: '',
    sorNumber: '',
    technician: '',
    findings: '',
    recommendations: '',
    status: 'draft' as const
  });

  const resetForm = () => {
    setFormData({
      otdrMeasurementId: '',
      route: '',
      core: '',
      sorNumber: '',
      technician: '',
      findings: '',
      recommendations: '',
      status: 'draft'
    });
    setEditingItem(null);
    setShowEditDialog(false);
    setInputMode('manual');
    setUploadFile(null);
  };

  const handleAddSOR = () => {
    const newSOR: SORData = {
      id: `sor-${Date.now()}`,
      ...formData,
      createdDate: new Date().toISOString().split('T')[0]
    };
    setSorData([...sorData, newSOR]);
    setShowAddDialog(false);
    resetForm();
  };

  const handleEdit = (item: SORData) => {
    setEditingItem(item);
    setFormData({
      otdrMeasurementId: item.otdrMeasurementId,
      route: item.route,
      core: item.core,
      sorNumber: item.sorNumber,
      technician: item.technician,
      findings: item.findings,
      recommendations: item.recommendations,
      status: item.status
    });
    setShowEditDialog(true);
  };

  const handleUpdate = () => {
    if (editingItem) {
      const updatedSOR = {
        ...editingItem,
        ...formData
      };
      setSorData(sorData.map(item => item.id === editingItem.id ? updatedSOR : item));
      setShowEditDialog(false);
      resetForm();
    }
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this SOR?')) {
      setSorData(sorData.filter(item => item.id !== id));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'submitted': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft': return 'Draft';
      case 'submitted': return 'Submitted';
      case 'approved': return 'Approved';
      case 'rejected': return 'Rejected';
      default: return status;
    }
  };

  const generateSORNumber = () => {
    const year = new Date().getFullYear();
    const count = sorData.length + 1;
    return `SOR-${year}-${count.toString().padStart(3, '0')}`;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
    }
  };

  const handleUpload = async () => {
    if (!uploadFile) {
      alert('Please select a file first!');
      return;
    }

    setIsUploading(true);
    
    // Simulasi proses upload
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulasi data yang diupload
      const uploadedData: SORData[] = [
        {
          id: `sor-upload-${Date.now()}`,
          otdrMeasurementId: 'otdr-upload-001',
          route: 'Route-Upload',
          core: 'Core-Upload',
          sorNumber: generateSORNumber(),
          createdDate: new Date().toISOString().split('T')[0],
          technician: 'Uploaded Data',
          status: 'draft',
          findings: 'Data from file upload: ' + uploadFile.name,
          recommendations: 'Please review and update data as needed.'
        }
      ];
      
      setSorData([...sorData, ...uploadedData]);
       setShowAddDialog(false);
       resetForm();
       alert(`Successfully uploaded ${uploadedData.length} SOR data from file ${uploadFile.name}`);
    } catch (error) {
      alert('Failed to upload file. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">SOR OTDR Management</h1>
          <p className="text-gray-600 mt-2">Manage Statement of Requirement from OTDR measurement results</p>
        </div>
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
           <DialogTrigger asChild>
             <Button onClick={() => {
               setFormData({...formData, sorNumber: generateSORNumber()});
               setInputMode('manual');
             }}>
               <Plus className="h-4 w-4 mr-2" />
               Add SOR Data
             </Button>
           </DialogTrigger>
         </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total SOR</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sorData.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Draft</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">
              {sorData.filter(item => item.status === 'draft').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Submitted</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {sorData.filter(item => item.status === 'submitted').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Approved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {sorData.filter(item => item.status === 'approved').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SOR List */}
      <Card>
        <CardHeader>
          <CardTitle>OTDR SOR List</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3 font-semibold">SOR Number</th>
                  <th className="text-left p-3 font-semibold">Route/Core</th>
                  <th className="text-left p-3 font-semibold">Technician</th>
                  <th className="text-left p-3 font-semibold">Date</th>
                  <th className="text-left p-3 font-semibold">Status</th>
                  <th className="text-left p-3 font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sorData.map((sor) => (
                  <tr key={sor.id} className="border-b hover:bg-gray-50">
                    <td className="p-3 font-medium">{sor.sorNumber}</td>
                    <td className="p-3">{sor.route} / {sor.core}</td>
                    <td className="p-3">{sor.technician}</td>
                    <td className="p-3">{sor.createdDate}</td>
                    <td className="p-3">
                      <Badge className={getStatusColor(sor.status)}>
                        {getStatusText(sor.status)}
                      </Badge>
                    </td>
                    <td className="p-3">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(sor)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(sor.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                        >
                          <Download className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Add Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add SOR Data</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {/* Mode Selection */}
            <div className="flex gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Button
                variant={inputMode === 'manual' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInputMode('manual')}
                className="flex-1"
              >
                <Plus className="h-4 w-4 mr-2" />
                Input Manual
              </Button>
              <Button
                variant={inputMode === 'upload' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInputMode('upload')}
                className="flex-1"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </Button>
            </div>
            
            {inputMode === 'manual' ? (
              // Manual Input Form
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sorNumber">SOR Number</Label>
                    <Input
                      value={formData.sorNumber}
                      onChange={(e) => setFormData({...formData, sorNumber: e.target.value})}
                      placeholder="SOR-2024-001"
                    />
                  </div>
                  <div>
                    <Label htmlFor="otdrMeasurementId">OTDR Measurement</Label>
                    <Select value={formData.otdrMeasurementId} onValueChange={(value) => {
                      const measurement = otdrMeasurements.find(m => m.id === value);
                      setFormData({
                        ...formData,
                        otdrMeasurementId: value,
                        route: measurement?.route || '',
                        core: measurement?.core || ''
                      });
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select OTDR measurement" />
                      </SelectTrigger>
                      <SelectContent>
                        {otdrMeasurements.map((measurement) => (
                          <SelectItem key={measurement.id} value={measurement.id}>
                            {measurement.route} - {measurement.core} ({measurement.measurementDate})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input
                      value={formData.route}
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Route-A"
                    />
                  </div>
                  <div>
                    <Label htmlFor="core">Core</Label>
                    <Input
                      value={formData.core}
                      onChange={(e) => setFormData({...formData, core: e.target.value})}
                      placeholder="Core-1"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="technician">Technician</Label>
                  <Input
                    value={formData.technician}
                    onChange={(e) => setFormData({...formData, technician: e.target.value})}
                    placeholder="Technician name"
                  />
                </div>
                <div>
                  <Label htmlFor="findings">Findings</Label>
                  <Textarea
                    value={formData.findings}
                    onChange={(e) => setFormData({...formData, findings: e.target.value})}
                    placeholder="Describe findings from OTDR measurement"
                    rows={4}
                  />
                </div>
                <div>
                  <Label htmlFor="recommendations">Recommendations</Label>
                  <Textarea
                    value={formData.recommendations}
                    onChange={(e) => setFormData({...formData, recommendations: e.target.value})}
                    placeholder="Provide action recommendations"
                    rows={4}
                  />
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value: any) => setFormData({...formData, status: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="submitted">Submitted</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => { setShowAddDialog(false); resetForm(); }}>Cancel</Button>
                  <Button onClick={handleAddSOR}>Save</Button>
                </div>
              </>
            ) : (
              // Upload Form
              <>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="file-upload">Select SOR File</Label>
                    <Input
                      id="file-upload"
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      className="mt-1"
                      onChange={handleFileChange}
                      disabled={isUploading}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Supported formats: Excel (.xlsx, .xls) or CSV (.csv)
                    </p>
                    {uploadFile && (
                      <p className="text-sm text-green-600 mt-1">
                        File selected: {uploadFile.name}
                      </p>
                    )}
                  </div>
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Expected File Format:</h4>
                    <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                      <li>• Columns: SOR Number, Route, Core, Technician, Findings, Recommendations</li>
                      <li>• Header must be in the first row</li>
                      <li>• Data starts from the second row</li>
                    </ul>
                  </div>
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => { setShowAddDialog(false); resetForm(); }}
                    disabled={isUploading}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleUpload}
                    disabled={!uploadFile || isUploading}
                  >
                    {isUploading ? 'Uploading...' : 'Upload'}
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit SOR</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sorNumber">SOR Number</Label>
                <Input
                  value={formData.sorNumber}
                  onChange={(e) => setFormData({...formData, sorNumber: e.target.value})}
                  placeholder="SOR-2024-001"
                />
              </div>
              <div>
                <Label htmlFor="technician">Technician</Label>
                <Input
                  value={formData.technician}
                  onChange={(e) => setFormData({...formData, technician: e.target.value})}
                  placeholder="Technician name"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="route">Route</Label>
                <Input
                  value={formData.route}
                  onChange={(e) => setFormData({...formData, route: e.target.value})}
                  placeholder="Route-A"
                />
              </div>
              <div>
                <Label htmlFor="core">Core</Label>
                <Input
                  value={formData.core}
                  onChange={(e) => setFormData({...formData, core: e.target.value})}
                  placeholder="Core-1"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="findings">Findings</Label>
              <Textarea
                value={formData.findings}
                onChange={(e) => setFormData({...formData, findings: e.target.value})}
                placeholder="Describe findings from OTDR measurement"
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="recommendations">Recommendations</Label>
              <Textarea
                value={formData.recommendations}
                onChange={(e) => setFormData({...formData, recommendations: e.target.value})}
                placeholder="Provide action recommendations"
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value: any) => setFormData({...formData, status: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => { setShowEditDialog(false); resetForm(); }}>Cancel</Button>
              <Button onClick={handleUpdate}>Update</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SORPage;