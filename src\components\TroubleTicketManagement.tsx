"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Ticket, Clock, User, Edit, CheckCircle, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import CreateTroubleTicketDialog from "./CreateTroubleTicketDialog";
import EditTroubleTicketDialog from "./EditTroubleTicketDialog"; // Import the new dialog component
import { showSuccess } from "@/utils/toast";

// New interfaces for material and closure details
export interface Material {
  name: string;
  quantity: number;
  unit: string;
}

export interface ClosureDetail {
  closureId: string;
  splicedCores: number;
  latitude?: number; // New: Optional latitude
  longitude?: number; // New: Optional longitude
}

// Define Activity interface (material fields removed)
export interface Activity { // Exported for use in other components
  name: string;
  estimatedDuration: number; // in minutes
  realDuration?: number | null; // in minutes, null if not completed, now optional
  pic?: string; // now optional
  result?: string; // Result of the activity
  notes?: string; // Additional notes for the activity
}

export interface TroubleTicket { // Exported for use in other components
  id: string;
  priority: "high" | "medium" | "low";
  status: "open" | "in-progress" | "closed";
  title: string;
  description: string; // Added description
  assignedTo: string;
  openedAt: string;
  closedAt: string | null; // Added closedAt
  duration: string | null; // Added duration
  routeName: string; // New field for route name
  repairType?: string; // New field for repair type
  repairNature?: "permanent" | "temporary"; // New field for repair nature
  temporaryReason?: string; // New field for temporary repair reason
  rfo?: {
    rootCause: string;
    timeToRepair: string;
    notes: string;
  };
  activities: Activity[]; // Added activities array
  photos?: string[]; // Array of photo URLs or base64 strings

  // Material and Closure details moved to the main ticket level
  materialsUsed?: Material[];
  numClosures?: number;
  numCables?: number;
  numHdpe?: number;
  otherCustomMaterials?: string;
  closureSplicingDetails?: ClosureDetail[];
}

// Helper to generate default activities for a new ticket
const getDefaultActivities = (): Activity[] => [
  { name: "Ticket Release", estimatedDuration: 5, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Measurement", estimatedDuration: 60, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Handling TT", estimatedDuration: 120, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: null, pic: "", result: "", notes: "" },
];

let ticketCounter = 1; // Start counter for new tickets
const generateNewTicketId = () => `NOC-CGK-${String(ticketCounter++).padStart(3, '0')}`;

// Get current date for sample data
const now = new Date();
const today = now.toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' });

export const initialSampleTickets: TroubleTicket[] = [ // Exported for use in other components
  {
    id: "NOC-CGK-001",
    priority: "high",
    status: "closed",
    title: "Fiber cut on Link FR-004",
    description: "Major fiber cut detected affecting multiple services in the downtown area. Immediate action required.",
    assignedTo: "John Doe",
    openedAt: `${today} 08:00 AM`,
    closedAt: `${today} 12:15 PM`, // Example closed time
    duration: "4h 15m", // Example duration
    routeName: "Route Bekasi - Grand Galaxy", // Added route name
    repairType: "fiber-cut", // Added repair type
    repairNature: "permanent", // Added repair nature
    rfo: {
      rootCause: "External fiber cut (construction)",
      timeToRepair: "4h 15m",
      notes: "Repaired by splicing team.",
    },
    // Aggregated materials for NOC-CGK-001
    materialsUsed: [{name: "Fiber Optic Cable (24 core)", quantity: 0.5, unit: "km"}, {name: "Splice Closure (24 port)", quantity: 1, unit: "unit"}],
    numClosures: 1,
    numCables: 1,
    numHdpe: 0,
    otherCustomMaterials: "Fusion Splicer, OTDR",
    closureSplicingDetails: [{closureId: "CL-001", splicedCores: 24, latitude: -6.2383, longitude: 107.0000}], // Added coordinates
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: 5, pic: "Admin", result: "Ticket released to team.", notes: "Automated release." },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 25, pic: "John Doe", result: "Team mobilized.", notes: "Gathered tools and vehicle." },
      { name: "Measurement", estimatedDuration: 60, realDuration: 55, pic: "John Doe", result: "Fault located at KM 12.5.", notes: "Used OTDR to pinpoint cut." },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: 40, pic: "John Doe", result: "Arrived at site.", notes: "Traffic was moderate." },
      { name: "Handling TT", estimatedDuration: 120, realDuration: 110, pic: "John Doe", result: "Fiber spliced and tested.", notes: "24-fiber splice completed." },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: 10, pic: "John Doe", result: "Site secured, services restored.", notes: "Final checks passed." },
    ],
  },
  {
    id: "NOC-CGK-002",
    priority: "medium",
    status: "in-progress",
    title: "Intermittent connectivity - OLT-03",
    description: "Customers in Sector B reporting intermittent internet connectivity. Suspected OLT port issue.",
    assignedTo: "Jane Smith",
    openedAt: `${today} 09:30 AM`,
    closedAt: null,
    duration: null,
    routeName: "Route Tangerang - BSD City", // Added route name
    repairType: "connectivity-issue", // Added repair type
    repairNature: "temporary", // Added repair nature
    temporaryReason: "Waiting for new SFP module spare part", // Added temporary reason
    // Aggregated materials for NOC-CGK-002
    materialsUsed: [{name: "SFP Module", quantity: 1, unit: "unit"}],
    numClosures: 0,
    numCables: 0,
    numHdpe: 0,
    otherCustomMaterials: "Optical Power Meter",
    closureSplicingDetails: [],
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: 4, pic: "Admin", result: "Ticket released.", notes: "System generated." },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Measurement", estimatedDuration: 60, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Handling TT", estimatedDuration: 120, realDuration: null, pic: "Jane Smith", result: "Troubleshooting OLT port.", notes: "Checking optical power levels." },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: null, pic: "", result: "", notes: "" },
    ],
  },
  {
    id: "NOC-CGK-003",
    priority: "low",
    status: "open",
    title: "High latency reports - Sector C",
    description: "Multiple reports of high latency during peak hours from users in Sector C. Needs investigation.",
    assignedTo: "Unassigned",
    openedAt: `${today} 10:00 AM`,
    closedAt: null,
    duration: null,
    routeName: "Route Jakarta Pusat - Sudirman", // Added route name
    repairType: "connectivity-issue", // Added repair type
    repairNature: "permanent", // Added repair nature
    materialsUsed: [], numClosures: 0, numCables: 0, numHdpe: 0, otherCustomMaterials: "", closureSplicingDetails: [], // No materials for this ticket
    activities: getDefaultActivities(), // Use default for new-like tickets
  },
  {
    id: "NOC-CGK-004",
    priority: "high",
    status: "closed",
    title: "Power outage at Node X",
    description: "Complete power failure at network Node X. Services down until power is restored.",
    assignedTo: "John Doe",
    openedAt: `${today} 07:00 AM`,
    closedAt: `${today} 09:30 AM`, // Example closed time
    duration: "2h 30m", // Example duration
    routeName: "Route Tangerang - Karawaci", // Added route name
    repairType: "power-outage", // Added repair type
    repairNature: "temporary", // Added repair nature
    temporaryReason: "Using generator temporarily while waiting for PLN repair", // Added temporary reason
    rfo: {
      rootCause: "Utility power failure",
      timeToRepair: "2h 30m",
      notes: "Generator kicked in, utility power restored later.",
    },
    // Aggregated materials for NOC-CGK-004
    materialsUsed: [{name: "Diesel Fuel", quantity: 10, unit: "liter"}],
    numClosures: 0,
    numCables: 0,
    numHdpe: 0,
    otherCustomMaterials: "Generator maintenance kit",
    closureSplicingDetails: [],
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: 5, pic: "Admin", result: "Ticket released.", notes: "Automated." },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 20, pic: "John Doe", result: "Team ready.", notes: "Checked generator fuel." },
      { name: "Measurement", estimatedDuration: 60, realDuration: 45, pic: "John Doe", result: "No fiber fault, power issue confirmed.", notes: "OTDR clear." },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: 30, pic: "John Doe", result: "Arrived at site.", notes: "Traffic was moderate." },
      { name: "Handling TT", estimatedDuration: 120, realDuration: 90, pic: "John Doe", result: "Generator started, services restored.", notes: "Manual generator start." },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: 10, pic: "John Doe", result: "Site secured, power stable.", notes: "Monitored for 10 mins." },
    ],
  },
  {
    id: "NOC-CGK-005",
    priority: "medium",
    status: "in-progress",
    title: "Router configuration error - Core Router 1",
    description: "Misconfiguration detected on Core Router 1 after recent update, causing routing issues.",
    assignedTo: "Jane Smith",
    openedAt: `${today} 11:00 AM`,
    closedAt: null,
    duration: null,
    routeName: "Route Jakarta Selatan - Kemang", // Added route name
    repairType: "configuration-error", // Added repair type
    // Aggregated materials for NOC-CGK-005
    materialsUsed: [{name: "Console Cable", quantity: 1, unit: "unit"}],
    numClosures: 0,
    numCables: 0,
    numHdpe: 0,
    otherCustomMaterials: "Laptop",
    closureSplicingDetails: [],
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: 5, pic: "Admin", result: "Ticket released.", notes: "Automated." },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 28, pic: "Jane Smith", result: "Team logged in.", notes: "Accessing router." },
      { name: "Measurement", estimatedDuration: 60, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Handling TT", estimatedDuration: 120, realDuration: null, pic: "Jane Smith", result: "Applying rollback configuration.", notes: "Using backup config." },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: null, pic: "", result: "", notes: "" },
    ],
  },
  {
    id: "NOC-CGK-006",
    priority: "low",
    status: "closed",
    title: "Routine software update - OLT-02",
    description: "Scheduled software update for OLT-02 completed successfully.",
    assignedTo: "Unassigned",
    openedAt: `${today} 06:00 AM`,
    closedAt: `${today} 07:00 AM`, // Example closed time
    duration: "1h 0m", // Example duration
    routeName: "Route Bekasi - Tambun", // Added route name
    repairType: "maintenance", // Added repair type
    rfo: {
      rootCause: "Scheduled maintenance",
      timeToRepair: "1h 00m",
      notes: "Update completed successfully.",
    },
    materialsUsed: [], numClosures: 0, numCables: 0, numHdpe: 0, otherCustomMaterials: "", closureSplicingDetails: [], // No specific materials for this
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: 5, pic: "Admin", result: "Ticket released.", notes: "Automated." },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 15, pic: "System", result: "Update script prepared.", notes: "Pre-checks passed." },
      { name: "Measurement", estimatedDuration: 60, realDuration: 0, pic: "System", result: "No impact on services.", notes: "Monitored during update." },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: 0, pic: "System", result: "N/A", notes: "Remote activity." },
      { name: "Handling TT", estimatedDuration: 120, realDuration: 60, pic: "System", result: "Software updated.", notes: "Version 2.1.0 installed." },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: 5, pic: "System", result: "Post-checks passed, services stable.", notes: "Confirmed stability." },
    ],
  },
  {
    id: "NOC-CGK-007",
    priority: "high",
    status: "in-progress",
    title: "New Fiber Deployment - Sector D",
    description: "Deployment of new fiber optic cable for expansion in Sector D. Requires splicing and new closures.",
    assignedTo: "Team Alpha",
    openedAt: `${today} 13:00 PM`,
    closedAt: null,
    duration: null,
    routeName: "Route Jakarta Utara - Kelapa Gading",
    repairType: "upgrade", // Added repair type
    // Aggregated materials for NOC-CGK-007
    materialsUsed: [
      {name: "Fiber Optic Cable (48 core)", quantity: 2.0, unit: "km"},
      {name: "Splice Closure (48 port)", quantity: 2, unit: "unit"},
      {name: "Fiber Optic Pigtail SC/UPC", quantity: 48, unit: "pcs"}
    ],
    numClosures: 2,
    numCables: 1,
    numHdpe: 0.5,
    otherCustomMaterials: "Fusion Splicer, Cleaver, Splicing Machine",
    closureSplicingDetails: [
      {closureId: "CL-002", splicedCores: 48, latitude: -6.1214, longitude: 106.7741}, // Added coordinates
      {closureId: "CL-003", splicedCores: 48, latitude: -6.1500, longitude: 106.8000}  // Added coordinates
    ],
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: 5, pic: "Admin", result: "Ticket released.", notes: "Automated." },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: 25, pic: "Team Alpha", result: "Team mobilized with equipment.", notes: "Loaded fiber drums and tools." },
      { name: "Measurement", estimatedDuration: 60, realDuration: 50, pic: "Team Alpha", result: "Route surveyed, measurements taken.", notes: "Confirmed cable lengths." },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: 35, pic: "Team Alpha", result: "Arrived at deployment site.", notes: "Smooth travel." },
      { name: "Handling TT", estimatedDuration: 240, realDuration: null, pic: "Team Alpha", result: "", notes: "Splicing in progress for new segment." },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: null, pic: "", result: "", notes: "" },
    ],
  },
  {
    id: "NOC-CGK-008",
    priority: "medium",
    status: "open",
    title: "Damaged Handhole Cover - Site 123",
    description: "Handhole cover at Site 123 is damaged and needs replacement. Potential security risk.",
    assignedTo: "Unassigned",
    openedAt: `${today} 14:30 PM`,
    closedAt: null,
    duration: null,
    routeName: "Route Bandung - Dago",
    repairType: "maintenance", // Added repair type
    // Aggregated materials for NOC-CGK-008
    materialsUsed: [{name: "Handhole Cover (Concrete)", quantity: 1, unit: "unit"}],
    numClosures: 0,
    numCables: 0,
    numHdpe: 0,
    otherCustomMaterials: "Crowbar, Safety Cones",
    closureSplicingDetails: [{closureId: "CL-006", splicedCores: 0, latitude: -6.8950, longitude: 107.6100}], // Added coordinates
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Team Preparation", estimatedDuration: 30, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Measurement", estimatedDuration: 60, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Travel to Site", estimatedDuration: 45, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Handling TT", estimatedDuration: 60, realDuration: null, pic: "", result: "", notes: "Replace damaged handhole cover." },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: null, pic: "", result: "", notes: "" },
    ],
  },
  {
    id: "NOC-CGK-009",
    priority: "high",
    status: "in-progress",
    title: "New Cable Installation - Sector E",
    description: "Installation of new fiber optic cable and associated closures for a new residential area.",
    assignedTo: "Team Beta",
    openedAt: `${today} 10:00 AM`,
    closedAt: null,
    duration: null,
    routeName: "Route Surabaya - Pusat",
    repairType: "upgrade", // Added repair type
    // Aggregated materials for NOC-CGK-009
    materialsUsed: [
      {name: "Fiber Optic Cable (96 core)", quantity: 1.0, unit: "km"},
      {name: "HDPE Pipe (40mm)", quantity: 1.2, unit: "km"},
      {name: "Cable Tie", quantity: 200, unit: "pcs"},
      {name: "Splice Closure (96 port)", quantity: 2, unit: "unit"},
      {name: "Fiber Optic Pigtail LC/UPC", quantity: 96, unit: "pcs"}
    ],
    numClosures: 2,
    numCables: 1,
    numHdpe: 1,
    otherCustomMaterials: "Cable Puller, Lubricant, Splicing Machine, OTDR",
    closureSplicingDetails: [
      {closureId: "CL-004", splicedCores: 96, latitude: -7.2575, longitude: 112.7521}, // Added coordinates
      {closureId: "CL-005", splicedCores: 96, latitude: -7.2800, longitude: 112.7800}  // Added coordinates
    ],
    activities: [
      { name: "Ticket Release", estimatedDuration: 5, realDuration: 5, pic: "Admin", result: "Ticket released.", notes: "Automated." },
      { name: "Team Preparation", estimatedDuration: 45, realDuration: 40, pic: "Team Beta", result: "Equipment loaded.", notes: "Truck prepared with all necessary materials." },
      { name: "Travel to Site", estimatedDuration: 60, realDuration: 55, pic: "Team Beta", result: "Arrived at site.", notes: "Traffic was heavy." },
      { name: "Cable Installation", estimatedDuration: 300, realDuration: null, pic: "Team Beta", result: "", notes: "Pulling 1000m of 96-core fiber." },
      { name: "Splicing & Termination", estimatedDuration: 180, realDuration: null, pic: "Team Beta", result: "", notes: "Splicing 2 closures." },
      { name: "Testing & Documentation", estimatedDuration: 90, realDuration: null, pic: "", result: "", notes: "" },
      { name: "Site Security until TT Closed", estimatedDuration: 15, realDuration: null, pic: "", result: "", notes: "" },
    ],
  },
];

interface TroubleTicketManagementProps {
  tickets: TroubleTicket[];
  setTickets: React.Dispatch<React.SetStateAction<TroubleTicket[]>>;
}

const TroubleTicketManagement: React.FC<TroubleTicketManagementProps> = ({ tickets, setTickets }) => {
  const [filterStatus, setFilterStatus] = React.useState<"all" | TroubleTicket["status"]>("all");
  const [isCreateTicketDialogOpen, setIsCreateTicketDialogOpen] = React.useState(false);
  const [isEditTicketDialogOpen, setIsEditTicketDialogOpen] = React.useState(false);
  const [editingTicket, setEditingTicket] = React.useState<TroubleTicket | null>(null);

  const getPriorityColor = (priority: TroubleTicket["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-500 hover:bg-red-600";
      case "medium":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "low":
        return "bg-blue-500 hover:bg-blue-600";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusColor = (status: TroubleTicket["status"]) => {
    switch (status) {
      case "open":
        return "bg-orange-500 hover:bg-orange-600";
      case "in-progress":
        return "bg-blue-500 hover:bg-blue-600";
      case "closed":
        return "bg-green-500 hover:bg-green-600";
      default:
        return "bg-gray-500";
    }
  };

  const handleCreateTicket = (newTicketData: {
    title: string;
    description: string; // Added description
    priority: "high" | "medium" | "low";
    assignedTo: string;
    routeName: string; // Added routeName
    repairType: string; // Added repairType
    repairNature: "permanent" | "temporary"; // Added repairNature
    temporaryReason?: string; // Added temporaryReason
    photos?: string[]; // Added photos
  }) => {
    const newTicket: TroubleTicket = {
      id: generateNewTicketId(), // Use new ID format
      status: "open", // New tickets are always open
      openedAt: new Date().toLocaleString(),
      closedAt: null, // Initialize as null
      duration: null, // Initialize as null
      activities: getDefaultActivities(), // Initialize with default activities
      materialsUsed: [], // Initialize empty material arrays for new tickets
      numClosures: 0,
      numCables: 0,
      numHdpe: 0,
      otherCustomMaterials: "",
      closureSplicingDetails: [],
      ...newTicketData,
    };
    setTickets((prevTickets) => [newTicket, ...prevTickets]); // Add new ticket to the top
  };

  const handleEditClick = (ticket: TroubleTicket) => {
    setEditingTicket(ticket);
    setIsEditTicketDialogOpen(true);
  };

  const handleUpdateTicket = (updatedTicket: TroubleTicket) => {
    setTickets((prevTickets) =>
      prevTickets.map((ticket) =>
        ticket.id === updatedTicket.id ? updatedTicket : ticket
      )
    );
  };

  const handleCloseTicket = (ticketId: string) => {
    setTickets((prevTickets) =>
      prevTickets.map((ticket) => {
        if (ticket.id === ticketId) {
          const closedAt = new Date();
          const openedDate = new Date(ticket.openedAt);
          const durationMs = closedAt.getTime() - openedDate.getTime();

          const hours = Math.floor(durationMs / (1000 * 60 * 60));
          const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
          const durationString = `${hours}h ${minutes}m`;

          return {
            ...ticket,
            status: "closed",
            closedAt: closedAt.toLocaleString(),
            duration: durationString,
          };
        }
        return ticket;
      })
    );
    showSuccess(`Ticket ${ticketId} has been closed.`);
  };

  const handleDeleteTicket = (ticketId: string) => {
    if (window.confirm('Are you sure you want to delete this trouble ticket?')) {
      setTickets((prevTickets) => prevTickets.filter((ticket) => ticket.id !== ticketId));
      showSuccess(`Ticket ${ticketId} has been deleted.`);
    }
  };

  const filteredTickets = tickets.filter(ticket => {
    if (filterStatus === "all") {
      return true;
    }
    return ticket.status === filterStatus;
  });

  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-xl">Trouble Ticket Management</CardTitle>
        <Button className="flex items-center" onClick={() => setIsCreateTicketDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" /> Create New Ticket
        </Button>
      </CardHeader>
      <CardContent>
        <div className="flex space-x-2 mb-4">
          <Button
            variant={filterStatus === "all" ? "default" : "outline"}
            onClick={() => setFilterStatus("all")}
            className={cn(filterStatus === "all" ? "bg-primary text-primary-foreground" : "text-muted-foreground")}
          >
            All
          </Button>
          <Button
            variant={filterStatus === "open" ? "default" : "outline"}
            onClick={() => setFilterStatus("open")}
            className={cn(filterStatus === "open" ? "bg-orange-500 text-white" : "text-muted-foreground")}
          >
            Open
          </Button>
          <Button
            variant={filterStatus === "in-progress" ? "default" : "outline"}
            onClick={() => setFilterStatus("in-progress")}
            className={cn(filterStatus === "in-progress" ? "bg-blue-500 text-white" : "text-muted-foreground")}
          >
            In-Progress
          </Button>
          <Button
            variant={filterStatus === "closed" ? "default" : "outline"}
            onClick={() => setFilterStatus("closed")}
            className={cn(filterStatus === "closed" ? "bg-green-500 text-white" : "text-muted-foreground")}
          >
            Closed
          </Button>
        </div>
        <h3 className="text-lg font-semibold mb-3">Tickets ({filteredTickets.length})</h3>
        <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
          {filteredTickets.length > 0 ? (
            filteredTickets.map((ticket) => (
              <div key={ticket.id} className="border rounded-md p-4 shadow-sm text-left">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-semibold text-base text-left">{ticket.title}</h4>
                  <div className="flex space-x-2">
                    <Badge className={getPriorityColor(ticket.priority)}>
                      {ticket.priority.charAt(0).toUpperCase() + ticket.priority.slice(1)} Priority
                    </Badge>
                    <Badge className={getStatusColor(ticket.status)}>
                      {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-2 text-left">{ticket.description}</p>
                <div className="mb-2 flex flex-wrap gap-2">
                  {ticket.repairType && (
                    <Badge variant="outline" className="text-xs">
                      {ticket.repairType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  )}
                  {ticket.repairNature && (
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${
                        ticket.repairNature === "permanent" 
                          ? "bg-blue-500 text-white border-blue-500" 
                          : "bg-orange-500 text-white border-orange-500"
                      }`}
                    >
                      {ticket.repairNature === "permanent" ? "Permanent" : "Temporary"}
                    </Badge>
                  )}
                </div>
                {ticket.repairNature === "temporary" && ticket.temporaryReason && (
                  <div className="mb-2">
                    <p className="text-xs text-muted-foreground text-left">
                      <span className="font-medium">Temporary Reason:</span> {ticket.temporaryReason}
                    </p>
                  </div>
                )}
                <div className="text-sm text-muted-foreground flex items-center space-x-4 text-left">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    <span>Opened: {ticket.openedAt}</span>
                  </div>
                  {ticket.status === "closed" && ticket.closedAt && (
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      <span>Closed: {ticket.closedAt}</span>
                    </div>
                  )}
                  {ticket.status === "closed" && ticket.duration && (
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      <span>Duration: {ticket.duration}</span>
                    </div>
                  )}
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-1" />
                    <span>{ticket.assignedTo}</span>
                  </div>
                </div>
                {ticket.rfo && (
                  <div className="mt-4 pt-4 border-t border-dashed border-muted-foreground/30">
                    <h5 className="font-semibold text-sm mb-2 text-left">RFO Report:</h5>
                    <p className="text-sm text-left">
                      <span className="font-medium">Root Cause:</span> {ticket.rfo.rootCause}
                    </p>
                    <p className="text-sm text-left">
                      <span className="font-medium">Time-to-Repair:</span> {ticket.rfo.timeToRepair}
                    </p>
                    <p className="text-sm text-left">
                      <span className="font-medium">Notes:</span> {ticket.rfo.notes}
                    </p>
                  </div>
                )}
                {ticket.activities && ticket.activities.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-dashed border-muted-foreground/30">
                    <h5 className="font-semibold text-sm mb-2 text-left">Activities:</h5>
                    <div className="text-sm space-y-2 text-left">
                      {ticket.activities.map((activity, idx) => (
                        <div key={idx} className="text-left break-words flex">
                          <span className="font-medium text-gray-700 mr-2 flex-shrink-0">{idx + 1}.</span>
                          <div className="flex-1">
                            <span className="font-medium">{activity.name}</span>
                            <div className="text-xs text-blue-600 mt-1">
                              Est: {activity.estimatedDuration} min, Real: {activity.realDuration !== null ? `${activity.realDuration} min` : 'N/A'}, PIC: {activity.pic || 'N/A'}
                            </div>
                            {activity.result && <div className="text-xs text-gray-500 text-left break-words mt-1">Result: {activity.result}</div>}
                            {activity.notes && activity.notes.trim() !== "" && activity.notes !== "System generated." && activity.notes !== "Automated release." && <div className="text-xs text-gray-500 text-left break-words mt-1">Notes: {activity.notes}</div>}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                <div className="mt-4 flex justify-end space-x-2">
                  {ticket.status !== "closed" && (
                    <Button variant="outline" size="sm" onClick={() => handleCloseTicket(ticket.id)}>
                      <CheckCircle className="mr-2 h-4 w-4" /> Close Ticket
                    </Button>
                  )}
                  <Button variant="outline" size="sm" onClick={() => handleEditClick(ticket)}>
                    <Edit className="mr-2 h-4 w-4" /> Edit
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDeleteTicket(ticket.id)}
                    className="border-red-500 text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center text-muted-foreground py-8">No tickets found for the selected status.</p>
          )}
        </div>
      </CardContent>
      <CreateTroubleTicketDialog
        isOpen={isCreateTicketDialogOpen}
        onClose={() => setIsCreateTicketDialogOpen(false)}
        onCreate={handleCreateTicket}
      />
      <EditTroubleTicketDialog
        isOpen={isEditTicketDialogOpen}
        onClose={() => setIsEditTicketDialogOpen(false)}
        ticket={editingTicket}
        onUpdate={handleUpdateTicket}
      />
    </Card>
  );
};

export default TroubleTicketManagement;