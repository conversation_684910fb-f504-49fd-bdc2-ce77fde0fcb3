// Utility untuk membuat demo user
// Hanya untuk development/testing

import { supabase } from '../lib/supabase';

export interface DemoUserData {
  email: string;
  password: string;
  full_name: string;
  username: string;
  department?: string;
  position?: string;
}

export const demoUsers: DemoUserData[] = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    full_name: 'System Administrator',
    username: 'admin',
    department: 'IT',
    position: 'System Admin'
  },
  {
    email: '<EMAIL>',
    password: 'Manager123!',
    full_name: 'Network Manager',
    username: 'manager',
    department: 'Network Operations',
    position: 'Manager'
  },
  {
    email: '<EMAIL>',
    password: 'Tech123!',
    full_name: 'Field Technician',
    username: 'technician',
    department: 'Field Operations',
    position: 'Senior Technician'
  }
];

export async function createDemoUser(userData: DemoUserData) {
  try {
    console.log('Creating demo user:', userData.email);
    
    // Sign up user
    const { data, error } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.full_name,
          username: userData.username,
          department: userData.department,
          position: userData.position
        }
      }
    });

    if (error) {
      console.error('Error creating user:', error);
      return { success: false, error: error.message };
    }

    if (data.user) {
      console.log('User created successfully:', data.user.email);
      
      // Update profile with additional data
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          username: userData.username,
          department: userData.department,
          position: userData.position
        })
        .eq('id', data.user.id);

      if (profileError) {
        console.warn('Error updating profile:', profileError);
      }

      return { 
        success: true, 
        user: data.user,
        message: `User ${userData.email} created successfully!`
      };
    }

    return { success: false, error: 'No user data returned' };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function createAllDemoUsers() {
  console.log('Creating all demo users...');
  const results = [];
  
  for (const userData of demoUsers) {
    const result = await createDemoUser(userData);
    results.push({ email: userData.email, ...result });
    
    // Wait a bit between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return results;
}

// Function to test login with demo user
export async function testDemoLogin(email: string, password: string) {
  try {
    console.log('Testing login for:', email);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('Login test failed:', error);
      return { success: false, error: error.message };
    }

    if (data.user) {
      console.log('Login test successful for:', email);
      
      // Sign out immediately after test
      await supabase.auth.signOut();
      
      return { 
        success: true, 
        message: `Login test successful for ${email}` 
      };
    }

    return { success: false, error: 'No user data returned' };
  } catch (error) {
    console.error('Login test error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Development helper - add to window object for console access
if (import.meta.env.DEV) {
  (window as any).createDemoUser = createDemoUser;
  (window as any).createAllDemoUsers = createAllDemoUsers;
  (window as any).testDemoLogin = testDemoLogin;
  (window as any).demoUsers = demoUsers;
  
  console.log('🔧 Demo user utilities available:');
  console.log('- createDemoUser(userData)');
  console.log('- createAllDemoUsers()');
  console.log('- testDemoLogin(email, password)');
  console.log('- demoUsers (array of demo user data)');
}
