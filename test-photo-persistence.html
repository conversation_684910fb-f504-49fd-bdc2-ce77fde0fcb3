<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Photo Persistence</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .photo-preview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px;
            border: 1px solid #ccc;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Test Photo Persistence in localStorage</h1>
    
    <div class="test-section">
        <h2>1. Test Base64 Conversion</h2>
        <input type="file" id="photoInput" accept="image/*" multiple>
        <button onclick="convertToBase64()">Convert to Base64</button>
        <div id="base64Result" class="result"></div>
        <div id="photoPreview"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test localStorage Save/Load</h2>
        <button onclick="saveToLocalStorage()">Save Photos to localStorage</button>
        <button onclick="loadFromLocalStorage()">Load Photos from localStorage</button>
        <button onclick="clearLocalStorage()">Clear localStorage</button>
        <div id="storageResult" class="result"></div>
        <div id="loadedPhotos"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test Asset Update Simulation</h2>
        <button onclick="simulateAssetUpdate()">Simulate Asset Update</button>
        <div id="updateResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Check Current localStorage Data</h2>
        <button onclick="checkCurrentData()">Check assetManagementRoutes</button>
        <div id="currentDataResult" class="result"></div>
    </div>

    <script>
        let convertedPhotos = [];
        
        async function convertToBase64() {
            const input = document.getElementById('photoInput');
            const files = Array.from(input.files);
            
            if (files.length === 0) {
                document.getElementById('base64Result').innerHTML = 'Please select photos first.';
                return;
            }
            
            try {
                const promises = files.map(file => {
                    return new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = () => resolve(reader.result);
                        reader.readAsDataURL(file);
                    });
                });
                
                convertedPhotos = await Promise.all(promises);
                
                document.getElementById('base64Result').innerHTML = 
                    `Converted ${convertedPhotos.length} photos to Base64. Total size: ${JSON.stringify(convertedPhotos).length} characters`;
                
                // Show previews
                const previewDiv = document.getElementById('photoPreview');
                previewDiv.innerHTML = '';
                convertedPhotos.forEach((photo, index) => {
                    const img = document.createElement('img');
                    img.src = photo;
                    img.className = 'photo-preview';
                    img.alt = `Photo ${index + 1}`;
                    previewDiv.appendChild(img);
                });
                
            } catch (error) {
                document.getElementById('base64Result').innerHTML = `Error: ${error.message}`;
            }
        }
        
        function saveToLocalStorage() {
            if (convertedPhotos.length === 0) {
                document.getElementById('storageResult').innerHTML = 'No photos to save. Convert photos first.';
                return;
            }
            
            try {
                const testData = {
                    photos: convertedPhotos,
                    timestamp: new Date().toISOString(),
                    count: convertedPhotos.length
                };
                
                localStorage.setItem('testPhotos', JSON.stringify(testData));
                document.getElementById('storageResult').innerHTML = 
                    `Saved ${convertedPhotos.length} photos to localStorage successfully.`;
                    
            } catch (error) {
                document.getElementById('storageResult').innerHTML = `Error saving: ${error.message}`;
            }
        }
        
        function loadFromLocalStorage() {
            try {
                const data = localStorage.getItem('testPhotos');
                if (!data) {
                    document.getElementById('storageResult').innerHTML = 'No test photos found in localStorage.';
                    return;
                }
                
                const parsedData = JSON.parse(data);
                document.getElementById('storageResult').innerHTML = 
                    `Loaded ${parsedData.count} photos from localStorage. Saved at: ${parsedData.timestamp}`;
                
                // Show loaded photos
                const loadedDiv = document.getElementById('loadedPhotos');
                loadedDiv.innerHTML = '<h3>Loaded Photos:</h3>';
                parsedData.photos.forEach((photo, index) => {
                    const img = document.createElement('img');
                    img.src = photo;
                    img.className = 'photo-preview';
                    img.alt = `Loaded Photo ${index + 1}`;
                    loadedDiv.appendChild(img);
                });
                
            } catch (error) {
                document.getElementById('storageResult').innerHTML = `Error loading: ${error.message}`;
            }
        }
        
        function clearLocalStorage() {
            localStorage.removeItem('testPhotos');
            document.getElementById('storageResult').innerHTML = 'Test photos cleared from localStorage.';
            document.getElementById('loadedPhotos').innerHTML = '';
        }
        
        function simulateAssetUpdate() {
            if (convertedPhotos.length === 0) {
                document.getElementById('updateResult').innerHTML = 'No photos to test. Convert photos first.';
                return;
            }
            
            try {
                // Simulate existing asset data
                const existingAsset = {
                    id: 'test-asset-1',
                    name: 'Test Asset',
                    photos: ['data:image/png;base64,existing1', 'data:image/png;base64,existing2']
                };
                
                // Simulate update with new photos
                const updatedAsset = {
                    ...existingAsset,
                    photos: [...existingAsset.photos, ...convertedPhotos]
                };
                
                // Save to localStorage like the app does
                const routes = [{
                    id: 'test-route',
                    name: 'Test Route',
                    assets: [{
                        type: 'test',
                        details: [updatedAsset]
                    }]
                }];
                
                localStorage.setItem('testAssetManagementRoutes', JSON.stringify(routes));
                
                document.getElementById('updateResult').innerHTML = 
                    `Asset update simulation complete. Total photos: ${updatedAsset.photos.length} (${existingAsset.photos.length} existing + ${convertedPhotos.length} new)`;
                    
            } catch (error) {
                document.getElementById('updateResult').innerHTML = `Error in simulation: ${error.message}`;
            }
        }
        
        function checkCurrentData() {
            try {
                const data = localStorage.getItem('assetManagementRoutes');
                if (!data) {
                    document.getElementById('currentDataResult').innerHTML = 'No assetManagementRoutes found in localStorage.';
                    return;
                }
                
                const routes = JSON.parse(data);
                let totalAssets = 0;
                let totalPhotos = 0;
                
                routes.forEach(route => {
                    route.assets.forEach(asset => {
                        if (asset.details) {
                            totalAssets += asset.details.length;
                            asset.details.forEach(detail => {
                                if (detail.photos) {
                                    totalPhotos += detail.photos.length;
                                }
                            });
                        }
                    });
                });
                
                document.getElementById('currentDataResult').innerHTML = 
                    `Found ${routes.length} routes, ${totalAssets} assets, ${totalPhotos} total photos in localStorage.`;
                    
            } catch (error) {
                document.getElementById('currentDataResult').innerHTML = `Error checking data: ${error.message}`;
            }
        }
    </script>
</body>
</html>