"use client";

import React from "react";
import { useForm, useField<PERSON><PERSON>y, FieldError } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { PlusCircle, Trash2 } from "lucide-react";
import { NetworkRoute, formSchema, generateLinkId, generateAssetId, generateNewRouteId } from "@/data/networkRoutes";
import * as z from "zod";

interface RouteFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (values: z.infer<typeof formSchema>) => void;
  initialData?: NetworkRoute | null;
  isCreatingNewRoute: boolean;
}

// Helper type guard to check if an error object has a 'message' property
function isObjectAndHasMessage(error: any): error is { message: string } {
  return typeof error === 'object' && error !== null && 'message' in error && typeof error.message === 'string';
}

const RouteFormDialog: React.FC<RouteFormDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isCreatingNewRoute,
}) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: "",
      name: "",
      links: [],
      assets: [],
      averageSLAHours: 0,
    },
  });

  const { fields: linkFields, append: appendLink, remove: removeLink } = useFieldArray({
    control: form.control,
    name: "links",
  });

  const { fields: assetFields, append: appendAsset, remove: removeAsset } = useFieldArray({
    control: form.control,
    name: "assets",
  });

  React.useEffect(() => {
    if (initialData) {
      form.reset({
        id: initialData.id,
        name: initialData.name,
        links: initialData.links.map(link => ({ ...link })),
        assets: initialData.assets.map(asset => ({ ...asset })),
        averageSLAHours: initialData.averageSLAHours,
      });
    } else if (isCreatingNewRoute) {
      form.reset({
        id: generateNewRouteId(),
        name: "",
        links: [{ id: generateLinkId(), name: "", distance: "", totalLoss: "" }],
        assets: [{ id: generateAssetId(), type: "Handhole", count: 0 }],
        averageSLAHours: 0,
      });
    }
  }, [initialData, isCreatingNewRoute, form]);

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    onSubmit(values);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isCreatingNewRoute ? "Create New Route" : `Edit Route: ${initialData?.name}`}</DialogTitle>
          <DialogDescription>
            {isCreatingNewRoute ? "Fill in the details for the new network route." : "Make changes to the route details and its associated links here. Click save when you're done."}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Route Name
            </Label>
            <Input id="name" {...form.register("name")} className="col-span-3" />
            {form.formState.errors.name && (
              <p className="col-span-4 text-right text-sm text-red-500">{form.formState.errors.name.message}</p>
            )}
          </div>

          <div className="col-span-4">
            <h4 className="font-semibold text-lg mb-2">Links</h4>
            {linkFields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-1 md:grid-cols-4 items-center gap-2 border p-3 rounded-md mb-2">
                <Label htmlFor={`links.${index}.name`} className="text-right md:text-left">
                  Link Name
                </Label>
                <Input id={`links.${index}.name`} {...form.register(`links.${index}.name`)} className="col-span-3" />
                {isObjectAndHasMessage(form.formState.errors.links?.[index]?.name) && (
                  <p className="col-span-4 text-right text-sm text-red-500">{form.formState.errors.links[index]?.name?.message}</p>
                )}

                <Label htmlFor={`links.${index}.distance`} className="text-right md:text-left">
                  Distance (km)
                </Label>
                <Input id={`links.${index}.distance`} {...form.register(`links.${index}.distance`)} className="col-span-3" />
                {isObjectAndHasMessage(form.formState.errors.links?.[index]?.distance) && (
                  <p className="col-span-4 text-right text-sm text-red-500">{form.formState.errors.links[index]?.distance?.message}</p>
                )}

                <Label htmlFor={`links.${index}.totalLoss`} className="text-right md:text-left">
                  Total Loss (dB)
                </Label>
                <Input id={`links.${index}.totalLoss`} {...form.register(`links.${index}.totalLoss`)} className="col-span-3" />
                {isObjectAndHasMessage(form.formState.errors.links?.[index]?.totalLoss) && (
                  <p className="col-span-4 text-right text-sm text-red-500">{form.formState.errors.links[index]?.totalLoss?.message}</p>
                )}

                <div className="col-span-4 flex justify-end">
                  <Button type="button" variant="destructive" size="sm" onClick={() => removeLink(index)}>
                    <Trash2 className="h-4 w-4 mr-2" /> Remove Link
                  </Button>
                </div>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={() => appendLink({ id: generateLinkId(), name: "", distance: "", totalLoss: "" })}
              className="w-full mt-2"
            >
              <PlusCircle className="h-4 w-4 mr-2" /> Add New Link
            </Button>
            {/* Handle array-level errors for 'links' */}
            {(() => {
              const error = form.formState.errors.links;
              if (!error) return null;

              let errorMessage: string | undefined;

              if (typeof error === 'string') {
                errorMessage = error;
              } else if (isObjectAndHasMessage(error)) {
                errorMessage = error.message;
              }

              if (errorMessage) {
                return (
                  <p className="col-span-4 text-center text-sm text-red-500 mt-2">
                    {errorMessage}
                  </p>
                );
              }
              return null;
            })()}
          </div>

          <div className="col-span-4">
            <h4 className="font-semibold text-lg mb-2">Assets</h4>
            {assetFields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-1 md:grid-cols-4 items-center gap-2 border p-3 rounded-md mb-2">
                <Label htmlFor={`assets.${index}.type`} className="text-right md:text-left">
                  Type
                </Label>
                <Input id={`assets.${index}.type`} {...form.register(`assets.${index}.type`)} className="col-span-3" />
                {isObjectAndHasMessage(form.formState.errors.assets?.[index]?.type) && (
                  <p className="col-span-4 text-right text-sm text-red-500">{form.formState.errors.assets[index]?.type?.message}</p>
                )}

                <Label htmlFor={`assets.${index}.count`} className="text-right md:text-left">
                  Count
                </Label>
                <Input id={`assets.${index}.count`} type="number" placeholder="0" {...form.register(`assets.${index}.count`)} className="col-span-3" />
                {isObjectAndHasMessage(form.formState.errors.assets?.[index]?.count) && (
                  <p className="col-span-4 text-right text-sm text-red-500">{form.formState.errors.assets[index]?.count?.message}</p>
                )}

                <div className="col-span-4 flex justify-end">
                  <Button type="button" variant="destructive" size="sm" onClick={() => removeAsset(index)}>
                    <Trash2 className="h-4 w-4 mr-2" /> Remove Asset
                  </Button>
                </div>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={() => appendAsset({ id: generateAssetId(), type: "", count: 0 })}
              className="w-full mt-2"
            >
              <PlusCircle className="h-4 w-4 mr-2" /> Add New Asset Type
            </Button>
            {/* Handle array-level errors for 'assets' */}
            {(() => {
              const error = form.formState.errors.assets;
              if (!error) return null;

              let errorMessage: string | undefined;

              if (typeof error === 'string') {
                errorMessage = error;
              } else if (isObjectAndHasMessage(error)) {
                errorMessage = error.message;
              }

              if (errorMessage) {
                return (
                  <p className="col-span-4 text-center text-sm text-red-500 mt-2">
                    {errorMessage}
                  </p>
                );
              }
              return null;
            })()}
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="averageSLAHours" className="text-right">
              Average SLA (hours)
            </Label>
            <Input id="averageSLAHours" type="number" step="0.1" {...form.register("averageSLAHours")} className="col-span-3" />
            {form.formState.errors.averageSLAHours && (
              <p className="col-span-4 text-right text-sm text-red-500">{form.formState.errors.averageSLAHours.message}</p>
            )}
          </div>
          <DialogFooter>
            <Button type="submit">Save changes</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default RouteFormDialog;