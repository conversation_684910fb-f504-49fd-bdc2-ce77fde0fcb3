import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';

const SettingsPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  
  // State for profile settings form
  const [profileForm, setProfileForm] = useState({
    fullName: user?.full_name || '',
    email: user?.email || '',
    phone: user?.phone || '',
  });
  
  // State for notification settings form
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    maintenanceAlerts: true,
    securityAlerts: true,
    weeklyReports: true,
  });
  
  // State for security settings form
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: true,
    loginNotifications: true,
    sessionTimeout: '30',
  });
  
  // Handler for profile form changes
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({ ...prev, [name]: value }));
  };
  
  // Handler untuk perubahan form notifikasi
  const handleNotificationChange = (key: string, value: boolean) => {
    setNotificationSettings(prev => ({ ...prev, [key]: value }));
  };
  
  // Handler for security form changes
  const handleSecurityChange = (key: string, value: string | boolean) => {
    setSecuritySettings(prev => ({ ...prev, [key]: value }));
  };
  
  // Handler for saving settings
  const handleSaveSettings = (type: string) => {
    setIsSaving(true);
    
    // Simulate saving to server
    setTimeout(() => {
      setIsSaving(false);
      toast({
        title: 'Settings Saved',
        description: `${type} settings have been successfully updated`,
      });
    }, 1000);
  };
  
  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-6">
          <div className="text-center">
            <p>Please login to access settings</p>
          </div>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-gray-500 mt-1">Manage your account preferences and settings</p>
        </div>
        
        <Separator />
        
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full md:w-auto grid-cols-3">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>
          
          {/* Profile Tab */}
          <TabsContent value="profile" className="mt-6">
            <Card className="p-6">
              <h3 className="text-xl font-medium mb-4">Profile Information</h3>
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input 
                    id="fullName" 
                    name="fullName" 
                    value={profileForm.fullName} 
                    onChange={handleProfileChange} 
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="email">Email</Label>
                  <Input 
                    id="email" 
                    name="email" 
                    type="email" 
                    value={profileForm.email} 
                    onChange={handleProfileChange} 
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="phone">Nomor Telepon</Label>
                  <Input 
                    id="phone" 
                    name="phone" 
                    value={profileForm.phone} 
                    onChange={handleProfileChange} 
                  />
                </div>
                
                <Button 
                  onClick={() => handleSaveSettings('profile')} 
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </Card>
          </TabsContent>
          
          {/* Tab Notifikasi */}
          <TabsContent value="notifications" className="mt-6">
            <Card className="p-6">
              <h3 className="text-xl font-medium mb-4">Notification Settings</h3>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Email Notifications</p>
        <p className="text-sm text-gray-500">Receive notifications via email</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.emailNotifications} 
                    onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Notifikasi SMS</p>
                    <p className="text-sm text-gray-500">Receive notifications via SMS</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.smsNotifications} 
                    onCheckedChange={(checked) => handleNotificationChange('smsNotifications', checked)} 
                  />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Maintenance Alerts</p>
            <p className="text-sm text-gray-500">Notifications about maintenance schedule</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.maintenanceAlerts} 
                    onCheckedChange={(checked) => handleNotificationChange('maintenanceAlerts', checked)} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Security Alerts</p>
            <p className="text-sm text-gray-500">Notifications about security activities</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.securityAlerts} 
                    onCheckedChange={(checked) => handleNotificationChange('securityAlerts', checked)} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Weekly Reports</p>
                    <p className="text-sm text-gray-500">Weekly summary of system activities</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.weeklyReports} 
                    onCheckedChange={(checked) => handleNotificationChange('weeklyReports', checked)} 
                  />
                </div>
                
                <Button 
                  onClick={() => handleSaveSettings('notifications')} 
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </Card>
          </TabsContent>
          
          {/* Security Tab */}
          <TabsContent value="security" className="mt-6">
            <Card className="p-6">
              <h3 className="text-xl font-medium mb-4">Security Settings</h3>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Two-Factor Authentication</p>
                    <p className="text-sm text-gray-500">Enhance security with additional verification</p>
                  </div>
                  <Switch 
                    checked={securitySettings.twoFactorAuth} 
                    onCheckedChange={(checked) => handleSecurityChange('twoFactorAuth', checked)} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Login Notifications</p>
                <p className="text-sm text-gray-500">Get notified when there are new logins</p>
                  </div>
                  <Switch 
                    checked={securitySettings.loginNotifications} 
                    onCheckedChange={(checked) => handleSecurityChange('loginNotifications', checked)} 
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                  <Input 
                    id="sessionTimeout" 
                    name="sessionTimeout" 
                    type="number" 
                    value={securitySettings.sessionTimeout} 
                    onChange={(e) => handleSecurityChange('sessionTimeout', e.target.value)} 
                  />
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h4 className="font-medium">Change Password</h4>
                  <div className="grid gap-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <Input id="currentPassword" type="password" />
                  </div>
                  
                  <div className="grid gap-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input id="newPassword" type="password" />
                  </div>
                  
                  <div className="grid gap-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input id="confirmPassword" type="password" />
                  </div>
                </div>
                
                <div className="flex flex-col space-y-2">
                  <Button 
                    onClick={() => handleSaveSettings('security')} 
                    disabled={isSaving}
                  >
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </Button>
                  
                  <Button variant="outline" className="mt-2">
                    Change Password
                  </Button>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SettingsPage;