"use client";

import React, { useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { usePortData } from "@/contexts/PortDataContext";

const ConnectedTablesCard: React.FC = () => {
  // Use shared port data from context
  const { table1Data, table2Data } = usePortData();
  
  // Group data by route
  const routeGroups = useMemo(() => {
    // Get all unique routes from both tables
    const allRoutes = new Set([
      ...table1Data.map(item => item.route),
      ...table2Data.map(item => item.route)
    ]);
    
    // Group data by route
    const groups = Array.from(allRoutes).map(route => {
      const table1RouteData = table1Data.filter(item => item.route === route);
      const table2RouteData = table2Data.filter(item => item.route === route);
      
      // Get most common ODC name from table1RouteData
      const table1OdcCounts = table1RouteData.reduce((acc, item) => {
        acc[item.odcName] = (acc[item.odcName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const table1MostCommonOdc = Object.keys(table1OdcCounts).length > 0
        ? Object.keys(table1OdcCounts).reduce((a, b) => 
            table1OdcCounts[a] > table1OdcCounts[b] ? a : b
          )
        : 'ODC-A';

      // Get most common ODC name from table2RouteData
      const table2OdcCounts = table2RouteData.reduce((acc, item) => {
        acc[item.odcName] = (acc[item.odcName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const table2MostCommonOdc = Object.keys(table2OdcCounts).length > 0
        ? Object.keys(table2OdcCounts).reduce((a, b) => 
            table2OdcCounts[a] > table2OdcCounts[b] ? a : b
          )
        : 'ODC-B';

      return {
        route,
        table1Data: table1RouteData,
        table2Data: table2RouteData,
        tableATitle: table1MostCommonOdc,
        tableBTitle: table2MostCommonOdc
      };
    });
    
    return groups;
  }, [table1Data, table2Data]);

  const renderTableCell = (value: string) => {
    return <span className="text-xs">{value}</span>;
  };

  const renderRouteCard = (routeGroup: any, index: number) => (
    <Card key={routeGroup.route} className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 mb-6">
      <CardHeader>
        <CardTitle className="text-xl text-slate-800 dark:text-slate-200">{routeGroup.route}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-start gap-8">
          {/* Table 1 */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg p-4" style={{width: '50.4%'}}>
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">{routeGroup.tableATitle}</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full text-xs">
                <thead>
                  <tr className="border-b border-blue-200 dark:border-blue-700">
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200">No Port</th>
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200">Traffic Name</th>
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200">Route</th>
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200">ODC Name</th>
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200">OTDR Distance</th>
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200">Ground Distance</th>
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200">Total Loss</th>
                    <th className="text-left p-2 font-semibold text-blue-800 dark:text-blue-200" style={{minWidth: '80px'}}>RSL</th>
                  </tr>
                </thead>
                <tbody>
                  {routeGroup.table1Data.map((row: any) => (
                    <tr key={`${routeGroup.route}-t1-${row.id}`} className="border-b border-blue-100 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900">
                      <td className="p-2">{renderTableCell(row.noPort)}</td>
                      <td className="p-2">{renderTableCell(row.trafficName)}</td>
                      <td className="p-2">{renderTableCell(row.route)}</td>
                      <td className="p-2">{renderTableCell(row.odcName)}</td>
                      <td className="p-2">{renderTableCell(row.otdrDistance)}</td>
                      <td className="p-2">{renderTableCell(row.groundDistance)}</td>
                      <td className="p-2">{renderTableCell(row.totalLoss)}</td>
                      <td className="p-2" style={{minWidth: '80px'}}>{renderTableCell(row.rsl)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Connection Lines */}
          <div className="flex flex-col justify-center items-center h-full" style={{width: '12%'}}>
            <div className="relative flex flex-col justify-center items-center" style={{height: '100%', marginTop: '20mm'}}>
              {/* First Line - Blue */}
              <div className="h-0.5 bg-blue-500 relative" style={{width: '200px', marginBottom: '15mm'}}>
                <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
                  <div className="w-0 h-0 border-l-8 border-l-blue-500 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
                </div>
              </div>
              {/* Second Line - Orange */}
              <div className="h-0.5 bg-orange-500 relative" style={{width: '200px'}}>
                <div className="absolute left-0 top-1/2 transform -translate-y-1/2">
                  <div className="w-0 h-0 border-r-8 border-r-orange-500 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Table 2 */}
          <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg p-4" style={{width: '50.4%'}}>
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">{routeGroup.tableBTitle}</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full text-xs">
                <thead>
                  <tr className="border-b border-green-200 dark:border-green-700">
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200">No Port</th>
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200">Traffic Name</th>
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200">Route</th>
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200">ODC Name</th>
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200">OTDR Distance</th>
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200">Ground Distance</th>
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200">Total Loss</th>
                    <th className="text-left p-2 font-semibold text-green-800 dark:text-green-200" style={{minWidth: '80px'}}>RSL</th>
                  </tr>
                </thead>
                <tbody>
                  {routeGroup.table2Data.map((row: any) => (
                    <tr key={`${routeGroup.route}-t2-${row.id}`} className="border-b border-green-100 dark:border-green-800 hover:bg-green-50 dark:hover:bg-green-900">
                      <td className="p-2">{renderTableCell(row.noPort)}</td>
                      <td className="p-2">{renderTableCell(row.trafficName)}</td>
                      <td className="p-2">{renderTableCell(row.route)}</td>
                      <td className="p-2">{renderTableCell(row.odcName)}</td>
                      <td className="p-2">{renderTableCell(row.otdrDistance)}</td>
                      <td className="p-2">{renderTableCell(row.groundDistance)}</td>
                      <td className="p-2">{renderTableCell(row.totalLoss)}</td>
                      <td className="p-2" style={{minWidth: '80px'}}>{renderTableCell(row.rsl)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {routeGroups.map((routeGroup, index) => renderRouteCard(routeGroup, index))}
    </div>
  );
};

export default ConnectedTablesCard;