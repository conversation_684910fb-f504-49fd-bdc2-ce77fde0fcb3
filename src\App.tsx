import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import RouteDetail from "./pages/RouteDetail";
import TroubleTicketPage from "./pages/TroubleTicketPage";
import TroubleTicketDetail from "./pages/TroubleTicketDetail";
import CoreManagementPage from "./pages/CoreManagementPage";
import AssetManagementPage from "./pages/AssetManagementPage";
import PortManagementPage from "./pages/PortManagementPage";
import PatrolPage from "./pages/PatrolPage";
import SORPage from "./pages/SORPage";
import ProblemHandlingPage from "./pages/ProblemHandlingPage";
import LoginPage from "./pages/LoginPage";
import UnauthorizedPage from "./pages/UnauthorizedPage";
import UserManagementPage from "./pages/UserManagementPage";
import RoleManagementPage from "./pages/RoleManagementPage";
import ProfilePage from "./pages/ProfilePage";
import SettingsPage from "./pages/SettingsPage";
import React from "react";
import Sidebar from "./components/Sidebar";
import { initialSampleTickets } from "./components/TroubleTicketManagement";
import { PortDataProvider } from "./contexts/PortDataContext";
import { PatrolDataProvider } from "./contexts/PatrolDataContext";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => {
  const [tickets, setTickets] = React.useState(initialSampleTickets);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AuthProvider>
          <PortDataProvider>
            <PatrolDataProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
                <Routes>
                  {/* Rute publik */}
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/unauthorized" element={<UnauthorizedPage />} />
                  
                  {/* Rute terproteksi yang memerlukan autentikasi */}
                  <Route path="/" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <Index tickets={tickets} />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/route/:id" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <RouteDetail />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/trouble-tickets" element={
                    <ProtectedRoute requiredPermission="view_tickets">
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <TroubleTicketPage tickets={tickets} setTickets={setTickets} />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/trouble-tickets/:id" element={
                    <ProtectedRoute requiredPermission="view_tickets">
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <TroubleTicketDetail tickets={tickets} />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/core-management" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <CoreManagementPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/port-management" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <PortManagementPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/asset-management" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <AssetManagementPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/patrol" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <PatrolPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/patrol/sor" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <SORPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/problem-handling" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <ProblemHandlingPage tickets={tickets} setTickets={setTickets} />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/problem-handling/trouble-tickets" element={
                    <ProtectedRoute requiredPermission="view_tickets">
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <ProblemHandlingPage tickets={tickets} setTickets={setTickets} />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/problem-handling/maintenance" element={
                    <ProtectedRoute requiredPermission="view_maintenance">
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <ProblemHandlingPage tickets={tickets} setTickets={setTickets} openMaintenanceForm={true} />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  {/* User and role management routes (admin only) */}
                  <Route path="/user-management" element={
                    <ProtectedRoute requiredPermission="manage_users">
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <UserManagementPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/role-management" element={
                    <ProtectedRoute requiredPermission="manage_users">
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <RoleManagementPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  {/* Rute profil dan pengaturan */}
                  <Route path="/profile" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <ProfilePage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  <Route path="/settings" element={
                    <ProtectedRoute>
                      <div className="flex min-h-screen">
                        <div className="w-80 flex-shrink-0">
                          <Sidebar />
                        </div>
                        <div className="flex-grow">
                          <SettingsPage />
                        </div>
                      </div>
                    </ProtectedRoute>
                  } />
                  
                  {/* Redirect dari root ke dashboard jika sudah login */}
                  <Route path="/dashboard" element={<Navigate to="/" replace />} />
                  
                  {/* Rute 404 */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
            </PatrolDataProvider>
          </PortDataProvider>
        </AuthProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;