import React, { useState, useEffect } from "react";
import { initialSampleRoutes, NetworkRoute, Asset, AssetDetail } from "../data/networkRoutes";
import RouteAssetCard from "../components/RouteAssetCard";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Button } from "../components/ui/button";
import { Search, Filter, FileText, Download, RotateCcw } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../components/ui/dropdown-menu";



export default function AssetManagementPage() {
  const [routes, setRoutes] = useState<NetworkRoute[]>(() => {
    const savedRoutes = localStorage.getItem('assetManagementRoutes');
    return savedRoutes ? JSON.parse(savedRoutes) : initialSampleRoutes;
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Save to localStorage whenever routes change
  useEffect(() => {
    localStorage.setItem('assetManagementRoutes', JSON.stringify(routes));
  }, [routes]);

  const handleResetData = () => {
    if (confirm('Are you sure you want to reset all asset data to initial state? This action cannot be undone.')) {
      localStorage.removeItem('assetManagementRoutes');
      setRoutes(initialSampleRoutes);
    }
  };

  const handleUpdateAssets = (routeId: string, updatedAssets: Asset[]) => {
    setRoutes(prevRoutes => {
      const newRoutes = prevRoutes.map(route => 
        route.id === routeId 
          ? { ...route, assets: updatedAssets }
          : route
      );
      localStorage.setItem('assetManagementRoutes', JSON.stringify(newRoutes));
      return newRoutes;
    });
  };

  const handleAssetUpdate = (updatedAsset: AssetDetail) => {
    setRoutes(prevRoutes => {
      const newRoutes = prevRoutes.map(route => ({
        ...route,
        assets: route.assets.map(asset => {
          if (asset.details?.some(detail => detail.id === updatedAsset.id)) {
            return {
              ...asset,
              details: asset.details?.map(detail => 
                detail.id === updatedAsset.id ? updatedAsset : detail
              )
            };
          }
          return asset;
        })
      }));
      localStorage.setItem('assetManagementRoutes', JSON.stringify(newRoutes));
      return newRoutes;
    });
  };

  const filteredRoutes = routes.filter(route => {
    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || route.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalAssets = routes.reduce((total, route) => {
    return total + route.assets.reduce((routeTotal, asset) => {
      return routeTotal + (asset.details ? asset.details.length : asset.count);
    }, 0);
  }, 0);

  const totalRoutes = routes.length;
  const operationalRoutes = routes.filter(route => route.status === "operational").length;

  // Function to get all assets with route information
  const getAllAssetsData = () => {
    const allAssets: (AssetDetail & { routeId: string; routeName: string; assetType: string })[] = [];
    
    routes.forEach(route => {
      route.assets.forEach(asset => {
        if (asset.details) {
          asset.details.forEach(detail => {
            allAssets.push({
              ...detail,
              routeId: route.id,
              routeName: route.name,
              assetType: asset.type
            });
          });
        }
      });
    });
    
    return allAssets;
  };

  // Print functions for different formats
  const handlePrintPDF = () => {
    const allAssets = getAllAssetsData();
    const printWindow = window.open('', '_blank');
    
    if (!printWindow) {
      handlePrintPDFFallback();
      return;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Asset Management Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #1f2937; margin-bottom: 10px; }
            .header p { color: #6b7280; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #d1d5db; padding: 8px; text-align: left; font-size: 12px; }
            th { background-color: #f3f4f6; font-weight: bold; }
            tr:nth-child(even) { background-color: #f9fafb; }
            .footer { margin-top: 30px; text-align: center; color: #6b7280; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Asset Management Report</h1>
            <p>Generated on ${new Date().toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</p>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>Asset ID</th>
                <th>Asset Name</th>
                <th>Route ID</th>
                <th>Route Name</th>
                <th>Asset Type</th>
                <th>Category</th>
                <th>Brand</th>
                <th>Model</th>
                <th>Area</th>
                <th>Address</th>
                <th>Latitude</th>
                <th>Longitude</th>
                <th>Installation Date</th>
                <th>Condition</th>
                <th>Last Maintenance</th>
                <th>Last Checked</th>
              </tr>
            </thead>
            <tbody>
              ${allAssets.map(asset => `
                <tr>
                  <td>${asset.id}</td>
                  <td>${asset.name}</td>
                  <td>${asset.routeId}</td>
                  <td>${asset.routeName}</td>
                  <td>${asset.assetType}</td>
                  <td>${asset.category}</td>
                  <td>${asset.brand}</td>
                  <td>${asset.model || 'N/A'}</td>
                  <td>${asset.area}</td>
                  <td>${asset.location.address}</td>
                  <td>${asset.location.latitude}</td>
                  <td>${asset.location.longitude}</td>
                  <td>${asset.installationDate || 'N/A'}</td>
                  <td>${asset.condition}</td>
                  <td>${asset.lastMaintenance || 'N/A'}</td>
                  <td>${asset.lastChecked || 'N/A'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          
          <div class="footer">
            <p>Total Assets: ${allAssets.length} | Total Routes: ${totalRoutes}</p>
            <p>Asset Management System - Generated Report</p>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  const handlePrintPDFFallback = () => {
    const allAssets = getAllAssetsData();
    const printContent = document.createElement('div');
    printContent.innerHTML = `
      <div style="font-family: Arial, sans-serif; margin: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #1f2937; margin-bottom: 10px;">Asset Management Report</h1>
          <p style="color: #6b7280;">Generated on ${new Date().toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}</p>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
          <thead>
            <tr>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Asset ID</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Asset Name</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Route ID</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Route Name</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Asset Type</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Category</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Brand</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Model</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Area</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Address</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Latitude</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Longitude</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Installation Date</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Condition</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Last Maintenance</th>
              <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6; font-weight: bold; font-size: 12px;">Last Checked</th>
            </tr>
          </thead>
          <tbody>
            ${allAssets.map((asset, index) => `
              <tr style="${index % 2 === 0 ? 'background-color: #f9fafb;' : ''}">
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.id}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.name}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.routeId}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.routeName}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.assetType}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.category}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.brand}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.model || 'N/A'}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.area}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.location.address}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.location.latitude}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.location.longitude}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.installationDate || 'N/A'}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.condition}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.lastMaintenance || 'N/A'}</td>
                <td style="border: 1px solid #d1d5db; padding: 8px; font-size: 12px;">${asset.lastChecked || 'N/A'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div style="margin-top: 30px; text-align: center; color: #6b7280; font-size: 12px;">
          <p>Total Assets: ${allAssets.length} | Total Routes: ${totalRoutes}</p>
          <p>Asset Management System - Generated Report</p>
        </div>
      </div>
    `;

    const originalContent = document.body.innerHTML;
    document.body.innerHTML = printContent.innerHTML;
    window.print();
    document.body.innerHTML = originalContent;
    window.location.reload();
  };

  const handleExportCSV = () => {
    const allAssets = getAllAssetsData();
    const headers = [
      'Asset ID', 'Asset Name', 'Route ID', 'Route Name', 'Asset Type', 'Category',
      'Brand', 'Model', 'Area', 'Address', 'Latitude', 'Longitude',
      'Installation Date', 'Condition', 'Last Maintenance', 'Last Checked'
    ];
    
    const csvContent = [
      headers.join(','),
      ...allAssets.map(asset => [
        asset.id,
        `"${asset.name}"`,
        asset.routeId,
        `"${asset.routeName}"`,
        asset.assetType,
        asset.category,
        asset.brand,
        asset.model || 'N/A',
        asset.area,
        `"${asset.location.address}"`,
        asset.location.latitude,
        asset.location.longitude,
        asset.installationDate || 'N/A',
        asset.condition,
        asset.lastMaintenance || 'N/A',
        asset.lastChecked || 'N/A'
      ].join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `asset_management_report_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExportXLS = () => {
    const allAssets = getAllAssetsData();
    const headers = [
      'Asset ID', 'Asset Name', 'Route ID', 'Route Name', 'Asset Type', 'Category',
      'Brand', 'Model', 'Area', 'Address', 'Latitude', 'Longitude',
      'Installation Date', 'Condition', 'Last Maintenance', 'Last Checked'
    ];
    
    let xlsContent = `<?xml version="1.0"?>\n<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">\n<Worksheet ss:Name="Asset Management Report">\n<Table>\n`;
    
    // Add headers
    xlsContent += '<Row>\n';
    headers.forEach(header => {
      xlsContent += `<Cell><Data ss:Type="String">${header}</Data></Cell>\n`;
    });
    xlsContent += '</Row>\n';
    
    // Add data rows
    allAssets.forEach(asset => {
      xlsContent += '<Row>\n';
      [
        asset.id,
        asset.name,
        asset.routeId,
        asset.routeName,
        asset.assetType,
        asset.category,
        asset.brand,
        asset.model || 'N/A',
        asset.area,
        asset.location.address,
        asset.location.latitude,
        asset.location.longitude,
        asset.installationDate || 'N/A',
        asset.condition,
        asset.lastMaintenance || 'N/A',
        asset.lastChecked || 'N/A'
      ].forEach(value => {
        const dataType = typeof value === 'number' ? 'Number' : 'String';
        xlsContent += `<Cell><Data ss:Type="${dataType}">${value}</Data></Cell>\n`;
      });
      xlsContent += '</Row>\n';
    });
    
    xlsContent += '</Table>\n</Worksheet>\n</Workbook>';
    
    const blob = new Blob([xlsContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `asset_management_report_${new Date().toISOString().split('T')[0]}.xls`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Asset Management</h1>
            <p className="text-gray-600 mt-1">Manage and monitor assets across all network routes</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              onClick={handleResetData}
              className="flex items-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Reset Data</span>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={handlePrintPDF}>
                  <FileText className="h-4 w-4 mr-2" />
                  Export as PDF
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExportXLS}>
                  <FileText className="h-4 w-4 mr-2" />
                  Export as XLS
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExportCSV}>
                  <FileText className="h-4 w-4 mr-2" />
                  Export as CSV
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
             <div className="flex space-x-4 text-sm">
               <div className="bg-blue-50 px-4 py-2 rounded-lg">
                 <p className="text-blue-600 font-semibold">{totalRoutes}</p>
                 <p className="text-blue-500">Total Routes</p>
               </div>
               <div className="bg-green-50 px-4 py-2 rounded-lg">
                 <p className="text-green-600 font-semibold">{operationalRoutes}</p>
                 <p className="text-green-500">Operational</p>
               </div>
               <div className="bg-purple-50 px-4 py-2 rounded-lg">
                 <p className="text-purple-600 font-semibold">{totalAssets}</p>
                 <p className="text-purple-500">Total Assets</p>
               </div>
             </div>
           </div>
         </div>

        {/* Filters */}
        <div className="flex space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search routes by name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="operational">Operational</SelectItem>
                <SelectItem value="degraded">Degraded</SelectItem>
                <SelectItem value="down">Down</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Routes Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredRoutes.map((route) => (
          <RouteAssetCard
            key={route.id}
            route={route}
            onUpdateAssets={handleUpdateAssets}
            onAssetUpdate={handleAssetUpdate}
          />
        ))}
      </div>

      {filteredRoutes.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">No routes found</div>
          <p className="text-gray-500">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};