import React, { useState } from "react";
import { initialSampleRoutes, NetworkRoute, Asset } from "../data/networkRoutes";
import RouteAssetCard from "../components/RouteAssetCard";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Search, Filter } from "lucide-react";



export default function AssetManagementPage() {
  const [routes, setRoutes] = useState<NetworkRoute[]>(initialSampleRoutes);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  const handleUpdateAssets = (routeId: string, updatedAssets: Asset[]) => {
    setRoutes(prevRoutes => 
      prevRoutes.map(route => 
        route.id === routeId 
          ? { ...route, assets: updatedAssets }
          : route
      )
    );
  };

  const filteredRoutes = routes.filter(route => {
    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || route.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalAssets = routes.reduce((total, route) => {
    return total + route.assets.reduce((routeTotal, asset) => routeTotal + asset.count, 0);
  }, 0);

  const totalRoutes = routes.length;
  const operationalRoutes = routes.filter(route => route.status === "operational").length;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Asset Management</h1>
            <p className="text-gray-600 mt-1">Manage and monitor assets across all network routes</p>
          </div>
          <div className="flex space-x-4 text-sm">
            <div className="bg-blue-50 px-4 py-2 rounded-lg">
              <p className="text-blue-600 font-semibold">{totalRoutes}</p>
              <p className="text-blue-500">Total Routes</p>
            </div>
            <div className="bg-green-50 px-4 py-2 rounded-lg">
              <p className="text-green-600 font-semibold">{operationalRoutes}</p>
              <p className="text-green-500">Operational</p>
            </div>
            <div className="bg-purple-50 px-4 py-2 rounded-lg">
              <p className="text-purple-600 font-semibold">{totalAssets}</p>
              <p className="text-purple-500">Total Assets</p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search routes by name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="operational">Operational</SelectItem>
                <SelectItem value="degraded">Degraded</SelectItem>
                <SelectItem value="down">Down</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Routes Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredRoutes.map((route) => (
          <RouteAssetCard
            key={route.id}
            route={route}
            onUpdateAssets={handleUpdateAssets}
          />
        ))}
      </div>

      {filteredRoutes.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">No routes found</div>
          <p className="text-gray-500">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};