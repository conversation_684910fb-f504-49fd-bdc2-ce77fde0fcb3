import React, { createContext, useContext, useState, ReactNode } from 'react';

interface Finding {
  id: string;
  type: 'cable_expose' | 'hdpe_expose' | 'third_party_work' | 'network_threat';
  location: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved';
  reportedBy: string;
  reportedDate: string;
  route?: string;
  affectedAssets?: string[];
  impactLevel?: 'none' | 'low' | 'medium' | 'high';
  remark?: string;
  findingLength?: string;
  thirdPartyContact?: string;
  photos?: string[]; // Array of photo URLs or base64 strings
}

interface Asset {
  id: string;
  name: string;
  type: 'odc' | 'closure' | 'cable' | 'equipment';
  location: string;
  condition: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  completeness: number;
  lastInspection: string;
  changes: string[];
  route: string;
  notes?: string;
  maintenanceHistory?: string[];
  patrolFindings?: string[]; // IDs of related findings
  riskLevel?: 'low' | 'medium' | 'high';
  photos?: string[]; // Array of photo URLs or base64 strings
}

interface OTDRMeasurement {
  id: string;
  route: string;
  core: string;
  distance: string;
  loss: string;
  reflectance: string;
  measurementDate: string;
  technician: string;
  status: 'pass' | 'fail' | 'warning';
  notes?: string;
  traffic?: string;
  threshold?: {
    lossThreshold: number;
    reflectanceThreshold: number;
  };
  photos?: string[]; // Array of photo URLs or base64 strings
}

interface PatrolDataContextType {
  // Data states
  findings: Finding[];
  assets: Asset[];
  otdrMeasurements: OTDRMeasurement[];
  
  // Setters
  setFindings: React.Dispatch<React.SetStateAction<Finding[]>>;
  setAssets: React.Dispatch<React.SetStateAction<Asset[]>>;
  setOtdrMeasurements: React.Dispatch<React.SetStateAction<OTDRMeasurement[]>>;
  
  // Helper functions
  addFinding: (finding: Omit<Finding, 'id'>) => void;
  updateFinding: (id: string, updates: Partial<Finding>) => void;
  addAsset: (asset: Omit<Asset, 'id'>) => void;
  updateAsset: (id: string, updates: Partial<Asset>) => void;
  addOTDRMeasurement: (measurement: Omit<OTDRMeasurement, 'id'>) => void;
  deleteFinding: (id: string) => void;
  deleteAsset: (id: string) => void;
  deleteOTDRMeasurement: (id: string) => void;
  updateOTDRMeasurement: (id: string, updates: Partial<OTDRMeasurement>) => void;
  
  // Analytics functions
  getCriticalFindings: () => Finding[];
  getAssetsByCondition: (condition: Asset['condition']) => Asset[];
  getFailedOTDRMeasurements: () => OTDRMeasurement[];
  getRouteRiskLevel: (route: string) => 'low' | 'medium' | 'high';
  getAffectedRoutes: () => string[];
  
  // Integration functions
  generateTroubleTicketFromFinding: (findingId: string) => any;
  updateAssetConditionFromFinding: (findingId: string, assetId: string) => void;
}

const PatrolDataContext = createContext<PatrolDataContextType | undefined>(undefined);

export const usePatrolData = () => {
  const context = useContext(PatrolDataContext);
  if (context === undefined) {
    throw new Error('usePatrolData must be used within a PatrolDataProvider');
  }
  return context;
};

export const PatrolDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Initial sample data
  const [findings, setFindings] = useState<Finding[]>([
    {
      id: '1',
      type: 'cable_expose',
      location: 'Jl. Sudirman KM 5',
      description: 'Fiber optic cable exposed due to road excavation',
      severity: 'high',
      status: 'open',
      reportedBy: 'Teknisi A',
      reportedDate: '2024-01-15',
      route: 'Route-A',
      affectedAssets: ['ODC-001', 'Closure-005'],
      impactLevel: 'high',
      remark: 'Immediate action needed to prevent further damage',
      findingLength: '2.5 meter',
      thirdPartyContact: 'PT. Konstruksi ABC - 021-12345678',
      photos: [
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpbmRpbmcgUGhvdG8gMTwvdGV4dD48L3N2Zz4=',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjYmJiIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzc3NyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpbmRpbmcgUGhvdG8gMjwvdGV4dD48L3N2Zz4='
      ]
    },
    {
      id: '2',
      type: 'third_party_work',
      location: 'Jl. Thamrin KM 2',
      description: 'PLN excavation work near cable route',
      severity: 'medium',
      status: 'in_progress',
      reportedBy: 'Teknisi B',
      reportedDate: '2024-01-14',
      route: 'Route-B',
      affectedAssets: ['Cable-003'],
      impactLevel: 'medium',
      remark: 'Close monitoring required during work progress',
      findingLength: '1.0 meter',
      thirdPartyContact: 'PLN Distribusi - 021-87654321',
      photos: [
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPldvcmsgUGhvdG88L3RleHQ+PC9zdmc+'
      ]
    }
  ]);

  const [assets, setAssets] = useState<Asset[]>([
    {
      id: 'ODC-001',
      name: 'ODC-001',
      type: 'odc',
      location: 'Jl. Gatot Subroto',
      condition: 'fair', // Degraded due to finding
      completeness: 85, // Reduced due to patrol findings
      lastInspection: '2024-01-10',
      changes: ['Port label addition', 'Dust cleaning', 'Repair due to patrol findings'],
      route: 'Route-A',
      notes: 'Asset in fair condition due to patrol findings',
      maintenanceHistory: ['2024-01-01: Routine cleaning', '2024-01-10: Repair due to patrol'],
      patrolFindings: ['1'],
      riskLevel: 'high',
      photos: [
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZTZmM2ZmIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzNjZjYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9EQyBQaG90bzwvdGV4dD48L3N2Zz4=',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmOGZmIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzNjZjYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9EQyBEZXRhaWw8L3RleHQ+PC9zdmc+',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmOGZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzNjZjYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9EQyBJbnNpZGU8L3RleHQ+PC9zdmc+',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmFmYmZjIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzNjZjYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9EQyBMYWJlbDwvdGV4dD48L3N2Zz4='
      ]
    },
    {
      id: 'Closure-005',
      name: 'Closure-005',
      type: 'closure',
      location: 'Jl. Sudirman KM 3',
      condition: 'poor', // Affected by cable expose finding
      completeness: 70,
      lastInspection: '2024-01-12',
      changes: ['Seal repair', 'Splice tray replacement', 'Additional protection due to findings'],
      route: 'Route-A',
      notes: 'Closure requires special attention',
      maintenanceHistory: ['2023-12-15: Initial installation', '2024-01-12: Seal repair due to patrol'],
      patrolFindings: ['1'],
      riskLevel: 'high'
    }
  ]);

  const [otdrMeasurements, setOtdrMeasurements] = useState<OTDRMeasurement[]>([
    {
      id: '1',
      route: 'Route-A',
      core: 'Core-1',
      distance: '15.2 km',
      loss: '0.45 dB/km', // Higher loss due to patrol findings
      reflectance: '-42 dB', // Worse reflectance
      measurementDate: '2024-01-15',
      technician: 'Teknisi C',
      status: 'warning', // Changed from pass to warning
      notes: 'Loss increased due to patrol findings - cable exposed',
      traffic: 'High Traffic - 80% utilization',
      threshold: { lossThreshold: 0.35, reflectanceThreshold: -45 },
      photos: [
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZmNGU2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2Q5NzcwNiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9URFIgUmVzdWx0PC90ZXh0Pjwvc3ZnPg==',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmVmM2M3Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2Q5NzcwNiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9URFIgR3JhcGg8L3RleHQ+PC9zdmc+',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmVmN2VkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2Q5NzcwNiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9URFIgRGF0YTwvdGV4dD48L3N2Zz4='
      ]
    },
    {
      id: '2',
      route: 'Route-B',
      core: 'Core-2',
      distance: '12.8 km',
      loss: '0.35 dB/km',
      reflectance: '-42 dB',
      measurementDate: '2024-01-14',
      technician: 'Teknisi D',
      status: 'warning',
      notes: 'Close monitoring due to third party work',
      traffic: 'Medium Traffic - 45% utilization',
      threshold: { lossThreshold: 0.30, reflectanceThreshold: -45 },
      photos: [
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmOWZmIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzA3NzNiNyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk9URFIgVGVzdDwvdGV4dD48L3N2Zz4='
      ]
    }
  ]);

  // Helper functions
  const addFinding = (finding: Omit<Finding, 'id'>) => {
    const newFinding = {
      ...finding,
      id: Date.now().toString()
    };
    setFindings(prev => [...prev, newFinding]);
    
    // Auto-impact on assets and OTDR if critical
    if (finding.severity === 'critical' || finding.severity === 'high') {
      updateRelatedAssets(newFinding);
      updateRelatedOTDR(newFinding);
    }
  };

  const updateFinding = (id: string, updates: Partial<Finding>) => {
    setFindings(prev => prev.map(finding => 
      finding.id === id ? { ...finding, ...updates } : finding
    ));
  };

  const addAsset = (asset: Omit<Asset, 'id'>) => {
    const newAsset = {
      ...asset,
      id: Date.now().toString()
    };
    setAssets(prev => [...prev, newAsset]);
  };

  const updateAsset = (id: string, updates: Partial<Asset>) => {
    setAssets(prev => prev.map(asset => 
      asset.id === id ? { ...asset, ...updates } : asset
    ));
  };

  const addOTDRMeasurement = (measurement: Omit<OTDRMeasurement, 'id'>) => {
    const newMeasurement = {
      ...measurement,
      id: Date.now().toString()
    };
    setOtdrMeasurements(prev => [...prev, newMeasurement]);
  };

  const deleteFinding = (id: string) => {
    setFindings(prev => prev.filter(finding => finding.id !== id));
  };

  const deleteAsset = (id: string) => {
    setAssets(prev => prev.filter(asset => asset.id !== id));
  };

  const deleteOTDRMeasurement = (id: string) => {
    setOtdrMeasurements(prev => prev.filter(measurement => measurement.id !== id));
  };

  const updateOTDRMeasurement = (id: string, updates: Partial<OTDRMeasurement>) => {
    setOtdrMeasurements(prev => prev.map(measurement => 
      measurement.id === id ? { ...measurement, ...updates } : measurement
    ));
  };

  // Analytics functions
  const getCriticalFindings = () => {
    return findings.filter(finding => 
      finding.severity === 'critical' || finding.severity === 'high'
    );
  };

  const getAssetsByCondition = (condition: Asset['condition']) => {
    return assets.filter(asset => asset.condition === condition);
  };

  const getFailedOTDRMeasurements = () => {
    return otdrMeasurements.filter(measurement => 
      measurement.status === 'fail' || measurement.status === 'warning'
    );
  };

  const getRouteRiskLevel = (route: string): 'low' | 'medium' | 'high' => {
    const routeFindings = findings.filter(f => f.route === route);
    const criticalCount = routeFindings.filter(f => f.severity === 'critical').length;
    const highCount = routeFindings.filter(f => f.severity === 'high').length;
    
    if (criticalCount > 0 || highCount > 2) return 'high';
    if (highCount > 0 || routeFindings.length > 3) return 'medium';
    return 'low';
  };

  const getAffectedRoutes = () => {
    const routes = new Set(findings.map(f => f.route).filter(Boolean));
    return Array.from(routes) as string[];
  };

  // Integration functions
  const generateTroubleTicketFromFinding = (findingId: string) => {
    const finding = findings.find(f => f.id === findingId);
    if (!finding) return null;

    return {
      id: `TT-${Date.now()}`,
      title: `Patrol Finding: ${finding.type.replace('_', ' ').toUpperCase()}`,
      description: `${finding.description}\n\nLokasi: ${finding.location}\nRoute: ${finding.route}\nSeverity: ${finding.severity}`,
      priority: finding.severity === 'critical' ? 'urgent' : 
                finding.severity === 'high' ? 'high' : 'medium',
      status: 'open',
      assignee: finding.reportedBy,
      createdAt: new Date().toISOString(),
      route: finding.route,
      category: 'patrol_finding',
      patrolFindingId: findingId
    };
  };

  const updateAssetConditionFromFinding = (findingId: string, assetId: string) => {
    const finding = findings.find(f => f.id === findingId);
    if (!finding) return;

    const conditionImpact = {
      'critical': 'critical',
      'high': 'poor',
      'medium': 'fair',
      'low': 'good'
    } as const;

    updateAsset(assetId, {
      condition: conditionImpact[finding.severity] as Asset['condition'],
      patrolFindings: [...(assets.find(a => a.id === assetId)?.patrolFindings || []), findingId],
      changes: [...(assets.find(a => a.id === assetId)?.changes || []), 
               `Condition affected by patrol finding: ${finding.description}`]
    });
  };

  // Helper functions for auto-impact
  const updateRelatedAssets = (finding: Finding) => {
    if (finding.affectedAssets) {
      finding.affectedAssets.forEach(assetId => {
        updateAssetConditionFromFinding(finding.id, assetId);
      });
    }
  };

  const updateRelatedOTDR = (finding: Finding) => {
    if (finding.route && (finding.severity === 'critical' || finding.severity === 'high')) {
      setOtdrMeasurements(prev => prev.map(measurement => {
        if (measurement.route === finding.route) {
          return {
            ...measurement,
            status: 'warning' as const,
            notes: `${measurement.notes || ''} - Affected by patrol finding: ${finding.description}`.trim()
          };
        }
        return measurement;
      }));
    }
  };

  const value: PatrolDataContextType = {
    findings,
    assets,
    otdrMeasurements,
    setFindings,
    setAssets,
    setOtdrMeasurements,
    addFinding,
    updateFinding,
    addAsset,
    updateAsset,
    addOTDRMeasurement,
    deleteFinding,
    deleteAsset,
    deleteOTDRMeasurement,
    updateOTDRMeasurement,
    getCriticalFindings,
    getAssetsByCondition,
    getFailedOTDRMeasurements,
    getRouteRiskLevel,
    getAffectedRoutes,
    generateTroubleTicketFromFinding,
    updateAssetConditionFromFinding
  };

  return (
    <PatrolDataContext.Provider value={value}>
      {children}
    </PatrolDataContext.Provider>
  );
};

export type { Finding, Asset, OTDRMeasurement, PatrolDataContextType };