"use client";

import React from "react";
import { MadeWithDyad } from "@/components/made-with-dyad";

import NetworkLinkData from "@/components/NetworkLinkData";
import MaintenanceTimeline from "@/components/MaintenanceTimeline";
import NetworkRouteSLA from "@/components/NetworkRouteSLA";
import RouteSLAChart from "@/components/RouteSLAChart";
import InProgressTicketsCard from "@/components/InProgressTicketsCard"; // Import the new component
import PatrolReportCard from "@/components/PatrolReportCard"; // Import the patrol report component
import { TroubleTicket } from "@/components/TroubleTicketManagement"; // Import TroubleTicket type

interface IndexProps {
  tickets: TroubleTicket[];
}

const Index: React.FC<IndexProps> = ({ tickets }) => {
  // Filter tickets that are 'in-progress' or 'open'
  const inProgressTickets = tickets.filter(
    (ticket) => ticket.status === "in-progress" || ticket.status === "open"
  );

  // Determine grid columns based on number of tickets
  const getGridCols = (count: number) => {
    if (count === 1) return "grid-cols-1";
    if (count === 2) return "grid-cols-1 lg:grid-cols-2";
    if (count >= 3) return "grid-cols-1 lg:grid-cols-3";
    return "grid-cols-1";
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 sm:p-6 lg:p-8">
      <header className="mb-8">
        <h1 className="text-3xl sm:text-4xl font-bold text-center text-primary">
          Network Management Service - Network Dashboard
        </h1>
        <p className="text-center text-muted-foreground mt-2">
          Real-time insights into your fiber optic network performance.
        </p>
      </header>

      {/* Dynamic Layout for In-Progress Trouble Tickets */}
      {inProgressTickets.length > 0 ? (
        <div className={`grid ${getGridCols(inProgressTickets.length)} gap-6 mb-8`}>
          {inProgressTickets.map((_, index) => (
            <div key={index} className="space-y-6">
              <InProgressTicketsCard tickets={tickets} index={index} />
            </div>
          ))}
        </div>
      ) : (
        <div className="mb-8">
          <div className="text-center py-8 bg-muted/50 rounded-lg">
            <p className="text-muted-foreground">No in-progress or open trouble tickets at the moment.</p>
          </div>
        </div>
      )}

      <main className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        {/* Route SLA Chart */}
        <RouteSLAChart />

        {/* Network Route SLA Monitoring */}
        <NetworkRouteSLA />

        {/* Patrol Report Summary */}
        <div className="lg:col-span-2 xl:col-span-3">
          <PatrolReportCard />
        </div>

        {/* Maintenance Timeline */}
        <MaintenanceTimeline />

        {/* Network Link Data Section */}
        <div className="lg:col-span-2 xl:col-span-3">
          <NetworkLinkData />
        </div>
      </main>

      <MadeWithDyad />
    </div>
  );
};

export default Index;