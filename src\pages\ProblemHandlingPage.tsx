"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertTriangle, Ticket, Calendar, Plus } from "lucide-react";
import TroubleTicketManagement from "@/components/TroubleTicketManagement";
import MaintenanceTimeline from "@/components/MaintenanceTimeline";
import { MadeWithDyad } from "@/components/made-with-dyad";
import { TroubleTicket } from "@/components/TroubleTicketManagement";

interface ProblemHandlingPageProps {
  tickets: TroubleTicket[];
  setTickets: React.Dispatch<React.SetStateAction<TroubleTicket[]>>;
  openMaintenanceForm?: boolean; // Optional prop to auto-open maintenance form
}

const ProblemHandlingPage: React.FC<ProblemHandlingPageProps> = ({ 
  tickets, 
  setTickets, 
  openMaintenanceForm = false 
}) => {

  const [showMaintenanceForm, setShowMaintenanceForm] = useState(openMaintenanceForm);

  // Statistics for dashboard overview
  const openTickets = tickets.filter(ticket => ticket.status === 'open').length;
  const inProgressTickets = tickets.filter(ticket => ticket.status === 'in-progress').length;
  const resolvedTickets = tickets.filter(ticket => ticket.status === 'resolved').length;

  React.useEffect(() => {
    if (openMaintenanceForm) {
      setShowMaintenanceForm(true);
    }
  }, [openMaintenanceForm]);

  return (
    <div className="min-h-screen bg-background text-foreground p-4 sm:p-6 lg:p-8">
      <header className="mb-8">
        <h1 className="text-3xl sm:text-4xl font-bold text-center text-primary">
          Problem Handling
        </h1>
        <p className="text-center text-muted-foreground mt-2">
          Comprehensive problem management and maintenance tracking system.
        </p>
      </header>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Tickets</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{openTickets}</div>
            <p className="text-xs text-muted-foreground">
              Requires immediate attention
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Ticket className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{inProgressTickets}</div>
            <p className="text-xs text-muted-foreground">
              Currently being worked on
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <Calendar className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{resolvedTickets}</div>
            <p className="text-xs text-muted-foreground">
              Successfully completed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <main className="container mx-auto">
        {/* Show different content based on current route */}
        {window.location.pathname === '/problem-handling/trouble-tickets' ? (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Trouble Ticket Management</CardTitle>
                <Button 
                  onClick={() => {
                    // This could trigger the add ticket dialog in TroubleTicketManagement
                    // For now, we'll just show a placeholder
                  }}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  New Ticket
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <TroubleTicketManagement tickets={tickets} setTickets={setTickets} />
            </CardContent>
          </Card>
        ) : window.location.pathname === '/problem-handling/maintenance' ? (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Maintenance Timeline</CardTitle>
                <Button 
                  onClick={() => setShowMaintenanceForm(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Schedule Maintenance
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <MaintenanceTimeline />
              {showMaintenanceForm && (
                <div className="mt-6 p-4 border rounded-lg bg-muted/50">
                  <h3 className="text-lg font-semibold mb-4">Schedule New Maintenance</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Maintenance Type</label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select maintenance type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="patrol">Routine Patrol</SelectItem>
                          <SelectItem value="repair">Scheduled Repair</SelectItem>
                          <SelectItem value="upgrade">System Upgrade</SelectItem>
                          <SelectItem value="emergency">Emergency Maintenance</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Scheduled Date</label>
                      <Input 
                        type="date"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Start Time</label>
                      <Input 
                        type="time"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">End Time</label>
                      <Input 
                        type="time"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium mb-2">Description</label>
                      <Textarea 
                        rows={3}
                        placeholder="Describe the maintenance activity..."
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium mb-2">Location/Asset</label>
                      <Input 
                        type="text"
                        placeholder="e.g., Sector A, Link FR-003, OLT-01"
                      />
                    </div>
                    
                    {/* Activity List Section */}
                    <div className="md:col-span-2 mt-4">
                      <div className="flex justify-between items-center mb-2">
                        <label className="block text-sm font-medium">Activity List</label>
                        <Button 
                          type="button" 
                          variant="outline" 
                          size="sm"
                          className="flex items-center gap-1 text-xs"
                          onClick={() => {
                            // Add logic to add a new activity
                          }}
                        >
                          <Plus className="h-3 w-3" />
                          Add Activity
                        </Button>
                      </div>
                      
                      <div className="space-y-3 max-h-80 overflow-y-auto p-2 border rounded-md bg-background">
                        {/* Sample Activities - In a real app, these would be dynamic */}
                        {[1, 2, 3].map((_, index) => (
                          <div key={index} className="p-3 border rounded-md shadow-sm">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div>
                                <label className="block text-xs font-medium mb-1">Activity Name</label>
                                <Input 
                                  placeholder="e.g., Team Preparation"
                                  className="text-sm"
                                />
                              </div>
                              <div>
                                <label className="block text-xs font-medium mb-1">Person In Charge</label>
                                <Input 
                                  placeholder="e.g., John Doe"
                                  className="text-sm"
                                />
                              </div>
                              <div>
                                <label className="block text-xs font-medium mb-1">Estimated Duration (minutes)</label>
                                <Input 
                                  type="number"
                                  placeholder="e.g., 30"
                                  className="text-sm"
                                />
                              </div>
                              <div className="flex items-end">
                                <Button 
                                  type="button" 
                                  variant="destructive" 
                                  size="sm"
                                  className="text-xs"
                                  onClick={() => {
                                    // Add logic to remove this activity
                                  }}
                                >
                                  Remove
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end gap-2 mt-4">
                    <Button 
                      variant="outline" 
                      onClick={() => setShowMaintenanceForm(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={() => {
                      // Handle form submission here
                      setShowMaintenanceForm(false);
                      // Add logic to save maintenance schedule
                    }}>
                      Schedule Maintenance
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          /* Default Problem Handling Overview */
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => window.location.href = '/problem-handling/trouble-tickets'}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Ticket className="h-5 w-5 text-blue-500" />
                  Trouble Tickets
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">Manage and track all network trouble tickets</p>
                <div className="text-2xl font-bold text-blue-600">{tickets.length} Total Tickets</div>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => window.location.href = '/problem-handling/maintenance'}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-green-500" />
                  Maintenance Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">Schedule and track maintenance activities</p>
                <div className="text-2xl font-bold text-green-600">6 Scheduled</div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>

      <MadeWithDyad />
    </div>
  );
};

export default ProblemHandlingPage;