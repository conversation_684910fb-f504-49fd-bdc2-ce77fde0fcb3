import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { Badge } from "./ui/badge";
import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { NetworkRoute, Asset, AssetDetail } from "../data/networkRoutes";
import { Edit, Package, Cable, Zap, Building } from "lucide-react";
import AssetDetailDialog from "./AssetDetailDialog";
import AssetListDialog from "./AssetListDialog";

interface RouteAssetCardProps {
  route: NetworkRoute;
  onUpdateAssets: (routeId: string, assets: Asset[]) => void;
  onAssetUpdate?: (updatedAsset: AssetDetail) => void;
}

const getAssetIcon = (assetType: string) => {
  switch (assetType.toLowerCase()) {
    case "fiber optic cable":
      return <Cable className="h-4 w-4" />;
    case "odf":
      return <Zap className="h-4 w-4" />;
    case "handhole":
    case "pole":
    case "jc":
      return <Building className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "operational":
      return "bg-green-500";
    case "degraded":
      return "bg-yellow-500";
    case "down":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

export default function RouteAssetCard({ route, onUpdateAssets, onAssetUpdate }: RouteAssetCardProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingAssets, setEditingAssets] = useState<Asset[]>(route.assets);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [isAssetListOpen, setIsAssetListOpen] = useState(false);

  const handleSaveAssets = () => {
    onUpdateAssets(route.id, editingAssets);
    setIsEditDialogOpen(false);
  };

  const handleAssetCountChange = (assetId: string, newCount: number) => {
    setEditingAssets(prev => 
      prev.map(asset => 
        asset.id === assetId ? { ...asset, count: Math.max(0, newCount) } : asset
      )
    );
  };

  const totalAssets = route.assets.reduce((sum, asset) => {
    return sum + (asset.details ? asset.details.length : asset.count);
  }, 0);

  const handleAssetClick = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsAssetListOpen(true);
  };

  const handleAssetDetailUpdate = (updatedAsset: AssetDetail) => {
    // Propagate the update to parent component (AssetManagementPage)
    if (onAssetUpdate) {
      onAssetUpdate(updatedAsset);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="text-lg font-semibold">{route.name}</CardTitle>
          </div>
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-1" />
                Edit Assets
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Edit Assets - {route.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {editingAssets.map((asset) => (
                  <div key={asset.id} className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2 flex-1">
                      {getAssetIcon(asset.type)}
                      <Label className="text-sm font-medium">{asset.type}</Label>
                    </div>
                    <Input
                      type="number"
                      min="0"
                      value={asset.count}
                      onChange={(e) => handleAssetCountChange(asset.id, parseInt(e.target.value) || 0)}
                      className="w-20"
                    />
                  </div>
                ))}
                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSaveAssets}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>ID: {route.id}</span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-semibold text-gray-700">Asset Summary</h4>
            <Badge variant="outline" className="text-xs">
              Total: {totalAssets} items
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            {route.assets.map((asset) => (
              <div 
                key={asset.id} 
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                onClick={() => handleAssetClick(asset)}
              >
                <div className="flex items-center space-x-2">
                  {getAssetIcon(asset.type)}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{asset.type}</p>
                    <p className="text-xs text-gray-500">Quantity</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-blue-600">{asset.details ? asset.details.length : asset.count}</p>
                  <p className="text-xs text-gray-500">units</p>
                </div>
              </div>
            ))}
          </div>

          <div className="pt-2 border-t border-gray-200">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Last Checked:</span>
              <span className="font-medium">{route.lastChecked}</span>
            </div>
          </div>
        </div>
      </CardContent>
      
      <AssetListDialog 
        isOpen={isAssetListOpen}
        onClose={() => setIsAssetListOpen(false)}
        asset={selectedAsset}
        onAssetUpdate={handleAssetDetailUpdate}
      />
    </Card>
  );
}