"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Edit, Router, Link as LinkIcon } from "lucide-react";
import { showSuccess } from "@/utils/toast";

const RouteACard: React.FC = () => {
  const handleEdit = () => {
    showSuccess("Edit Route A clicked! (Form editing not implemented yet)");
    // Here you can add logic to open dialog or edit page
  };

  return (
    <Card className="col-span-1 md:col-span-2 lg:col-span-3">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-xl">Route A</CardTitle>
        <Button variant="outline" size="sm" onClick={handleEdit}>
          <Edit className="mr-2 h-4 w-4" /> Edit
        </Button>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Sub-Card 1 */}
        <Card className="p-4 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-medium">Router Status</CardTitle>
            <Router className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">Online</div>
            <p className="text-xs text-muted-foreground">Last updated: 1 min ago</p>
          </CardContent>
        </Card>

        {/* Sub-Card 2 */}
        <Card className="p-4 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-medium">Link Health</CardTitle>
            <LinkIcon className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">Excellent</div>
            <p className="text-xs text-muted-foreground">No packet loss</p>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  );
};

export default RouteACard;