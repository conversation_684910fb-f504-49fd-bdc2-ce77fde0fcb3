import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "./ui/dialog";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { AssetDetail } from "../data/networkRoutes";
import { Package, MapPin, Calendar, Edit, Eye, X, Printer, Camera } from "lucide-react";
import PhotoGallery from './PhotoGallery';
import EditAssetDialog from './EditAssetDialog';

interface AssetDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  assetDetail: AssetDetail | null;
  onAssetUpdate?: (updatedAsset: AssetDetail) => void;
}

const getConditionColor = (condition: string) => {
  switch (condition) {
    case "excellent":
      return "bg-green-500";
    case "good":
      return "bg-blue-500";
    case "fair":
      return "bg-yellow-500";
    case "poor":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

const getConditionBadgeVariant = (condition: string) => {
  switch (condition) {
    case "excellent":
      return "default";
    case "good":
      return "secondary";
    case "fair":
      return "outline";
    case "poor":
      return "destructive";
    default:
      return "outline";
  }
};

export default function AssetDetailDialog({ isOpen, onClose, assetDetail, onAssetUpdate }: AssetDetailDialogProps) {
  const [isPhotoGalleryOpen, setIsPhotoGalleryOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentAssetDetail, setCurrentAssetDetail] = useState<AssetDetail | null>(assetDetail);

  const handlePrintAssetDetail = () => {
    if (!currentAssetDetail) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      handlePrintAssetDetailFallback();
      return;
    }

    const photosHtml = currentAssetDetail.photos && currentAssetDetail.photos.length > 0 
      ? `
        <div style="margin-top: 8px; page-break-inside: avoid;">
          <h3 style="color: #1f2937; margin-bottom: 4px; font-size: 10px; font-weight: 600;">Asset Photos</h3>
          <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 2px; max-width: 100%;">
            ${currentAssetDetail.photos.slice(0, 5).map((photo, index) => `
              <div style="position: relative; border: 1px solid #e5e7eb; border-radius: 2px; overflow: hidden; aspect-ratio: 1; width: 100%; max-width: 50px;">
                <img src="${photo}" alt="Photo ${index + 1}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'" />
                <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 1px; background: rgba(0,0,0,0.7); color: white; text-align: center; font-size: 6px;">${index + 1}</div>
              </div>
            `).join('')}
          </div>
          ${currentAssetDetail.photos.length > 5 ? `<div style="font-size: 7px; color: #6b7280; margin-top: 2px;">+${currentAssetDetail.photos.length - 5} more photos</div>` : ''}
        </div>
      `
      : '<div style="margin-top: 8px; padding: 8px; background: #f9fafb; border-radius: 4px; text-align: center; color: #6b7280; font-size: 8px;">No photos available</div>';

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Asset Detail Report - ${currentAssetDetail.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 10px; color: #333; }
            .header { text-align: center; margin-bottom: 15px; border-bottom: 1px solid #3b82f6; padding-bottom: 8px; }
            .title { font-size: 14px; font-weight: bold; color: #1f2937; margin-bottom: 2px; }
            .subtitle { font-size: 8px; color: #6b7280; }
            .section { margin-bottom: 12px; }
            .section-title { font-size: 10px; font-weight: 600; color: #1f2937; margin-bottom: 6px; border-bottom: 1px solid #e5e7eb; padding-bottom: 2px; }
            .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 6px; }
            .info-item { background: #f8fafc; padding: 4px; border-radius: 3px; border-left: 2px solid #3b82f6; }
            .info-label { font-weight: 600; color: #374151; font-size: 7px; text-transform: uppercase; letter-spacing: 0.3px; }
            .info-value { color: #1f2937; font-size: 8px; margin-top: 1px; }
            .badge { display: inline-block; padding: 2px 4px; border-radius: 2px; font-size: 7px; font-weight: 500; }
            .badge-excellent { background: #dcfce7; color: #166534; }
            .badge-good { background: #dbeafe; color: #1d4ed8; }
            .badge-fair { background: #fef3c7; color: #92400e; }
            .badge-poor { background: #fee2e2; color: #dc2626; }
            .footer { margin-top: 15px; text-align: center; font-size: 7px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 8px; }
            @media print {
              body { margin: 0; font-size: 7px; line-height: 1.1; }
              .header { page-break-after: avoid; margin-bottom: 8px; padding-bottom: 4px; }
              .title { font-size: 12px; margin-bottom: 1px; }
              .subtitle { font-size: 7px; }
              .section { page-break-inside: avoid; margin-bottom: 6px; }
              .section-title { font-size: 8px; margin-bottom: 3px; }
              .info-grid { grid-template-columns: repeat(4, 1fr); gap: 3px; }
              .info-item { padding: 3px; }
              .info-label { font-size: 6px; }
              .info-value { font-size: 7px; margin-top: 1px; }
              .footer { margin-top: 8px; padding-top: 4px; font-size: 6px; }
              .badge { padding: 1px 2px; font-size: 6px; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">Asset Detail Report</div>
            <div class="subtitle">Generated on ${new Date().toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
          </div>

          <div class="section">
            <div class="section-title">Basic Information</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Asset ID</div>
                <div class="info-value">${currentAssetDetail.id}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Asset Name</div>
                <div class="info-value">${currentAssetDetail.name}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Category</div>
                <div class="info-value">${currentAssetDetail.category}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Brand</div>
                <div class="info-value">${currentAssetDetail.brand || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Model</div>
                <div class="info-value">${currentAssetDetail.model || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Serial Number</div>
                <div class="info-value">${currentAssetDetail.serialNumber || 'N/A'}</div>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Status & Condition</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Condition</div>
                <div class="info-value">
                  <span class="badge badge-${currentAssetDetail.condition}">${currentAssetDetail.condition?.charAt(0).toUpperCase() + currentAssetDetail.condition?.slice(1) || 'N/A'}</span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">Area/Zone</div>
                <div class="info-value">${currentAssetDetail.area || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Installation Date</div>
                <div class="info-value">${currentAssetDetail.installationDate ? new Date(currentAssetDetail.installationDate).toLocaleDateString() : 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Last Maintenance</div>
                <div class="info-value">${currentAssetDetail.lastMaintenance ? new Date(currentAssetDetail.lastMaintenance).toLocaleDateString() : 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Warranty Until</div>
                <div class="info-value">${currentAssetDetail.warranty ? new Date(currentAssetDetail.warranty).toLocaleDateString() : 'N/A'}</div>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Location Information</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Address</div>
                <div class="info-value">${currentAssetDetail.location?.address || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Latitude</div>
                <div class="info-value">${currentAssetDetail.location?.latitude || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Longitude</div>
                <div class="info-value">${currentAssetDetail.location?.longitude || 'N/A'}</div>
              </div>
            </div>
          </div>

          ${currentAssetDetail.notes ? `
            <div class="section">
              <div class="section-title">Additional Notes</div>
              <div style="background: #f8fafc; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6;">
                ${currentAssetDetail.notes}
              </div>
            </div>
          ` : ''}

          ${photosHtml}

          <div class="footer">
            <div>Asset Management System - Detailed Report</div>
            <div>Report generated for: ${currentAssetDetail.name} (ID: ${currentAssetDetail.id})</div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const handlePrintAssetDetailFallback = () => {
    if (!currentAssetDetail) return;

    const photosText = currentAssetDetail.photos && currentAssetDetail.photos.length > 0 
      ? `\n\nAsset Photos: ${currentAssetDetail.photos.length} photo(s) available`
      : '\n\nAsset Photos: No photos available';

    const reportContent = `
ASSET DETAIL REPORT
${'='.repeat(50)}

Generated on: ${new Date().toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}

BASIC INFORMATION
${'-'.repeat(30)}
Asset ID: ${currentAssetDetail.id}
Asset Name: ${currentAssetDetail.name}
Category: ${currentAssetDetail.category}
Brand: ${currentAssetDetail.brand || 'N/A'}
Model: ${currentAssetDetail.model || 'N/A'}
Serial Number: ${currentAssetDetail.serialNumber || 'N/A'}

STATUS & CONDITION
${'-'.repeat(30)}
Condition: ${currentAssetDetail.condition?.charAt(0).toUpperCase() + currentAssetDetail.condition?.slice(1) || 'N/A'}
Area/Zone: ${currentAssetDetail.area || 'N/A'}
Installation Date: ${currentAssetDetail.installationDate ? new Date(currentAssetDetail.installationDate).toLocaleDateString() : 'N/A'}
Last Maintenance: ${currentAssetDetail.lastMaintenance ? new Date(currentAssetDetail.lastMaintenance).toLocaleDateString() : 'N/A'}
Warranty Until: ${currentAssetDetail.warranty ? new Date(currentAssetDetail.warranty).toLocaleDateString() : 'N/A'}

LOCATION INFORMATION
${'-'.repeat(30)}
Address: ${currentAssetDetail.location?.address || 'N/A'}
Latitude: ${currentAssetDetail.location?.latitude || 'N/A'}
Longitude: ${currentAssetDetail.location?.longitude || 'N/A'}
${currentAssetDetail.notes ? `\nADDITIONAL NOTES\n${'-'.repeat(30)}\n${currentAssetDetail.notes}` : ''}${photosText}

${'='.repeat(50)}
Asset Management System - Detailed Report
Report generated for: ${currentAssetDetail.name} (ID: ${currentAssetDetail.id})
    `;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `asset-detail-${currentAssetDetail.id}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  
  // Update local state when assetDetail prop changes
  useEffect(() => {
    setCurrentAssetDetail(assetDetail);
  }, [assetDetail]);

  if (!currentAssetDetail) return null;
  
  const allPhotos = currentAssetDetail.photos || (currentAssetDetail.photo ? [currentAssetDetail.photo] : []);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>{currentAssetDetail.name}</span>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditDialogOpen(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Asset
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrintAssetDetail}
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Detail
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Asset Photo */}
          {allPhotos.length > 0 && (
            <div className="w-full">
              <div className="relative">
                <img 
                  src={allPhotos[0]} 
                  alt={currentAssetDetail.name}
                  className="w-full h-48 object-cover rounded-lg border cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => setIsPhotoGalleryOpen(true)}
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
                {allPhotos.length > 1 && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="bg-white/80">
                      {allPhotos.length} foto
                    </Badge>
                  </div>
                )}
                {allPhotos.length > 1 && (
                  <Button
                    variant="secondary"
                    size="sm"
                    className="absolute bottom-2 right-2 bg-white/80 hover:bg-white"
                    onClick={() => setIsPhotoGalleryOpen(true)}
                  >
                    <Camera className="h-4 w-4 mr-1" />
                    Lihat Semua
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-700">Basic Information</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ID:</span>
                  <span className="font-medium">{currentAssetDetail.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jenis:</span>
                  <span className="font-medium">{currentAssetDetail.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Merk:</span>
                  <span className="font-medium">{currentAssetDetail.brand}</span>
                </div>
                {currentAssetDetail.model && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Model:</span>
                    <span className="font-medium">{currentAssetDetail.model}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-700">Status & Condition</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Kondisi:</span>
                  <Badge variant={getConditionBadgeVariant(currentAssetDetail.condition)}>
                    {currentAssetDetail.condition.charAt(0).toUpperCase() + currentAssetDetail.condition.slice(1)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Area:</span>
                  <span className="font-medium">{currentAssetDetail.area}</span>
                </div>
                {currentAssetDetail.installationDate && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tgl Install:</span>
                    <span className="font-medium">{new Date(currentAssetDetail.installationDate).toLocaleDateString('id-ID')}</span>
                  </div>
                )}
                {currentAssetDetail.lastMaintenance && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Maintenance:</span>
                    <span className="font-medium">{new Date(currentAssetDetail.lastMaintenance).toLocaleDateString('id-ID')}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm text-gray-700 flex items-center space-x-1">
              <MapPin className="h-4 w-4" />
              <span>Lokasi</span>
            </h3>
            <div className="bg-gray-50 p-3 rounded-lg space-y-2">
              <div className="text-sm">
                <span className="text-gray-600">Alamat:</span>
                <p className="font-medium mt-1">{currentAssetDetail.location.address}</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Latitude:</span>
                  <p className="font-medium font-mono">{currentAssetDetail.location.latitude}</p>
                </div>
                <div>
                  <span className="text-gray-600">Longitude:</span>
                  <p className="font-medium font-mono">{currentAssetDetail.location.longitude}</p>
                </div>
              </div>
              <div className="flex justify-end pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="p-2 border-blue-500 hover:bg-blue-50"
                  onClick={() => {
                    const googleMapsUrl = `https://www.google.com/maps?q=${currentAssetDetail.location.latitude},${currentAssetDetail.location.longitude}`;
                    window.open(googleMapsUrl, '_blank');
                  }}
                  title="Buka di Google Maps"
                >
                  <MapPin className="h-4 w-4 text-blue-600" />
                </Button>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm text-gray-700 flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>Timeline</span>
            </h3>
            <div className="space-y-2">
              {currentAssetDetail.installationDate && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Instalasi:</span>
                    <span className="ml-2 text-gray-600">{new Date(currentAssetDetail.installationDate).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              )}
              {currentAssetDetail.lastMaintenance && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Last Maintenance:</span>
                    <span className="ml-2 text-gray-600">{new Date(currentAssetDetail.lastMaintenance).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              )}
              {currentAssetDetail.lastChecked && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Last Inspection:</span>
                    <span className="ml-2 text-gray-600">{currentAssetDetail.lastChecked}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
      
      <PhotoGallery
         isOpen={isPhotoGalleryOpen}
         onClose={() => setIsPhotoGalleryOpen(false)}
         photos={allPhotos}
       />
       
      <EditAssetDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        assetDetail={currentAssetDetail}
        onSave={(updatedAsset) => {
          // Update local state first
          setCurrentAssetDetail(updatedAsset);
          // Then notify parent component
          if (onAssetUpdate) {
            onAssetUpdate(updatedAsset);
          }
          setIsEditDialogOpen(false);
        }}
      />
    </Dialog>
  );
}