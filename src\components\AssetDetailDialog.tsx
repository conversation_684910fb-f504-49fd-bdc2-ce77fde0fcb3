import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "./ui/dialog";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { AssetDetail } from "../data/networkRoutes";
import { MapPin, Calendar, Wrench, Package, Camera, CheckCircle, Edit } from "lucide-react";
import PhotoGallery from './PhotoGallery';
import EditAssetDialog from './EditAssetDialog';

interface AssetDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  assetDetail: AssetDetail | null;
  onAssetUpdate?: (updatedAsset: AssetDetail) => void;
}

const getConditionColor = (condition: string) => {
  switch (condition) {
    case "excellent":
      return "bg-green-500";
    case "good":
      return "bg-blue-500";
    case "fair":
      return "bg-yellow-500";
    case "poor":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

const getConditionBadgeVariant = (condition: string) => {
  switch (condition) {
    case "excellent":
      return "default";
    case "good":
      return "secondary";
    case "fair":
      return "outline";
    case "poor":
      return "destructive";
    default:
      return "outline";
  }
};

export default function AssetDetailDialog({ isOpen, onClose, assetDetail, onAssetUpdate }: AssetDetailDialogProps) {
  const [isPhotoGalleryOpen, setIsPhotoGalleryOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  
  if (!assetDetail) return null;
  
  const allPhotos = assetDetail.photos || [assetDetail.photo].filter(Boolean);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>{assetDetail.name}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditDialogOpen(true)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Asset
            </Button>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Asset Photo */}
          {allPhotos.length > 0 && (
            <div className="w-full">
              <div className="relative">
                <img 
                  src={allPhotos[0]} 
                  alt={assetDetail.name}
                  className="w-full h-48 object-cover rounded-lg border cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => setIsPhotoGalleryOpen(true)}
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
                {allPhotos.length > 1 && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="bg-white/80">
                      {allPhotos.length} foto
                    </Badge>
                  </div>
                )}
                {allPhotos.length > 1 && (
                  <Button
                    variant="secondary"
                    size="sm"
                    className="absolute bottom-2 right-2 bg-white/80 hover:bg-white"
                    onClick={() => setIsPhotoGalleryOpen(true)}
                  >
                    <Camera className="h-4 w-4 mr-1" />
                    Lihat Semua
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-700">Basic Information</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ID:</span>
                  <span className="font-medium">{assetDetail.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jenis:</span>
                  <span className="font-medium">{assetDetail.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Merk:</span>
                  <span className="font-medium">{assetDetail.brand}</span>
                </div>
                {assetDetail.model && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Model:</span>
                    <span className="font-medium">{assetDetail.model}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-700">Status & Condition</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Kondisi:</span>
                  <Badge variant={getConditionBadgeVariant(assetDetail.condition)}>
                    {assetDetail.condition.charAt(0).toUpperCase() + assetDetail.condition.slice(1)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Area:</span>
                  <span className="font-medium">{assetDetail.area}</span>
                </div>
                {assetDetail.installationDate && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tgl Install:</span>
                    <span className="font-medium">{new Date(assetDetail.installationDate).toLocaleDateString('id-ID')}</span>
                  </div>
                )}
                {assetDetail.lastMaintenance && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Maintenance:</span>
                    <span className="font-medium">{new Date(assetDetail.lastMaintenance).toLocaleDateString('id-ID')}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm text-gray-700 flex items-center space-x-1">
              <MapPin className="h-4 w-4" />
              <span>Lokasi</span>
            </h3>
            <div className="bg-gray-50 p-3 rounded-lg space-y-2">
              <div className="text-sm">
                <span className="text-gray-600">Alamat:</span>
                <p className="font-medium mt-1">{assetDetail.location.address}</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Latitude:</span>
                  <p className="font-medium font-mono">{assetDetail.location.latitude}</p>
                </div>
                <div>
                  <span className="text-gray-600">Longitude:</span>
                  <p className="font-medium font-mono">{assetDetail.location.longitude}</p>
                </div>
              </div>
              <div className="flex justify-end pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="p-2 border-blue-500 hover:bg-blue-50"
                  onClick={() => {
                    console.log('Button clicked');
                    console.log('Coordinates:', assetDetail.location.latitude, assetDetail.location.longitude);
                    const googleMapsUrl = `https://www.google.com/maps?q=${assetDetail.location.latitude},${assetDetail.location.longitude}`;
                    console.log('Google Maps URL:', googleMapsUrl);
                    window.open(googleMapsUrl, '_blank');
                  }}
                  title="Buka di Google Maps"
                >
                  <MapPin className="h-4 w-4 text-blue-600" />
                </Button>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm text-gray-700 flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>Timeline</span>
            </h3>
            <div className="space-y-2">
              {assetDetail.installationDate && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Instalasi:</span>
                    <span className="ml-2 text-gray-600">{new Date(assetDetail.installationDate).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              )}
              {assetDetail.lastMaintenance && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Last Maintenance:</span>
                    <span className="ml-2 text-gray-600">{new Date(assetDetail.lastMaintenance).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              )}
              {assetDetail.lastChecked && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Last Inspection:</span>
                    <span className="ml-2 text-gray-600">{assetDetail.lastChecked}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
      
      <PhotoGallery
         isOpen={isPhotoGalleryOpen}
         onClose={() => setIsPhotoGalleryOpen(false)}
         photos={allPhotos}
       />
       
      <EditAssetDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        assetDetail={assetDetail}
        onSave={(updatedAsset) => {
          if (onAssetUpdate) {
            onAssetUpdate(updatedAsset);
          }
          setIsEditDialogOpen(false);
        }}
      />
    </Dialog>
  );
}