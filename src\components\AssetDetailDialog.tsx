import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "./ui/dialog";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { AssetDetail } from "../data/networkRoutes";
import { MapPin, Calendar, Package, Camera, Edit, Printer } from "lucide-react";
import { formatDateTime, TEXT_CONSTANTS } from "../config/locale";
import PhotoGallery from './PhotoGallery';
import EditAssetDialog from './EditAssetDialog';

interface AssetDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  assetDetail: AssetDetail | null;
  onAssetUpdate?: (updatedAsset: AssetDetail) => void;
}

const getConditionColor = (condition: string) => {
  switch (condition) {
    case "excellent":
      return "bg-green-500";
    case "good":
      return "bg-blue-500";
    case "fair":
      return "bg-yellow-500";
    case "poor":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

const getConditionBadgeVariant = (condition: string) => {
  switch (condition) {
    case "excellent":
      return "default";
    case "good":
      return "secondary";
    case "fair":
      return "outline";
    case "poor":
      return "destructive";
    default:
      return "outline";
  }
};

export default function AssetDetailDialog({ isOpen, onClose, assetDetail, onAssetUpdate }: AssetDetailDialogProps) {
  const [isPhotoGalleryOpen, setIsPhotoGalleryOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const handlePrintAssetDetail = () => {
    if (!assetDetail) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      handlePrintAssetDetailFallback();
      return;
    }

    const photosHtml = assetDetail.photos && assetDetail.photos.length > 0
      ? `
        <div style="margin-top: 12px; page-break-inside: avoid;">
          <h3 style="color: #1f2937; margin-bottom: 8px; font-size: 12px; font-weight: 600;">Asset Photos</h3>
          <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; max-width: 100%;">
            ${assetDetail.photos.slice(0, 6).map((photo, index) => `
              <div style="position: relative; border: 2px solid #e5e7eb; border-radius: 6px; overflow: hidden; aspect-ratio: 4/3; width: 100%; max-width: 120px;">
                <img src="${photo}" alt="Photo ${index + 1}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'" />
                <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 4px; background: rgba(0,0,0,0.8); color: white; text-align: center; font-size: 10px; font-weight: 600;">Photo ${index + 1}</div>
              </div>
            `).join('')}
          </div>
          ${assetDetail.photos.length > 6 ? `<div style="font-size: 10px; color: #6b7280; margin-top: 6px; text-align: center; font-style: italic;">+${assetDetail.photos.length - 6} more photos available</div>` : ''}
        </div>
      `
      : '<div style="margin-top: 12px; padding: 12px; background: #f9fafb; border-radius: 6px; text-align: center; color: #6b7280; font-size: 10px;">No photos available</div>';

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Asset Detail Report - ${assetDetail.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 10px; color: #333; }
            .header { text-align: center; margin-bottom: 15px; border-bottom: 1px solid #3b82f6; padding-bottom: 8px; }
            .title { font-size: 14px; font-weight: bold; color: #1f2937; margin-bottom: 2px; }
            .subtitle { font-size: 8px; color: #6b7280; }
            .section { margin-bottom: 12px; page-break-inside: avoid; }
            .section-title { font-size: 10px; font-weight: bold; color: #1f2937; margin-bottom: 6px; border-bottom: 1px solid #e5e7eb; padding-bottom: 2px; }
            .info-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; }
            .info-item { margin-bottom: 4px; }
            .info-label { font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px; }
            .info-value { font-size: 8px; color: #1f2937; }
            .badge { display: inline-block; padding: 2px 6px; border-radius: 4px; font-size: 7px; font-weight: 600; }
            .badge-excellent { background: #dcfce7; color: #166534; }
            .badge-good { background: #dbeafe; color: #1e40af; }
            .badge-fair { background: #fef3c7; color: #92400e; }
            .badge-poor { background: #fee2e2; color: #dc2626; }
            .footer { margin-top: 20px; text-align: center; font-size: 7px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 8px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">ASSET DETAIL REPORT</div>
            <div class="subtitle">${TEXT_CONSTANTS.REPORTS.GENERATED_ON} ${formatDateTime(new Date())}</div>
          </div>

          <div class="section">
            <div class="section-title">Basic Information</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Asset ID</div>
                <div class="info-value">${assetDetail.id}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Asset Name</div>
                <div class="info-value">${assetDetail.name}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Category</div>
                <div class="info-value">${assetDetail.category}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Brand</div>
                <div class="info-value">${assetDetail.brand || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Model</div>
                <div class="info-value">${assetDetail.model || 'N/A'}</div>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Status & Condition</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Condition</div>
                <div class="info-value">
                  <span class="badge badge-${assetDetail.condition}">${assetDetail.condition?.charAt(0).toUpperCase() + assetDetail.condition?.slice(1) || 'N/A'}</span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">Area/Zone</div>
                <div class="info-value">${assetDetail.area || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Installation Date</div>
                <div class="info-value">${assetDetail.installationDate ? new Date(assetDetail.installationDate).toLocaleDateString() : 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Last Maintenance</div>
                <div class="info-value">${assetDetail.lastMaintenance ? new Date(assetDetail.lastMaintenance).toLocaleDateString() : 'N/A'}</div>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Location Information</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Address</div>
                <div class="info-value">${assetDetail.location?.address || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Latitude</div>
                <div class="info-value">${assetDetail.location?.latitude || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Longitude</div>
                <div class="info-value">${assetDetail.location?.longitude || 'N/A'}</div>
              </div>
            </div>
          </div>

          ${photosHtml}

          <div class="footer">
            <div>Asset Management System - Detailed Report</div>
            <div>Report generated for: ${assetDetail.name} (ID: ${assetDetail.id})</div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const handlePrintAssetDetailFallback = () => {
    if (!assetDetail) return;

    const photosText = assetDetail.photos && assetDetail.photos.length > 0
      ? `\n\nAsset Photos: ${assetDetail.photos.length} photo(s) available`
      : '\n\nAsset Photos: No photos available';

    const reportContent = `
ASSET DETAIL REPORT
${'='.repeat(50)}

Generated on: ${new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}

BASIC INFORMATION
${'-'.repeat(30)}
Asset ID: ${assetDetail.id}
Asset Name: ${assetDetail.name}
Category: ${assetDetail.category}
Brand: ${assetDetail.brand || 'N/A'}
Model: ${assetDetail.model || 'N/A'}

STATUS & CONDITION
${'-'.repeat(30)}
Condition: ${assetDetail.condition?.charAt(0).toUpperCase() + assetDetail.condition?.slice(1) || 'N/A'}
Area/Zone: ${assetDetail.area || 'N/A'}
Installation Date: ${assetDetail.installationDate ? new Date(assetDetail.installationDate).toLocaleDateString() : 'N/A'}
Last Maintenance: ${assetDetail.lastMaintenance ? new Date(assetDetail.lastMaintenance).toLocaleDateString() : 'N/A'}

LOCATION INFORMATION
${'-'.repeat(30)}
Address: ${assetDetail.location?.address || 'N/A'}
Latitude: ${assetDetail.location?.latitude || 'N/A'}
Longitude: ${assetDetail.location?.longitude || 'N/A'}${photosText}

${'='.repeat(50)}
Asset Management System - Detailed Report
Report generated for: ${assetDetail.name} (ID: ${assetDetail.id})
    `;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `asset-detail-${assetDetail.id}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (!assetDetail) return null;
  
  const allPhotos = assetDetail.photos || (assetDetail.photo ? [assetDetail.photo] : []);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>{assetDetail.name}</span>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrintAssetDetail}
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditDialogOpen(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Asset
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Asset Photo */}
          {allPhotos.length > 0 && (
            <div className="w-full">
              <div className="relative">
                <img 
                  src={allPhotos[0]} 
                  alt={assetDetail.name}
                  className="w-full h-48 object-cover rounded-lg border cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => setIsPhotoGalleryOpen(true)}
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
                {allPhotos.length > 1 && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="bg-white/80">
                      {allPhotos.length} foto
                    </Badge>
                  </div>
                )}
                {allPhotos.length > 1 && (
                  <Button
                    variant="secondary"
                    size="sm"
                    className="absolute bottom-2 right-2 bg-white/80 hover:bg-white"
                    onClick={() => setIsPhotoGalleryOpen(true)}
                  >
                    <Camera className="h-4 w-4 mr-1" />
                    {TEXT_CONSTANTS.PHOTOS.VIEW_ALL}
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-700">Basic Information</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ID:</span>
                  <span className="font-medium">{assetDetail.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jenis:</span>
                  <span className="font-medium">{assetDetail.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Merk:</span>
                  <span className="font-medium">{assetDetail.brand}</span>
                </div>
                {assetDetail.model && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Model:</span>
                    <span className="font-medium">{assetDetail.model}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-700">Status & Condition</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Kondisi:</span>
                  <Badge variant={getConditionBadgeVariant(assetDetail.condition)}>
                    {assetDetail.condition.charAt(0).toUpperCase() + assetDetail.condition.slice(1)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Area:</span>
                  <span className="font-medium">{assetDetail.area}</span>
                </div>
                {assetDetail.installationDate && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tgl Install:</span>
                    <span className="font-medium">{new Date(assetDetail.installationDate).toLocaleDateString('id-ID')}</span>
                  </div>
                )}
                {assetDetail.lastMaintenance && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Maintenance:</span>
                    <span className="font-medium">{new Date(assetDetail.lastMaintenance).toLocaleDateString('id-ID')}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm text-gray-700 flex items-center space-x-1">
              <MapPin className="h-4 w-4" />
              <span>Lokasi</span>
            </h3>
            <div className="bg-gray-50 p-3 rounded-lg space-y-2">
              <div className="text-sm">
                <span className="text-gray-600">Alamat:</span>
                <p className="font-medium mt-1">{assetDetail.location.address}</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Latitude:</span>
                  <p className="font-medium font-mono">{assetDetail.location.latitude}</p>
                </div>
                <div>
                  <span className="text-gray-600">Longitude:</span>
                  <p className="font-medium font-mono">{assetDetail.location.longitude}</p>
                </div>
              </div>
              <div className="flex justify-end pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="p-2 border-blue-500 hover:bg-blue-50"
                  onClick={() => {
                    console.log('Button clicked');
                    console.log('Coordinates:', assetDetail.location.latitude, assetDetail.location.longitude);
                    const googleMapsUrl = `https://www.google.com/maps?q=${assetDetail.location.latitude},${assetDetail.location.longitude}`;
                    console.log('Google Maps URL:', googleMapsUrl);
                    window.open(googleMapsUrl, '_blank');
                  }}
                  title="Buka di Google Maps"
                >
                  <MapPin className="h-4 w-4 text-blue-600" />
                </Button>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm text-gray-700 flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>Timeline</span>
            </h3>
            <div className="space-y-2">
              {assetDetail.installationDate && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Instalasi:</span>
                    <span className="ml-2 text-gray-600">{new Date(assetDetail.installationDate).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              )}
              {assetDetail.lastMaintenance && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Last Maintenance:</span>
                    <span className="ml-2 text-gray-600">{new Date(assetDetail.lastMaintenance).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              )}
              {assetDetail.lastChecked && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div>
                    <span className="font-medium">Last Inspection:</span>
                    <span className="ml-2 text-gray-600">{assetDetail.lastChecked}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
      
      <PhotoGallery
         isOpen={isPhotoGalleryOpen}
         onClose={() => setIsPhotoGalleryOpen(false)}
         photos={allPhotos}
       />
       
      <EditAssetDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        assetDetail={assetDetail}
        onSave={(updatedAsset) => {
          if (onAssetUpdate) {
            onAssetUpdate(updatedAsset);
          }
          setIsEditDialogOpen(false);
        }}
      />
    </Dialog>
  );
}