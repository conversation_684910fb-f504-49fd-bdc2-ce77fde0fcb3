import React, { useState } from 'react';
import { supabase } from '../lib/supabase';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { Loader2, User, Database, CheckCircle, XCircle } from 'lucide-react';

interface SetupStatus {
  connection: boolean;
  database: boolean;
  user: boolean;
}

export const QuickUserSetup: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Admin123!');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState<SetupStatus>({
    connection: false,
    database: false,
    user: false
  });

  const checkSetupStatus = async () => {
    setLoading(true);
    setMessage('Checking setup status...');
    
    try {
      // Check connection
      const { error: connectionError } = await supabase.auth.getSession();
      const connectionOk = !connectionError;
      
      // Check database
      const { error: dbError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
      const databaseOk = !dbError;
      
      // Check if we can create users (this will fail but that's ok)
      const userOk = connectionOk && databaseOk;
      
      setStatus({
        connection: connectionOk,
        database: databaseOk,
        user: userOk
      });
      
      if (!connectionOk) {
        setMessage('❌ Supabase connection failed. Check your environment variables.');
      } else if (!databaseOk) {
        setMessage('❌ Database tables not found. Please run the schema setup first.');
      } else {
        setMessage('✅ Setup looks good! You can create a user now.');
      }
      
    } catch (error) {
      setMessage(`❌ Error checking status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const createUser = async () => {
    if (!email || !password) {
      setMessage('❌ Please enter email and password');
      return;
    }
    
    setLoading(true);
    setMessage('Creating user...');
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: 'System Administrator',
            username: 'admin'
          }
        }
      });
      
      if (error) {
        setMessage(`❌ Error creating user: ${error.message}`);
        return;
      }
      
      if (data.user) {
        setMessage(`✅ User created successfully! Email: ${data.user.email}`);
        setStatus(prev => ({ ...prev, user: true }));
        
        // Auto sign out after creation
        setTimeout(async () => {
          await supabase.auth.signOut();
          setMessage(prev => prev + '\n\n🔓 Signed out. You can now login with these credentials.');
        }, 1000);
      }
      
    } catch (error) {
      setMessage(`❌ Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const createDemoUsers = async () => {
    setLoading(true);
    setMessage('Creating demo users...');
    
    const demoUsers = [
      { email: '<EMAIL>', password: 'Admin123!', name: 'Admin User' },
      { email: '<EMAIL>', password: 'Manager123!', name: 'Manager User' },
      { email: '<EMAIL>', password: 'Tech123!', name: 'Tech User' }
    ];
    
    const results = [];
    
    for (const user of demoUsers) {
      try {
        const { data, error } = await supabase.auth.signUp({
          email: user.email,
          password: user.password,
          options: {
            data: {
              full_name: user.name,
              username: user.email.split('@')[0]
            }
          }
        });
        
        if (error) {
          results.push(`❌ ${user.email}: ${error.message}`);
        } else {
          results.push(`✅ ${user.email}: Created successfully`);
        }
        
        // Wait between requests
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        results.push(`❌ ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    setMessage(results.join('\n'));
    setLoading(false);
    
    // Sign out after creation
    setTimeout(async () => {
      await supabase.auth.signOut();
    }, 1000);
  };

  React.useEffect(() => {
    checkSetupStatus();
  }, []);

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Quick User Setup
        </CardTitle>
        <CardDescription>
          Setup users for testing the authentication system
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status Indicators */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            {status.connection ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm">Supabase Connection</span>
          </div>
          
          <div className="flex items-center gap-2">
            {status.database ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm">Database Tables</span>
          </div>
          
          <div className="flex items-center gap-2">
            {status.user ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <Database className="h-4 w-4 text-gray-400" />
            )}
            <span className="text-sm">Demo Users</span>
          </div>
        </div>
        
        {/* User Creation Form */}
        <div className="space-y-3">
          <Input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={loading}
          />
          
          <Input
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={loading}
          />
          
          <div className="flex gap-2">
            <Button 
              onClick={createUser} 
              disabled={loading || !status.connection || !status.database}
              className="flex-1"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Create User'}
            </Button>
            
            <Button 
              onClick={createDemoUsers} 
              disabled={loading || !status.connection || !status.database}
              variant="outline"
            >
              Create Demo Users
            </Button>
          </div>
        </div>
        
        <Button 
          onClick={checkSetupStatus} 
          disabled={loading}
          variant="outline"
          className="w-full"
        >
          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Check Status'}
        </Button>
        
        {/* Message Display */}
        {message && (
          <Alert>
            <AlertDescription className="whitespace-pre-line text-sm">
              {message}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};
