// Utility untuk check status Supabase connection dan setup
import { supabase } from '../lib/supabase';

export async function checkSupabaseConnection() {
  try {
    console.log('🔍 Checking Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Supabase connection error:', error);
      return { success: false, error: error.message };
    }
    
    console.log('✅ Supabase connection successful');
    return { success: true, session: data.session };
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function checkDatabaseTables() {
  try {
    console.log('🔍 Checking database tables...');
    
    // Check if profiles table exists
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (profilesError) {
      console.error('❌ Profiles table not found:', profilesError.message);
      return { 
        success: false, 
        error: 'Database tables not created. Please run the schema setup first.',
        details: profilesError.message
      };
    }
    
    // Check if roles table exists
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('count')
      .limit(1);
    
    if (rolesError) {
      console.error('❌ Roles table not found:', rolesError.message);
      return { 
        success: false, 
        error: 'Roles table not created. Please run the schema setup first.',
        details: rolesError.message
      };
    }
    
    console.log('✅ Database tables exist');
    return { success: true, message: 'All required tables exist' };
    
  } catch (error) {
    console.error('❌ Database check error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function checkExistingUsers() {
  try {
    console.log('🔍 Checking existing users...');
    
    // This will only work if user is authenticated or has proper RLS policies
    const { data: { users }, error } = await supabase.auth.admin.listUsers();
    
    if (error) {
      console.log('ℹ️ Cannot list users (normal for anon key):', error.message);
      return { 
        success: true, 
        message: 'Cannot list users with anon key (this is normal)',
        userCount: 'unknown'
      };
    }
    
    console.log(`✅ Found ${users?.length || 0} users`);
    return { 
      success: true, 
      userCount: users?.length || 0,
      users: users?.map(u => ({ id: u.id, email: u.email }))
    };
    
  } catch (error) {
    console.error('❌ User check error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function runFullDiagnostic() {
  console.log('🔧 Running full Supabase diagnostic...');
  
  const results = {
    connection: await checkSupabaseConnection(),
    database: await checkDatabaseTables(),
    users: await checkExistingUsers()
  };
  
  console.log('📊 Diagnostic Results:', results);
  
  // Provide recommendations
  const recommendations = [];
  
  if (!results.connection.success) {
    recommendations.push('❌ Fix Supabase connection first');
  }
  
  if (!results.database.success) {
    recommendations.push('❌ Run database schema setup (quick-setup.sql)');
  }
  
  if (results.users.success && results.users.userCount === 0) {
    recommendations.push('⚠️ No users found - create demo users');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('✅ Everything looks good!');
  }
  
  console.log('💡 Recommendations:', recommendations);
  
  return { results, recommendations };
}

// Make available in development console
if (import.meta.env.DEV) {
  (window as any).checkSupabaseConnection = checkSupabaseConnection;
  (window as any).checkDatabaseTables = checkDatabaseTables;
  (window as any).checkExistingUsers = checkExistingUsers;
  (window as any).runFullDiagnostic = runFullDiagnostic;
  
  console.log('🔧 Supabase diagnostic tools available:');
  console.log('- checkSupabaseConnection()');
  console.log('- checkDatabaseTables()');
  console.log('- checkExistingUsers()');
  console.log('- runFullDiagnostic()');
}
