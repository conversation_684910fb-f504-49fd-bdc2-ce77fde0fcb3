"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TroubleTicket, Material, ClosureDetail } from "./TroubleTicketManagement";

interface MaterialUsageSummaryCardProps {
  ticket: TroubleTicket;
}

const MaterialUsageSummaryCard: React.FC<MaterialUsageSummaryCardProps> = ({ ticket }) => {
  // Material data is now directly on the ticket object, no aggregation needed from activities
  const aggregatedMaterials = ticket.materialsUsed || [];
  const totalClosures = ticket.numClosures || 0;
  const totalCables = ticket.numCables || 0;
  const totalHdpe = ticket.numHdpe || 0;
  const customMaterials = ticket.otherCustomMaterials ? [ticket.otherCustomMaterials] : [];
  const allClosureSplicingDetails = ticket.closureSplicingDetails || [];

  const hasAnyMaterialData = aggregatedMaterials.length > 0 ||
                             totalClosures > 0 || totalCables > 0 || totalHdpe > 0 ||
                             customMaterials.length > 0 || allClosureSplicingDetails.length > 0;

  return (
    <Card className="lg:col-span-3">
      <CardHeader>
        <CardTitle className="text-2xl flex items-center">
          <Package className="mr-2 h-6 w-6 text-green-500" /> Material Usage Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!hasAnyMaterialData && (
          <p className="text-muted-foreground">No material usage recorded for this ticket.</p>
        )}

        {aggregatedMaterials.length > 0 && (
          <div>
            <h3 className="font-semibold text-lg mb-2">Aggregated Materials:</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Material</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {aggregatedMaterials.map((material, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{material.name}</TableCell>
                    <TableCell>{material.quantity.toFixed(2)}</TableCell>
                    <TableCell>{material.unit}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {(totalClosures > 0 || totalCables > 0 || totalHdpe > 0) && (
          <div>
            <h3 className="font-semibold text-lg mb-2">Component Counts:</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Component</TableHead>
                  <TableHead>Count</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {totalClosures > 0 && (
                  <TableRow>
                    <TableCell className="font-medium">Total Closures</TableCell>
                    <TableCell>{totalClosures}</TableCell>
                  </TableRow>
                )}
                {totalCables > 0 && (
                  <TableRow>
                    <TableCell className="font-medium">Total Cables</TableCell>
                    <TableCell>{totalCables}</TableCell>
                  </TableRow>
                )}
                {totalHdpe > 0 && (
                  <TableRow>
                    <TableCell className="font-medium">Total HDPE</TableCell>
                    <TableCell>{totalHdpe}</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        )}

        {customMaterials.length > 0 && (
          <div>
            <h3 className="font-semibold text-lg mb-2">Other Custom Materials:</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customMaterials.map((material, index) => (
                  <TableRow key={index}>
                    <TableCell>{material}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {allClosureSplicingDetails.length > 0 && (
          <div>
            <h3 className="font-semibold text-lg mb-2">Aggregated Closure Splicing Details:</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Closure ID</TableHead>
                  <TableHead>Spliced Cores</TableHead>
                  <TableHead>Latitude</TableHead> {/* Changed column header */}
                  <TableHead>Longitude</TableHead> {/* New column header */}
                </TableRow>
              </TableHeader>
              <TableBody>
                {allClosureSplicingDetails.map((detail, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{detail.closureId}</TableCell>
                    <TableCell>{detail.splicedCores}</TableCell>
                    <TableCell>
                      {detail.latitude !== undefined && detail.latitude !== null
                        ? detail.latitude.toFixed(4)
                        : "N/A"}
                    </TableCell>
                    <TableCell>
                      {detail.longitude !== undefined && detail.longitude !== null
                        ? detail.longitude.toFixed(4)
                        : "N/A"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MaterialUsageSummaryCard;