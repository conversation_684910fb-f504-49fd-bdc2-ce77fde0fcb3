import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { LogOut, Settings, User, ChevronRight } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface UserProfileProps {
  compact?: boolean;
}

const UserProfile: React.FC<UserProfileProps> = ({ compact = false }) => {
  const { user, roles, logout, isAuthenticated } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Jika user tidak terautentikasi, tampilkan pesan
  if (!isAuthenticated || !user) {
    if (compact) {
      return (
        <div className="p-3 rounded-lg bg-sidebar-accent/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-sky-500 flex items-center justify-center">
              <User className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-sidebar-foreground truncate">
                <Link to="/login">Login</Link>
              </p>
            </div>
          </div>
        </div>
      );
    }
    
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <p>Please login to view your profile</p>
            <Button className="mt-4" onClick={() => navigate('/login')}>
              Login
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Fungsi untuk menangani logout
  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logout();
      toast({
        title: 'Logout Successful',
        description: 'You have been logged out of the system',
      });
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: 'Logout Failed',
        description: 'An error occurred during logout',
        variant: 'destructive',
      });
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Function to get initials from user name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  // Tampilan kompak untuk sidebar
  if (compact) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="p-3 rounded-lg bg-sidebar-accent/50 cursor-pointer hover:bg-sidebar-accent/70 transition-colors">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                {user.avatar_url ? (
                  <AvatarImage src={user.avatar_url} alt={user.full_name || user.username} />
                ) : null}
                <AvatarFallback className="text-sm">
                  {user.full_name ? getInitials(user.full_name) : user.username.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-sidebar-foreground truncate">
                  {user.full_name || user.username}
                </p>
                <p className="text-xs text-sidebar-foreground/70 truncate">
                  {roles.length > 0 ? roles[0].name : 'User'}
                </p>
              </div>
              <ChevronRight className="h-4 w-4 text-sidebar-foreground/50" />
            </div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem asChild>
            <Link to="/profile" className="cursor-pointer">
              <User className="mr-2 h-4 w-4" />
              <span>My Profile</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to="/settings" className="cursor-pointer">
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="text-red-500 focus:text-red-500 cursor-pointer"
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }
  
  // Tampilan penuh untuk halaman profil
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-4">
        <CardTitle>User Profile</CardTitle>
        <CardDescription>Your account information and settings</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col items-center space-y-4">
          <Avatar className="h-24 w-24">
            {user.avatar_url ? (
              <AvatarImage src={user.avatar_url} alt={user.full_name || user.username} />
            ) : null}
            <AvatarFallback className="text-lg">
              {user.full_name ? getInitials(user.full_name) : user.username.substring(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="text-center">
            <h3 className="text-xl font-semibold">{user.full_name || user.username}</h3>
            <p className="text-sm text-gray-500">{user.email}</p>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="text-sm font-medium">Peran</h4>
          <div className="flex flex-wrap gap-2">
            {roles.map((role) => (
              <Badge key={role.id} variant="secondary">
                {role.name}
              </Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="text-sm font-medium">Account Information</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-gray-500">Username</div>
            <div>{user.username}</div>
            <div className="text-gray-500">Status</div>
            <div>
              {user.is_active ? (
                <Badge variant="success">Active</Badge>
              ) : (
                <Badge variant="destructive">Inactive</Badge>
              )}
            </div>
            <div className="text-gray-500">Last Login</div>
            <div>{user.last_login ? new Date(user.last_login).toLocaleString() : 'Never logged in'}</div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          className="flex items-center gap-2"
          onClick={() => navigate('/settings')}
        >
          <Settings className="h-4 w-4" />
          Settings
        </Button>
        <Button
          variant="destructive"
          className="flex items-center gap-2"
          onClick={handleLogout}
          disabled={isLoggingOut}
        >
          <LogOut className="h-4 w-4" />
          {isLoggingOut ? 'Logging out...' : 'Logout'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default UserProfile;