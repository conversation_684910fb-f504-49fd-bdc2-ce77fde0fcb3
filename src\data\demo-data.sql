-- =====================================================
-- DEMO DATA UNTUK TESTING MS DASHBOARD AUTH
-- =====================================================
-- Jalankan setelah auth_schema.sql berhasil dieksekusi
-- File ini berisi data demo untuk testing sistem auth

-- =====================================================
-- DEMO USERS (akan dibuat melalui Supabase Auth UI)
-- =====================================================
-- Catatan: User harus dibuat melalui Supabase Auth, bukan langsung di database
-- Berikut adalah contoh user yang bisa dibuat untuk testing:

/*
Demo Users untuk Testing:
1. <EMAIL> (password: Admin123!)
2. <EMAIL> (password: Manager123!)
3. <EMAIL> (password: Tech123!)
4. <EMAIL> (password: Operator123!)
5. <EMAIL> (password: Viewer123!)
*/

-- =====================================================
-- UPDATE PROFILES UNTUK DEMO USERS
-- =====================================================
-- Setelah user dibuat melalui Supabase Auth, update profile mereka
-- Ganti UUID dengan ID user yang sebenarnya dari auth.users

-- Contoh update profile (ganti dengan UUID yang sebenarnya):
/*
UPDATE public.profiles SET
    username = 'admin',
    full_name = 'System Administrator',
    department = 'IT',
    position = 'System Admin',
    phone = '+62-812-3456-7890'
WHERE id = 'user-uuid-here';

UPDATE public.profiles SET
    username = 'manager',
    full_name = 'Network Manager',
    department = 'Network Operations',
    position = 'Manager',
    phone = '+62-812-3456-7891'
WHERE id = 'user-uuid-here';

UPDATE public.profiles SET
    username = 'technician',
    full_name = 'Field Technician',
    department = 'Field Operations',
    position = 'Senior Technician',
    phone = '+62-812-3456-7892'
WHERE id = 'user-uuid-here';

UPDATE public.profiles SET
    username = 'operator',
    full_name = 'Network Operator',
    department = 'Network Operations',
    position = 'Operator',
    phone = '+62-812-3456-7893'
WHERE id = 'user-uuid-here';

UPDATE public.profiles SET
    username = 'viewer',
    full_name = 'Report Viewer',
    department = 'Management',
    position = 'Analyst',
    phone = '+62-812-3456-7894'
WHERE id = 'user-uuid-here';
*/

-- =====================================================
-- ASSIGN ROLES TO DEMO USERS
-- =====================================================
-- Script untuk assign roles ke demo users
-- Ganti dengan UUID yang sebenarnya

-- Function untuk assign role berdasarkan email
CREATE OR REPLACE FUNCTION assign_demo_roles()
RETURNS void AS $$
DECLARE
    admin_user_id UUID;
    manager_user_id UUID;
    technician_user_id UUID;
    operator_user_id UUID;
    viewer_user_id UUID;
    
    admin_role_id UUID;
    manager_role_id UUID;
    technician_role_id UUID;
    operator_role_id UUID;
    viewer_role_id UUID;
BEGIN
    -- Get role IDs
    SELECT id INTO admin_role_id FROM public.roles WHERE name = 'admin';
    SELECT id INTO manager_role_id FROM public.roles WHERE name = 'manager';
    SELECT id INTO technician_role_id FROM public.roles WHERE name = 'technician';
    SELECT id INTO operator_role_id FROM public.roles WHERE name = 'operator';
    SELECT id INTO viewer_role_id FROM public.roles WHERE name = 'viewer';
    
    -- Get user IDs by email (if they exist)
    SELECT id INTO admin_user_id FROM auth.users WHERE email = '<EMAIL>';
    SELECT id INTO manager_user_id FROM auth.users WHERE email = '<EMAIL>';
    SELECT id INTO technician_user_id FROM auth.users WHERE email = '<EMAIL>';
    SELECT id INTO operator_user_id FROM auth.users WHERE email = '<EMAIL>';
    SELECT id INTO viewer_user_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Assign roles (only if users exist)
    IF admin_user_id IS NOT NULL AND admin_role_id IS NOT NULL THEN
        INSERT INTO public.user_roles (user_id, role_id) 
        VALUES (admin_user_id, admin_role_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
    END IF;
    
    IF manager_user_id IS NOT NULL AND manager_role_id IS NOT NULL THEN
        INSERT INTO public.user_roles (user_id, role_id) 
        VALUES (manager_user_id, manager_role_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
    END IF;
    
    IF technician_user_id IS NOT NULL AND technician_role_id IS NOT NULL THEN
        INSERT INTO public.user_roles (user_id, role_id) 
        VALUES (technician_user_id, technician_role_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
    END IF;
    
    IF operator_user_id IS NOT NULL AND operator_role_id IS NOT NULL THEN
        INSERT INTO public.user_roles (user_id, role_id) 
        VALUES (operator_user_id, operator_role_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
    END IF;
    
    IF viewer_user_id IS NOT NULL AND viewer_role_id IS NOT NULL THEN
        INSERT INTO public.user_roles (user_id, role_id) 
        VALUES (viewer_user_id, viewer_role_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
    END IF;
    
    RAISE NOTICE 'Demo roles assigned successfully';
END;
$$ LANGUAGE plpgsql;

-- Jalankan function untuk assign roles
-- SELECT assign_demo_roles();

-- =====================================================
-- TESTING QUERIES
-- =====================================================

-- Query untuk melihat semua users dengan roles
-- SELECT * FROM user_roles_summary ORDER BY created_at;

-- Query untuk melihat permissions user tertentu
-- SELECT * FROM user_permissions_view WHERE email = '<EMAIL>';

-- Query untuk test permission function
-- SELECT user_has_permission('user-uuid-here', 'view_dashboard');

-- Query untuk melihat audit logs
-- SELECT * FROM audit_logs ORDER BY created_at DESC LIMIT 10;

-- =====================================================
-- CLEANUP FUNCTIONS (untuk development)
-- =====================================================

-- Function untuk reset demo data
CREATE OR REPLACE FUNCTION reset_demo_data()
RETURNS void AS $$
BEGIN
    -- Remove all user roles
    DELETE FROM public.user_roles;
    
    -- Reset audit logs
    DELETE FROM public.audit_logs;
    
    -- Reset profiles (keep the records but clear custom data)
    UPDATE public.profiles SET
        username = NULL,
        full_name = NULL,
        department = NULL,
        position = NULL,
        phone = NULL,
        last_login = NULL
    WHERE id IN (
        SELECT id FROM auth.users 
        WHERE email LIKE '%@msdashboard.com'
    );
    
    RAISE NOTICE 'Demo data reset successfully';
END;
$$ LANGUAGE plpgsql;

-- Function untuk melihat status demo users
CREATE OR REPLACE FUNCTION check_demo_users()
RETURNS TABLE(
    email TEXT,
    username TEXT,
    full_name TEXT,
    roles TEXT[],
    is_active BOOLEAN,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.email::TEXT,
        p.username::TEXT,
        p.full_name::TEXT,
        COALESCE(ARRAY_AGG(r.name ORDER BY r.name) FILTER (WHERE r.name IS NOT NULL), ARRAY[]::TEXT[]) as roles,
        p.is_active,
        u.created_at
    FROM auth.users u
    LEFT JOIN public.profiles p ON u.id = p.id
    LEFT JOIN public.user_roles ur ON u.id = ur.user_id
    LEFT JOIN public.roles r ON ur.role_id = r.id
    WHERE u.email LIKE '%@msdashboard.com'
    GROUP BY u.email, p.username, p.full_name, p.is_active, u.created_at
    ORDER BY u.created_at;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- USAGE INSTRUCTIONS
-- =====================================================

/*
CARA MENGGUNAKAN DEMO DATA:

1. Pastikan auth_schema.sql sudah dijalankan terlebih dahulu

2. Buat demo users melalui Supabase Auth UI atau API:
   - Buka Dashboard Supabase > Authentication > Users
   - Klik "Add User" untuk setiap demo user
   - Atau gunakan API signup

3. Setelah users dibuat, jalankan function assign_demo_roles():
   SELECT assign_demo_roles();

4. Update profile data sesuai kebutuhan dengan query UPDATE di atas

5. Test dengan queries:
   SELECT check_demo_users();
   SELECT * FROM user_permissions_view WHERE email = '<EMAIL>';

6. Untuk reset data (development only):
   SELECT reset_demo_data();

CATATAN PENTING:
- Jangan jalankan di production
- Ganti password default dengan yang lebih kuat
- Hapus demo users setelah testing selesai
*/
