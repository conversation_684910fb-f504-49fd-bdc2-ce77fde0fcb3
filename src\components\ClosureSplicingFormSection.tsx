"use client";

import React from "react";
import { useFormContex<PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import * as z from "zod";

// Define Zod schema for ClosureDetail (re-defined here for local scope, or import if shared)
const closureDetailSchema = z.object({
  closureId: z.string().min(1, { message: "Closure ID cannot be empty." }),
  splicedCores: z.coerce.number().min(0, { message: "Spliced cores must be non-negative." }),
  latitude: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().optional()
  ),
  longitude: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.coerce.number().optional()
  ),
});

interface ClosureSplicingFormSectionProps {
  closureIndex: number;
  removeClosureSplicing: (index: number) => void;
}

const ClosureSplicingFormSection: React.FC<ClosureSplicingFormSectionProps> = ({
  closureIndex,
  removeClosureSplicing,
}) => {
  const { control, register, formState, getValues, setValue, watch } = useFormContext();
  const { errors } = formState;

  // Local state for latitude and longitude input values
  const [localLatValue, setLocalLatValue] = React.useState<string>(
    typeof watch(`closureSplicingDetails.${closureIndex}.latitude`) === 'number' && 
    !isNaN(watch(`closureSplicingDetails.${closureIndex}.latitude`) as number)
      ? (watch(`closureSplicingDetails.${closureIndex}.latitude`) as number).toFixed(4)
      : ""
  );
  const [localLonValue, setLocalLonValue] = React.useState<string>(
    typeof watch(`closureSplicingDetails.${closureIndex}.longitude`) === 'number' && 
    !isNaN(watch(`closureSplicingDetails.${closureIndex}.longitude`) as number)
      ? (watch(`closureSplicingDetails.${closureIndex}.longitude`) as number).toFixed(4)
      : ""
  );

  // Effect to sync local state when form value changes externally (e.g., on reset)
  React.useEffect(() => {
    const lat = getValues(`closureSplicingDetails.${closureIndex}.latitude`);
    setLocalLatValue(typeof lat === 'number' && !isNaN(lat) ? lat.toFixed(4) : "");
  }, [watch(`closureSplicingDetails.${closureIndex}.latitude`), getValues, closureIndex]);

  React.useEffect(() => {
    const lon = getValues(`closureSplicingDetails.${closureIndex}.longitude`);
    setLocalLonValue(typeof lon === 'number' && !isNaN(lon) ? lon.toFixed(4) : "");
  }, [watch(`closureSplicingDetails.${closureIndex}.longitude`), getValues, closureIndex]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-2 border p-2 rounded-md mb-2">
      <Label htmlFor={`closureSplicingDetails.${closureIndex}.closureId`} className="text-right md:text-left text-sm">
        Closure ID
      </Label>
      <Input id={`closureSplicingDetails.${closureIndex}.closureId`} {...register(`closureSplicingDetails.${closureIndex}.closureId`)} className="col-span-3" />
      {errors.closureSplicingDetails?.[closureIndex]?.closureId && (
        <p className="col-span-4 text-right text-xs text-red-500">{errors.closureSplicingDetails[closureIndex]?.closureId?.message}</p>
      )}

      <Label htmlFor={`closureSplicingDetails.${closureIndex}.splicedCores`} className="text-right md:text-left text-sm">
        Spliced Cores
      </Label>
      <Input 
        id={`closureSplicingDetails.${closureIndex}.splicedCores`} 
        type="number" 
        {...register(`closureSplicingDetails.${closureIndex}.splicedCores`, { valueAsNumber: true })} 
        className="col-span-3" 
      />
      {errors.closureSplicingDetails?.[closureIndex]?.splicedCores && (
        <p className="col-span-4 text-right text-xs text-red-500">{errors.closureSplicingDetails[closureIndex]?.splicedCores?.message}</p>
      )}

      <Label htmlFor={`closureSplicingDetails.${closureIndex}.latitude`} className="text-right md:text-left text-sm">
        Latitude
      </Label>
      <Input 
        id={`closureSplicingDetails.${closureIndex}.latitude`} 
        type="number" 
        step="any"
        value={localLatValue}
        onChange={(e) => setLocalLatValue(e.target.value)}
        onBlur={(e) => {
          const val = parseFloat(e.target.value);
          setValue(`closureSplicingDetails.${closureIndex}.latitude`, isNaN(val) ? undefined : val, { shouldValidate: true });
        }}
        className="col-span-3" 
      />
      {errors.closureSplicingDetails?.[closureIndex]?.latitude && (
        <p className="col-span-4 text-right text-xs text-red-500">{errors.closureSplicingDetails[closureIndex]?.latitude?.message}</p>
      )}

      <Label htmlFor={`closureSplicingDetails.${closureIndex}.longitude`} className="text-right md:text-left text-sm">
        Longitude
      </Label>
      <Input 
        id={`closureSplicingDetails.${closureIndex}.longitude`} 
        type="number" 
        step="any"
        value={localLonValue}
        onChange={(e) => setLocalLonValue(e.target.value)}
        onBlur={(e) => {
          const val = parseFloat(e.target.value);
          setValue(`closureSplicingDetails.${closureIndex}.longitude`, isNaN(val) ? undefined : val, { shouldValidate: true });
        }}
        className="col-span-3" 
      />
      {errors.closureSplicingDetails?.[closureIndex]?.longitude && (
        <p className="col-span-4 text-right text-xs text-red-500">{errors.closureSplicingDetails[closureIndex]?.longitude?.message}</p>
      )}

      <div className="col-span-4 flex justify-end">
        <Button type="button" variant="destructive" size="sm" onClick={() => removeClosureSplicing(closureIndex)}>
          <Trash2 className="h-3 w-3 mr-1" /> Remove Closure
        </Button>
      </div>
    </div>
  );
};

export default ClosureSplicingFormSection;