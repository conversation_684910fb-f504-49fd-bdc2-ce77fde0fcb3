import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Eye, Wrench, Activity, Plus, Search, Filter, Ticket, Edit, Trash2, FileText, Camera } from 'lucide-react';
import { Link } from 'react-router-dom';
import { usePatrolData } from '@/contexts/PatrolDataContext';
import PhotoUpload from '@/components/PhotoUpload';
import PhotoGallery from '@/components/PhotoGallery';

const PatrolPage: React.FC = () => {
  const {
    findings,
    assets,
    otdrMeasurements,
    addFinding,
    updateFinding,
    addAsset,
    updateAsset,
    deleteAsset,
    addOTDRMeasurement,
    updateOTDRMeasurement,
    deleteOTDRMeasurement,
    deleteFinding,
    getCriticalFindings,
    getFailedOTDRMeasurements,
    generateTroubleTicketFromFinding,
    getRouteRiskLevel
  } = usePatrolData();
  
  const [activeTab, setActiveTab] = useState('findings');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [isPhotoGalleryOpen, setIsPhotoGalleryOpen] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    type: '',
    severity: '',
    location: '',
    description: '',
    reportedBy: '',
    impactLevel: '',
    affectedAssets: '',
    remark: '',
    findingLength: '',
    thirdPartyContact: '',
    // Asset fields
    name: '',
    assetType: '',
    condition: '',
    completeness: '',
    maintenanceHistory: '',
    notes: '',
    // OTDR fields
    route: '',
    core: '',
    distance: '',
    loss: '',
    reflectance: '',
    technician: '',
    traffic: ''
  });

  // Photo states
  const [findingPhotos, setFindingPhotos] = useState<File[]>([]);
  const [assetPhotos, setAssetPhotos] = useState<File[]>([]);
  const [otdrPhotos, setOtdrPhotos] = useState<File[]>([]);

  // Helper function to convert File to base64
  const convertFilesToBase64 = async (files: File[]): Promise<string[]> => {
    const promises = files.map(file => {
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    });
    return Promise.all(promises);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'pass': return 'bg-green-100 text-green-800';
      case 'fail': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'excellent': return 'bg-green-500';
      case 'good': return 'bg-blue-500';
      case 'fair': return 'bg-yellow-500';
      case 'poor': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'cable_expose':
      case 'hdpe_expose':
        return <AlertTriangle className="h-4 w-4" />;
      case 'third_party_work':
        return <Wrench className="h-4 w-4" />;
      case 'network_threat':
        return <Eye className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const handleCreateTroubleTicket = (findingId: string) => {
    // Navigate to Problem Handling page with maintenance form open
    window.location.href = '/problem-handling/maintenance';
  };

  const filteredFindings = findings.filter(finding => {
    const matchesSearch = finding.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         finding.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || finding.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const resetForm = () => {
    setFormData({
      type: '',
      severity: '',
      location: '',
      description: '',
      reportedBy: '',
      impactLevel: '',
      affectedAssets: '',
      remark: '',
      findingLength: '',
      thirdPartyContact: '',
      // Asset fields
      name: '',
      assetType: '',
      condition: '',
      completeness: '',
      maintenanceHistory: '',
      notes: '',
      // OTDR fields
      route: '',
      core: '',
      distance: '',
      loss: '',
      reflectance: '',
      technician: '',
      traffic: ''
    });
    // Reset photos
    setFindingPhotos([]);
    setAssetPhotos([]);
    setOtdrPhotos([]);
  };

  const handleAddReport = async () => {
    if (activeTab === 'findings') {
      const photos = await convertFilesToBase64(findingPhotos);
      const newFinding = {
        id: Date.now().toString(),
        type: formData.type,
        location: formData.location,
        description: formData.description,
        severity: formData.severity as 'low' | 'medium' | 'high' | 'critical',
        status: 'open' as const,
        reportedBy: formData.reportedBy,
        reportedDate: new Date().toISOString().split('T')[0],
        route: formData.route,
        impactLevel: formData.impactLevel as 'low' | 'medium' | 'high',
        affectedAssets: formData.affectedAssets ? formData.affectedAssets.split(',').map(s => s.trim()) : [],
        remark: formData.remark,
        findingLength: formData.findingLength,
        thirdPartyContact: formData.thirdPartyContact,
        photos: photos
      };
      addFinding(newFinding);
    } else if (activeTab === 'assets') {
      const photos = await convertFilesToBase64(assetPhotos);
      const newAsset = {
        id: Date.now().toString(),
        name: formData.name,
        type: formData.assetType,
        location: formData.location,
        condition: formData.condition as 'excellent' | 'good' | 'fair' | 'poor' | 'critical',
        completeness: formData.completeness,
        lastInspection: new Date().toISOString().split('T')[0],
        route: formData.route,
        notes: formData.notes,
        changes: [],
        maintenanceHistory: [],
        photos: photos
      };
      addAsset(newAsset);
    } else if (activeTab === 'otdr') {
      const photos = await convertFilesToBase64(otdrPhotos);
      const newOTDR = {
        id: Date.now().toString(),
        route: formData.route,
        core: formData.core,
        distance: formData.distance,
        loss: formData.loss,
        reflectance: formData.reflectance,
        status: 'pass' as const,
        measurementDate: new Date().toISOString().split('T')[0],
        technician: formData.technician,
        notes: formData.notes,
        traffic: formData.traffic,
        photos: photos
      };
      addOTDRMeasurement(newOTDR);
    }
    resetForm();
    setShowAddDialog(false);
  };

  const handleEdit = (item: any, type: string) => {
    setEditingItem({ ...item, type });
    setFormData({
      type: item.type || '',
      location: item.location || '',
      description: item.description || '',
      severity: item.severity || 'medium',
      route: item.route || '',
      reportedBy: item.reportedBy || '',
      impactLevel: item.impactLevel || 'low',
      affectedAssets: item.affectedAssets ? item.affectedAssets.join(', ') : '',
      remark: item.remark || '',
      findingLength: item.findingLength || '',
      thirdPartyContact: item.thirdPartyContact || '',
      name: item.name || '',
      assetType: item.type || '',
      condition: item.condition || 'good',
      completeness: item.completeness || 100,
      notes: item.notes || '',
      core: item.core || '',
      distance: item.distance || '',
      loss: item.loss || '',
      reflectance: item.reflectance || '',
      technician: item.technician || '',
      traffic: item.traffic || ''
    });
    setShowEditDialog(true);
  };

  const handleUpdate = () => {
    if (editingItem.type === 'finding') {
      const updatedFinding = {
        ...editingItem,
        type: formData.type,
        location: formData.location,
        description: formData.description,
        severity: formData.severity,
        route: formData.route,
        reportedBy: formData.reportedBy,
        impactLevel: formData.impactLevel,
        affectedAssets: formData.affectedAssets ? formData.affectedAssets.split(',').map(s => s.trim()) : [],
        remark: formData.remark,
        findingLength: formData.findingLength,
        thirdPartyContact: formData.thirdPartyContact
      };
      updateFinding(editingItem.id, updatedFinding);
    } else if (editingItem.type === 'asset') {
      const updatedAsset = {
        ...editingItem,
        name: formData.name,
        type: formData.assetType,
        location: formData.location,
        condition: formData.condition,
        completeness: formData.completeness,
        route: formData.route,
        notes: formData.notes
      };
      updateAsset(editingItem.id, updatedAsset);
    } else if (editingItem.type === 'otdr') {
      const updatedOTDR = {
        ...editingItem,
        route: formData.route,
        core: formData.core,
        distance: formData.distance,
        loss: formData.loss,
        reflectance: formData.reflectance,
        technician: formData.technician,
        notes: formData.notes,
        traffic: formData.traffic
      };
      updateOTDRMeasurement(editingItem.id, updatedOTDR);
    }
    resetForm();
    setShowEditDialog(false);
    setEditingItem(null);
  };

  const handleDelete = (id: string, type: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      if (type === 'finding') {
        deleteFinding(id);
      } else if (type === 'asset') {
        deleteAsset(id);
      } else if (type === 'otdr') {
        deleteOTDRMeasurement(id);
      }
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Patrol Management</h1>
          <div className="flex gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
            <span>Critical Findings: {getCriticalFindings().length}</span>
            <span>Failed OTDR: {getFailedOTDRMeasurements().length}</span>
            <span>Poor Assets: {assets.filter(a => a.condition === 'poor' || a.condition === 'critical').length}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Link to="/patrol/sor">
            <Button variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              SOR OTDR
            </Button>
          </Link>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button size="sm" onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Report
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="findings">Findings Monitoring</TabsTrigger>
        <TabsTrigger value="assets">Asset Monitoring</TabsTrigger>
        <TabsTrigger value="otdr">OTDR Measurement</TabsTrigger>
        </TabsList>

        {/* Monitoring Temuan */}
        <TabsContent value="findings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Findings Monitoring
              </CardTitle>
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search findings..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredFindings.map((finding) => (
                  <div key={finding.id} className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded-full ${getSeverityColor(finding.severity)} text-white`}>
                          {getTypeIcon(finding.type)}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{finding.location}</h3>
                          <p className="text-gray-600 dark:text-gray-400 mt-1">{finding.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                            <span>Reported by: {finding.reportedBy}</span>
          <span>Date: {finding.reportedDate}</span>
                            {finding.route && <span>Route: {finding.route}</span>}
                            {finding.impactLevel && <span>Impact: {finding.impactLevel}</span>}
                          </div>
                          {finding.affectedAssets && finding.affectedAssets.length > 0 && (
                            <div className="mt-2 text-sm text-orange-600 dark:text-orange-400">
                              Affected Assets: {finding.affectedAssets.join(', ')}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleCreateTroubleTicket(finding.id)}
                          className="text-xs"
                        >
                          <Ticket className="h-3 w-3 mr-1" />
                          Maintenance Timeline
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleEdit(finding, 'finding')}
                          className="text-xs"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button 
                          size="sm" 
                          variant="destructive"
                          onClick={() => handleDelete(finding.id, 'finding')}
                          className="text-xs"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                        <Badge className={getStatusColor(finding.status)}>
                          {finding.status}
                        </Badge>
                        <Badge variant="outline">
                          {finding.severity}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Monitoring Asset */}
        <TabsContent value="assets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Asset Monitoring
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {assets.map((asset) => (
                  <Card key={asset.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{asset.name}</CardTitle>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{asset.location}</p>
                        </div>
                        <Badge variant="outline">{asset.type.toUpperCase()}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Condition:</span>
                        <Badge className={`${getConditionColor(asset.condition)} text-white`}>
                          {asset.condition}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Completeness:</span>
                        <span className="text-sm font-semibold">{asset.completeness}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${asset.completeness}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-500">
                        <p>Last Inspection: {asset.lastInspection}</p>
                        <p>Route: {asset.route}</p>
                      </div>
                      {asset.notes && (
                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                          Notes: {asset.notes}
                        </div>
                      )}
                      {asset.maintenanceHistory && asset.maintenanceHistory.length > 0 && (
                        <div className="mt-2 text-sm text-blue-600 dark:text-blue-400">
                          Last Maintenance: {asset.maintenanceHistory[asset.maintenanceHistory.length - 1].date}
                        </div>
                      )}
                      {asset.changes.length > 0 && (
                        <div className="mt-3">
                          <p className="text-sm font-medium mb-1">Recent Changes:</p>
                          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            {asset.changes.map((change, index) => (
                              <li key={index} className="flex items-start gap-1">
                                <span className="text-blue-500">•</span>
                                {change}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {/* Display Photos */}
                      {asset.photos && asset.photos.length > 0 && (
                        <div className="mt-3">
                          <div className="flex items-center gap-2 mb-2">
                            <Camera className="h-4 w-4 text-gray-500" />
                            <span className="text-sm text-gray-500">{asset.photos.length} photo{asset.photos.length > 1 ? 's' : ''}</span>
                          </div>
                          <div className="flex gap-2 overflow-x-auto">
                            {asset.photos.slice(0, 3).map((photo, index) => (
                              <div key={index} className="flex-shrink-0">
                                <img
                                  src={photo}
                                  alt={`Asset photo ${index + 1}`}
                                  className="w-16 h-16 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity"
                                  onClick={() => {
                                    setSelectedPhotos(asset.photos);
                                    setIsPhotoGalleryOpen(true);
                                  }}
                                  onError={(e) => {
                                    e.currentTarget.src = '/placeholder.svg';
                                  }}
                                />
                              </div>
                            ))}
                            {asset.photos.length > 3 && (
                              <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg border flex items-center justify-center text-xs text-gray-500 cursor-pointer hover:bg-gray-200 transition-colors"
                                   onClick={() => {
                                     setSelectedPhotos(asset.photos);
                                     setIsPhotoGalleryOpen(true);
                                   }}>
                                +{asset.photos.length - 3}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                      <div className="flex gap-2 mt-3">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="text-xs"
                          onClick={() => handleEdit(asset, 'asset')}
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button 
                          size="sm" 
                          variant="destructive" 
                          className="text-xs"
                          onClick={() => handleDelete(asset.id, 'asset')}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pengukuran OTDR */}
        <TabsContent value="otdr" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                OTDR Measurement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3 font-semibold">Route</th>
                      <th className="text-left p-3 font-semibold">Core</th>
                      <th className="text-left p-3 font-semibold">Distance</th>
                      <th className="text-left p-3 font-semibold">Loss</th>
                      <th className="text-left p-3 font-semibold">Reflectance</th>
                      <th className="text-left p-3 font-semibold">Date</th>
                      <th className="text-left p-3 font-semibold">Technician</th>
                      <th className="text-left p-3 font-semibold">Traffic</th>
                      <th className="text-left p-3 font-semibold">Status</th>
                      <th className="text-left p-3 font-semibold">Notes</th>
                      <th className="text-left p-3 font-semibold">Photos</th>
                      <th className="text-left p-3 font-semibold">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {otdrMeasurements.map((measurement) => (
                      <tr key={measurement.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="p-3 font-medium">{measurement.route}</td>
                        <td className="p-3">{measurement.core}</td>
                        <td className="p-3">{measurement.distance}</td>
                        <td className="p-3">{measurement.loss}</td>
                        <td className="p-3">{measurement.reflectance}</td>
                        <td className="p-3">{measurement.measurementDate}</td>
                        <td className="p-3">{measurement.technician}</td>
                        <td className="p-3">{measurement.traffic || '-'}</td>
                        <td className="p-3">
                          <Badge className={getStatusColor(measurement.status)}>
                            {measurement.status}
                          </Badge>
                        </td>
                        <td className="p-3 text-gray-600 dark:text-gray-400">
                          {measurement.notes || '-'}
                        </td>
                        <td className="p-3">
                          {measurement.photos && measurement.photos.length > 0 ? (
                            <div className="flex gap-1">
                              {measurement.photos.slice(0, 2).map((photo, index) => (
                                <img
                                  key={index}
                                  src={photo}
                                  alt={`OTDR photo ${index + 1}`}
                                  className="w-8 h-8 object-cover rounded border cursor-pointer hover:opacity-80 transition-opacity"
                                  onClick={() => {
                                    setSelectedPhotos(measurement.photos);
                                    setIsPhotoGalleryOpen(true);
                                  }}
                                  onError={(e) => {
                                    e.currentTarget.src = '/placeholder.svg';
                                  }}
                                />
                              ))}
                              {measurement.photos.length > 2 && (
                                <div className="w-8 h-8 bg-gray-100 rounded border flex items-center justify-center text-xs text-gray-500 cursor-pointer hover:bg-gray-200 transition-colors"
                                     onClick={() => {
                                       setSelectedPhotos(measurement.photos);
                                       setIsPhotoGalleryOpen(true);
                                     }}>
                                  +{measurement.photos.length - 2}
                                </div>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-400 text-xs">No photos</span>
                          )}
                        </td>
                        <td className="p-3">
                          <div className="flex gap-1">
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="text-xs"
                              onClick={() => handleEdit(measurement, 'otdr')}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="destructive" 
                              className="text-xs"
                              onClick={() => handleDelete(measurement.id, 'otdr')}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Report Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Report {activeTab === 'findings' ? 'Finding' : activeTab === 'assets' ? 'Asset' : 'OTDR'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {activeTab === 'findings' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="type">Finding Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cable_expose">Exposed Cable</SelectItem>
                        <SelectItem value="hdpe_expose">Exposed HDPE</SelectItem>
                        <SelectItem value="third_party_work">Third Party Work</SelectItem>
                        <SelectItem value="network_threat">Network Threat</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="severity">Severity Level</Label>
                    <Select value={formData.severity} onValueChange={(value) => setFormData({...formData, severity: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    value={formData.description} 
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Enter finding description"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="reportedBy">Reported By</Label>
                    <Input 
                      value={formData.reportedBy} 
                      onChange={(e) => setFormData({...formData, reportedBy: e.target.value})}
                      placeholder="Reporter name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="impactLevel">Impact Level</Label>
                    <Select value={formData.impactLevel} onValueChange={(value) => setFormData({...formData, impactLevel: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="affectedAssets">Affected Assets (separate with comma)</Label>
                  <Input 
                    value={formData.affectedAssets} 
                    onChange={(e) => setFormData({...formData, affectedAssets: e.target.value})}
                    placeholder="Asset1, Asset2, Asset3"
                  />
                </div>
                <div>
                  <Label htmlFor="remark">Remark</Label>
                  <Textarea 
                    value={formData.remark} 
                    onChange={(e) => setFormData({...formData, remark: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="findingLength">Finding Length</Label>
                    <Input 
                      value={formData.findingLength} 
                      onChange={(e) => setFormData({...formData, findingLength: e.target.value})}
                      placeholder="Example: 2.5 meter"
                    />
                  </div>
                  <div>
                    <Label htmlFor="thirdPartyContact">Third Party Contact</Label>
                    <Input 
                      value={formData.thirdPartyContact} 
                      onChange={(e) => setFormData({...formData, thirdPartyContact: e.target.value})}
                      placeholder="Company name - phone number"
                    />
                  </div>
                </div>
                
                {/* Photo Upload for Findings */}
                <PhotoUpload
                  label="Finding Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={findingPhotos}
                  onPhotosChange={setFindingPhotos}
                />
              </>
            )}

            {activeTab === 'assets' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Asset Name</Label>
                    <Input 
                      value={formData.name} 
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="Enter asset name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="assetType">Asset Type</Label>
                    <Select value={formData.assetType} onValueChange={(value) => setFormData({...formData, assetType: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="closure">Closure</SelectItem>
                        <SelectItem value="manhole">Manhole</SelectItem>
                        <SelectItem value="pole">Pole</SelectItem>
                        <SelectItem value="cabinet">Cabinet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="condition">Condition</Label>
                    <Select value={formData.condition} onValueChange={(value) => setFormData({...formData, condition: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                        <SelectItem value="poor">Poor</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="completeness">Completeness (%)</Label>
                    <Input 
                      type="number" 
                      min="0" 
                      max="100"
                      value={formData.completeness} 
                      onChange={(e) => setFormData({...formData, completeness: parseInt(e.target.value) || 0})}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                
                {/* Photo Upload for Assets */}
                <PhotoUpload
                  label="Asset Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={assetPhotos}
                  onPhotosChange={setAssetPhotos}
                />
              </>
            )}

            {activeTab === 'otdr' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                  <div>
                    <Label htmlFor="core">Core</Label>
                    <Input 
                      value={formData.core} 
                      onChange={(e) => setFormData({...formData, core: e.target.value})}
                      placeholder="Enter core"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="distance">Distance</Label>
                    <Input 
                      value={formData.distance} 
                      onChange={(e) => setFormData({...formData, distance: e.target.value})}
                      placeholder="e.g., 15.2 km"
                    />
                  </div>
                  <div>
                    <Label htmlFor="loss">Loss</Label>
                    <Input 
                      value={formData.loss} 
                      onChange={(e) => setFormData({...formData, loss: e.target.value})}
                      placeholder="e.g., 0.25 dB"
                    />
                  </div>
                  <div>
                    <Label htmlFor="reflectance">Reflectance</Label>
                    <Input 
                      value={formData.reflectance} 
                      onChange={(e) => setFormData({...formData, reflectance: e.target.value})}
                      placeholder="e.g., -45 dB"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="technician">Technician</Label>
                    <Input 
                      value={formData.technician} 
                      onChange={(e) => setFormData({...formData, technician: e.target.value})}
                      placeholder="Technician name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="traffic">Traffic</Label>
                    <Input 
                      value={formData.traffic} 
                      onChange={(e) => setFormData({...formData, traffic: e.target.value})}
                      placeholder="Example: High Traffic - 80% utilization"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                
                {/* Photo Upload for OTDR */}
                <PhotoUpload
                  label="OTDR Measurement Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={otdrPhotos}
                  onPhotosChange={setOtdrPhotos}
                />
              </>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => { setShowAddDialog(false); resetForm(); }}>Cancel</Button>
              <Button onClick={handleAddReport}>Save</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit {editingItem?.type === 'finding' ? 'Finding' : editingItem?.type === 'asset' ? 'Asset' : 'OTDR'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {editingItem?.type === 'finding' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="type">Finding Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cable_expose">Exposed Cable</SelectItem>
                        <SelectItem value="hdpe_expose">Exposed HDPE</SelectItem>
                        <SelectItem value="third_party_work">Third Party Work</SelectItem>
                        <SelectItem value="network_threat">Network Threat</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="severity">Severity Level</Label>
                    <Select value={formData.severity} onValueChange={(value) => setFormData({...formData, severity: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    value={formData.description} 
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Enter finding description"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="reportedBy">Reported By</Label>
                    <Input 
                      value={formData.reportedBy} 
                      onChange={(e) => setFormData({...formData, reportedBy: e.target.value})}
                      placeholder="Reporter name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="impactLevel">Impact Level</Label>
                    <Select value={formData.impactLevel} onValueChange={(value) => setFormData({...formData, impactLevel: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="affectedAssets">Affected Assets (separate with comma)</Label>
                  <Input 
                    value={formData.affectedAssets} 
                    onChange={(e) => setFormData({...formData, affectedAssets: e.target.value})}
                    placeholder="Asset1, Asset2, Asset3"
                  />
                </div>
                <div>
                  <Label htmlFor="remark">Remark</Label>
                  <Textarea 
                    value={formData.remark} 
                    onChange={(e) => setFormData({...formData, remark: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="findingLength">Finding Length</Label>
                    <Input 
                      value={formData.findingLength} 
                      onChange={(e) => setFormData({...formData, findingLength: e.target.value})}
                      placeholder="Example: 2.5 meter"
                    />
                  </div>
                  <div>
                    <Label htmlFor="thirdPartyContact">Third Party Contact</Label>
                    <Input 
                      value={formData.thirdPartyContact} 
                      onChange={(e) => setFormData({...formData, thirdPartyContact: e.target.value})}
                      placeholder="Company name - phone number"
                    />
                  </div>
                </div>
              </>
            )}

            {editingItem?.type === 'asset' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Asset Name</Label>
                    <Input 
                      value={formData.name} 
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="Enter asset name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="assetType">Asset Type</Label>
                    <Select value={formData.assetType} onValueChange={(value) => setFormData({...formData, assetType: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="closure">Closure</SelectItem>
                        <SelectItem value="manhole">Manhole</SelectItem>
                        <SelectItem value="pole">Pole</SelectItem>
                        <SelectItem value="cabinet">Cabinet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="condition">Condition</Label>
                    <Select value={formData.condition} onValueChange={(value) => setFormData({...formData, condition: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                        <SelectItem value="poor">Poor</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="completeness">Completeness (%)</Label>
                    <Input 
                      type="number" 
                      min="0" 
                      max="100"
                      value={formData.completeness} 
                      onChange={(e) => setFormData({...formData, completeness: parseInt(e.target.value) || 0})}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
              </>
            )}

            {editingItem?.type === 'otdr' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                  <div>
                    <Label htmlFor="core">Core</Label>
                    <Input 
                      value={formData.core} 
                      onChange={(e) => setFormData({...formData, core: e.target.value})}
                      placeholder="Enter core"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="distance">Distance</Label>
                    <Input 
                      value={formData.distance} 
                      onChange={(e) => setFormData({...formData, distance: e.target.value})}
                      placeholder="e.g., 15.2 km"
                    />
                  </div>
                  <div>
                    <Label htmlFor="loss">Loss</Label>
                    <Input 
                      value={formData.loss} 
                      onChange={(e) => setFormData({...formData, loss: e.target.value})}
                      placeholder="e.g., 0.25 dB"
                    />
                  </div>
                  <div>
                    <Label htmlFor="reflectance">Reflectance</Label>
                    <Input 
                      value={formData.reflectance} 
                      onChange={(e) => setFormData({...formData, reflectance: e.target.value})}
                      placeholder="e.g., -45 dB"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="technician">Technician</Label>
                    <Input 
                      value={formData.technician} 
                      onChange={(e) => setFormData({...formData, technician: e.target.value})}
                      placeholder="Technician name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="traffic">Traffic</Label>
                    <Input 
                      value={formData.traffic} 
                      onChange={(e) => setFormData({...formData, traffic: e.target.value})}
                      placeholder="Example: High Traffic - 80% utilization"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
              </>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => { setShowEditDialog(false); resetForm(); setEditingItem(null); }}>Cancel</Button>
              <Button onClick={handleUpdate}>Update</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Photo Gallery */}
      <PhotoGallery
        isOpen={isPhotoGalleryOpen}
        onClose={() => setIsPhotoGalleryOpen(false)}
        photos={selectedPhotos}
      />
    </div>
  );
};

export default PatrolPage;