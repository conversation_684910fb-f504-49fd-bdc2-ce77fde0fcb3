import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Eye, Wrench, Activity, Plus, Search, Filter, Ticket, Edit, Trash2, FileText, Camera, Printer, Upload, Download } from 'lucide-react';
import { Link } from 'react-router-dom';
import { usePatrolData } from '@/contexts/PatrolDataContext';
import PhotoUpload from '@/components/PhotoUpload';
import PhotoGallery from '@/components/PhotoGallery';

const PatrolPage: React.FC = () => {
  const {
    findings,
    assets,
    otdrMeasurements,
    addFinding,
    updateFinding,
    addAsset,
    updateAsset,
    deleteAsset,
    addOTDRMeasurement,
    updateOTDRMeasurement,
    deleteOTDRMeasurement,
    deleteFinding,
    getCriticalFindings,
    getFailedOTDRMeasurements,
    generateTroubleTicketFromFinding,
    getRouteRiskLevel
  } = usePatrolData();
  
  const [activeTab, setActiveTab] = useState('findings');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showFindingDetail, setShowFindingDetail] = useState(false);
  const [selectedFinding, setSelectedFinding] = useState<any>(null);
  const [showAssetDetail, setShowAssetDetail] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<any>(null);
  const [isPhotoGalleryOpen, setIsPhotoGalleryOpen] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [exportType, setExportType] = useState<'all' | 'findings' | 'assets' | 'otdr'>('all');
  const [formData, setFormData] = useState({
    type: '',
    severity: '',
    location: '',
    description: '',
    reportedBy: '',
    impactLevel: '',
    affectedAssets: '',
    remark: '',
    findingLength: '',
    thirdPartyContact: '',
    // Asset fields
    name: '',
    assetType: '',
    condition: '',
    completeness: '',
    maintenanceHistory: '',
    notes: '',
    // OTDR fields
    route: '',
    core: '',
    distance: '',
    loss: '',
    reflectance: '',
    technician: '',
    traffic: ''
  });

  // Photo states
  const [findingPhotos, setFindingPhotos] = useState<File[]>([]);
  const [assetPhotos, setAssetPhotos] = useState<File[]>([]);
  const [otdrPhotos, setOtdrPhotos] = useState<File[]>([]);

  // Helper function to convert File to base64
  const convertFilesToBase64 = async (files: File[]): Promise<string[]> => {
    const promises = files.map(file => {
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    });
    return Promise.all(promises);
  };

  // Helper function to convert base64 to File
  const convertBase64ToFiles = async (base64Array: string[]): Promise<File[]> => {
    const files: File[] = [];
    for (let i = 0; i < base64Array.length; i++) {
      try {
        const response = await fetch(base64Array[i]);
        const blob = await response.blob();
        const file = new File([blob], `photo-${i + 1}.jpg`, { type: 'image/jpeg' });
        files.push(file);
      } catch (error) {
        console.error('Error converting base64 to file:', error);
      }
    }
    return files;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'pass': return 'bg-green-100 text-green-800';
      case 'fail': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'excellent': return 'bg-green-500';
      case 'good': return 'bg-blue-500';
      case 'fair': return 'bg-yellow-500';
      case 'poor': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'cable_expose':
      case 'hdpe_expose':
        return <AlertTriangle className="h-4 w-4" />;
      case 'third_party_work':
        return <Wrench className="h-4 w-4" />;
      case 'network_threat':
        return <Eye className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const handleCreateTroubleTicket = (findingId: string) => {
    // Navigate to Problem Handling page with maintenance form open
    window.location.href = '/problem-handling/maintenance';
  };

  const filteredFindings = findings.filter(finding => {
    const matchesSearch = finding.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         finding.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || finding.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const handleFindingClick = (finding: any) => {
    setSelectedFinding(finding);
    setShowFindingDetail(true);
  };

  const handleAssetClick = (asset: any) => {
    setSelectedAsset(asset);
    setShowAssetDetail(true);
  };

  const handlePrintFinding = (finding: any) => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      handlePrintFindingFallback(finding);
      return;
    }

    const photosHtml = finding.photos && finding.photos.length > 0
      ? `
        <div style="margin-top: 12px; page-break-inside: avoid;">
          <h3 style="color: #1f2937; margin-bottom: 8px; font-size: 12px; font-weight: 600;">Finding Photos</h3>
          <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; max-width: 100%;">
            ${finding.photos.slice(0, 6).map((photo: string, index: number) => `
              <div style="position: relative; border: 2px solid #e5e7eb; border-radius: 6px; overflow: hidden; aspect-ratio: 4/3; width: 100%; max-width: 120px;">
                <img src="${photo}" alt="Photo ${index + 1}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'" />
                <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 4px; background: rgba(0,0,0,0.8); color: white; text-align: center; font-size: 10px; font-weight: 600;">Photo ${index + 1}</div>
              </div>
            `).join('')}
          </div>
          ${finding.photos.length > 6 ? `<div style="font-size: 10px; color: #6b7280; margin-top: 6px; text-align: center; font-style: italic;">+${finding.photos.length - 6} more photos available</div>` : ''}
        </div>
      `
      : '<div style="margin-top: 12px; padding: 12px; background: #f9fafb; border-radius: 6px; text-align: center; color: #6b7280; font-size: 10px;">No photos available</div>';

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Finding Detail Report - ${finding.location}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 10px; color: #333; }
            .header { text-align: center; margin-bottom: 15px; border-bottom: 1px solid #dc2626; padding-bottom: 8px; }
            .title { font-size: 14px; font-weight: bold; color: #1f2937; margin-bottom: 2px; }
            .subtitle { font-size: 8px; color: #6b7280; }
            .section { margin-bottom: 12px; page-break-inside: avoid; }
            .section-title { font-size: 10px; font-weight: bold; color: #1f2937; margin-bottom: 6px; border-bottom: 1px solid #e5e7eb; padding-bottom: 2px; }
            .info-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; }
            .info-item { margin-bottom: 4px; }
            .info-label { font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px; }
            .info-value { font-size: 8px; color: #1f2937; }
            .badge { display: inline-block; padding: 2px 6px; border-radius: 4px; font-size: 7px; font-weight: 600; }
            .badge-high { background: #fee2e2; color: #dc2626; }
            .badge-medium { background: #fef3c7; color: #d97706; }
            .badge-low { background: #dcfce7; color: #16a34a; }
            .footer { margin-top: 20px; text-align: center; font-size: 7px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 8px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">FINDING DETAIL REPORT</div>
            <div class="subtitle">Generated on ${new Date().toLocaleString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
          </div>

          <div class="section">
            <div class="section-title">Finding Information</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Finding ID</div>
                <div class="info-value">${finding.id}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Location</div>
                <div class="info-value">${finding.location}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Type</div>
                <div class="info-value">${finding.type}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Severity</div>
                <div class="info-value">
                  <span class="badge badge-${finding.severity}">${finding.severity?.charAt(0).toUpperCase() + finding.severity?.slice(1)}</span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">Status</div>
                <div class="info-value">${finding.status}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Reported By</div>
                <div class="info-value">${finding.reportedBy}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Reported Date</div>
                <div class="info-value">${finding.reportedDate}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Impact Level</div>
                <div class="info-value">${finding.impactLevel || 'N/A'}</div>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Description</div>
            <div class="info-value">${finding.description}</div>
          </div>

          ${finding.remark ? `
          <div class="section">
            <div class="section-title">Remarks</div>
            <div class="info-value">${finding.remark}</div>
          </div>
          ` : ''}

          ${photosHtml}

          <div class="footer">
            <div>Patrol Management System - Finding Detail Report</div>
            <div>Report generated for: ${finding.location} (ID: ${finding.id})</div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const handlePrintFindingFallback = (finding: any) => {
    const reportContent = `
FINDING DETAIL REPORT
${'='.repeat(50)}

Generated on: ${new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}

FINDING INFORMATION
${'-'.repeat(30)}
Finding ID: ${finding.id}
Location: ${finding.location}
Type: ${finding.type}
Severity: ${finding.severity}
Status: ${finding.status}
Reported By: ${finding.reportedBy}
Reported Date: ${finding.reportedDate}
Impact Level: ${finding.impactLevel || 'N/A'}

DESCRIPTION
${'-'.repeat(30)}
${finding.description}

${finding.remark ? `REMARKS
${'-'.repeat(30)}
${finding.remark}

` : ''}PHOTOS
${'-'.repeat(30)}
${finding.photos?.length || 0} photo(s) available

${'='.repeat(50)}
Patrol Management System - Finding Detail Report
Report generated for: ${finding.location} (ID: ${finding.id})
    `;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `finding-detail-${finding.id}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handlePrintAsset = (asset: any) => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      handlePrintAssetFallback(asset);
      return;
    }

    const photosHtml = asset.photos && asset.photos.length > 0
      ? `
        <div style="margin-top: 12px; page-break-inside: avoid;">
          <h3 style="color: #1f2937; margin-bottom: 8px; font-size: 12px; font-weight: 600;">Asset Photos</h3>
          <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; max-width: 100%;">
            ${asset.photos.slice(0, 6).map((photo: string, index: number) => `
              <div style="position: relative; border: 2px solid #e5e7eb; border-radius: 6px; overflow: hidden; aspect-ratio: 4/3; width: 100%; max-width: 120px;">
                <img src="${photo}" alt="Photo ${index + 1}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'" />
                <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 4px; background: rgba(0,0,0,0.8); color: white; text-align: center; font-size: 10px; font-weight: 600;">Photo ${index + 1}</div>
              </div>
            `).join('')}
          </div>
          ${asset.photos.length > 6 ? `<div style="font-size: 10px; color: #6b7280; margin-top: 6px; text-align: center; font-style: italic;">+${asset.photos.length - 6} more photos available</div>` : ''}
        </div>
      `
      : '<div style="margin-top: 12px; padding: 12px; background: #f9fafb; border-radius: 6px; text-align: center; color: #6b7280; font-size: 10px;">No photos available</div>';

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Asset Detail Report - ${asset.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 10px; color: #333; }
            .header { text-align: center; margin-bottom: 15px; border-bottom: 1px solid #2563eb; padding-bottom: 8px; }
            .title { font-size: 14px; font-weight: bold; color: #1f2937; margin-bottom: 2px; }
            .subtitle { font-size: 8px; color: #6b7280; }
            .section { margin-bottom: 12px; page-break-inside: avoid; }
            .section-title { font-size: 10px; font-weight: bold; color: #1f2937; margin-bottom: 6px; border-bottom: 1px solid #e5e7eb; padding-bottom: 2px; }
            .info-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; }
            .info-item { margin-bottom: 4px; }
            .info-label { font-size: 7px; color: #6b7280; font-weight: 600; margin-bottom: 1px; }
            .info-value { font-size: 8px; color: #1f2937; }
            .badge { display: inline-block; padding: 2px 6px; border-radius: 4px; font-size: 7px; font-weight: 600; }
            .badge-excellent { background: #dcfce7; color: #16a34a; }
            .badge-good { background: #dbeafe; color: #2563eb; }
            .badge-fair { background: #fef3c7; color: #d97706; }
            .badge-poor { background: #fee2e2; color: #dc2626; }
            .footer { margin-top: 20px; text-align: center; font-size: 7px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 8px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">ASSET DETAIL REPORT</div>
            <div class="subtitle">Generated on ${new Date().toLocaleString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
          </div>

          <div class="section">
            <div class="section-title">Asset Information</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Asset ID</div>
                <div class="info-value">${asset.id}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Name</div>
                <div class="info-value">${asset.name}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Type</div>
                <div class="info-value">${asset.type}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Location</div>
                <div class="info-value">${asset.location}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Route</div>
                <div class="info-value">${asset.route}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Condition</div>
                <div class="info-value">
                  <span class="badge badge-${asset.condition}">${asset.condition?.charAt(0).toUpperCase() + asset.condition?.slice(1)}</span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">Completeness</div>
                <div class="info-value">${asset.completeness}%</div>
              </div>
              <div class="info-item">
                <div class="info-label">Last Inspection</div>
                <div class="info-value">${asset.lastInspection}</div>
              </div>
            </div>
          </div>

          ${asset.notes ? `
          <div class="section">
            <div class="section-title">Notes</div>
            <div class="info-value">${asset.notes}</div>
          </div>
          ` : ''}

          ${asset.maintenanceHistory && asset.maintenanceHistory.length > 0 ? `
          <div class="section">
            <div class="section-title">Maintenance History</div>
            <div class="info-value">
              ${asset.maintenanceHistory.map((maintenance: any) =>
                typeof maintenance === 'string' ? maintenance : maintenance.description
              ).join('<br>')}
            </div>
          </div>
          ` : ''}

          ${asset.changes && asset.changes.length > 0 ? `
          <div class="section">
            <div class="section-title">Recent Changes</div>
            <div class="info-value">
              ${asset.changes.map((change: any) =>
                typeof change === 'string' ? change : change.description
              ).join('<br>')}
            </div>
          </div>
          ` : ''}

          ${photosHtml}

          <div class="footer">
            <div>Patrol Management System - Asset Detail Report</div>
            <div>Report generated for: ${asset.name} (ID: ${asset.id})</div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const handlePrintAssetFallback = (asset: any) => {
    const reportContent = `
ASSET DETAIL REPORT
${'='.repeat(50)}

Generated on: ${new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}

ASSET INFORMATION
${'-'.repeat(30)}
Asset ID: ${asset.id}
Name: ${asset.name}
Type: ${asset.type}
Location: ${asset.location}
Route: ${asset.route}
Condition: ${asset.condition}
Completeness: ${asset.completeness}%
Last Inspection: ${asset.lastInspection}

${asset.notes ? `NOTES
${'-'.repeat(30)}
${asset.notes}

` : ''}${asset.maintenanceHistory && asset.maintenanceHistory.length > 0 ? `MAINTENANCE HISTORY
${'-'.repeat(30)}
${asset.maintenanceHistory.map((maintenance: any) =>
  typeof maintenance === 'string' ? maintenance : maintenance.description
).join('\n')}

` : ''}${asset.changes && asset.changes.length > 0 ? `RECENT CHANGES
${'-'.repeat(30)}
${asset.changes.map((change: any) =>
  typeof change === 'string' ? change : change.description
).join('\n')}

` : ''}PHOTOS
${'-'.repeat(30)}
${asset.photos?.length || 0} photo(s) available

${'='.repeat(50)}
Patrol Management System - Asset Detail Report
Report generated for: ${asset.name} (ID: ${asset.id})
    `;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `asset-detail-${asset.id}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Export functions
  const handleExportXLS = () => {
    // For XLS export, we'll create a CSV with tab separators that Excel can open
    let csvContent = '';
    let filename = '';

    switch (exportType) {
      case 'findings':
        csvContent = generateFindingsXLS();
        filename = `patrol-findings-${new Date().toISOString().split('T')[0]}.xls`;
        break;
      case 'assets':
        csvContent = generateAssetsXLS();
        filename = `patrol-assets-${new Date().toISOString().split('T')[0]}.xls`;
        break;
      case 'otdr':
        csvContent = generateOTDRXLS();
        filename = `patrol-otdr-${new Date().toISOString().split('T')[0]}.xls`;
        break;
      case 'all':
      default:
        csvContent = generateAllDataXLS();
        filename = `patrol-data-${new Date().toISOString().split('T')[0]}.xls`;
        break;
    }

    const blob = new Blob([csvContent], { type: 'application/vnd.ms-excel' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setShowExportDialog(false);
  };

  const handleExportCSV = () => {
    let csvContent = '';
    let filename = '';

    switch (exportType) {
      case 'findings':
        csvContent = generateFindingsCSV();
        filename = `patrol-findings-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'assets':
        csvContent = generateAssetsCSV();
        filename = `patrol-assets-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'otdr':
        csvContent = generateOTDRCSV();
        filename = `patrol-otdr-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'all':
      default:
        csvContent = generateAllDataCSV();
        filename = `patrol-data-${new Date().toISOString().split('T')[0]}.csv`;
        break;
    }

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setShowExportDialog(false);
  };

  const generateFindingsCSV = () => {
    const headers = ['ID', 'Type', 'Location', 'Description', 'Severity', 'Status', 'Reported By', 'Reported Date', 'Route', 'Impact Level', 'Affected Assets', 'Remark'];
    const rows = findings.map(finding => [
      finding.id,
      finding.type,
      finding.location,
      finding.description,
      finding.severity,
      finding.status,
      finding.reportedBy,
      finding.reportedDate,
      finding.route,
      finding.impactLevel || '',
      finding.affectedAssets?.join('; ') || '',
      finding.remark || ''
    ]);
    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  };

  const generateAssetsCSV = () => {
    const headers = ['ID', 'Name', 'Type', 'Location', 'Condition', 'Completeness', 'Route', 'Last Inspection', 'Notes'];
    const rows = assets.map(asset => [
      asset.id,
      asset.name,
      asset.type,
      asset.location,
      asset.condition,
      asset.completeness,
      asset.route,
      asset.lastInspection,
      asset.notes || ''
    ]);
    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  };

  const generateOTDRCSV = () => {
    const headers = ['ID', 'Route', 'Core', 'Distance', 'Loss', 'Reflectance', 'Technician', 'Date', 'Traffic', 'Notes'];
    const rows = otdrMeasurements.map(otdr => [
      otdr.id,
      otdr.route,
      otdr.core,
      otdr.distance,
      otdr.loss,
      otdr.reflectance,
      otdr.technician,
      otdr.date,
      otdr.traffic || '',
      otdr.notes || ''
    ]);
    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  };

  const generateAllDataCSV = () => {
    let csvContent = 'FINDINGS\n';
    csvContent += generateFindingsCSV();
    csvContent += '\n\nASSETS\n';
    csvContent += generateAssetsCSV();
    csvContent += '\n\nOTDR MEASUREMENTS\n';
    csvContent += generateOTDRCSV();
    return csvContent;
  };

  // XLS generation functions (using tab-separated values for Excel compatibility)
  const generateFindingsXLS = () => {
    const headers = ['ID', 'Type', 'Location', 'Description', 'Severity', 'Status', 'Reported By', 'Reported Date', 'Route', 'Impact Level', 'Affected Assets', 'Remark'];
    const rows = findings.map(finding => [
      finding.id,
      finding.type,
      finding.location,
      finding.description,
      finding.severity,
      finding.status,
      finding.reportedBy,
      finding.reportedDate,
      finding.route,
      finding.impactLevel || '',
      finding.affectedAssets?.join('; ') || '',
      finding.remark || ''
    ]);
    return [headers, ...rows].map(row => row.join('\t')).join('\n');
  };

  const generateAssetsXLS = () => {
    const headers = ['ID', 'Name', 'Type', 'Location', 'Condition', 'Completeness', 'Route', 'Last Inspection', 'Notes'];
    const rows = assets.map(asset => [
      asset.id,
      asset.name,
      asset.type,
      asset.location,
      asset.condition,
      asset.completeness,
      asset.route,
      asset.lastInspection,
      asset.notes || ''
    ]);
    return [headers, ...rows].map(row => row.join('\t')).join('\n');
  };

  const generateOTDRXLS = () => {
    const headers = ['ID', 'Route', 'Core', 'Distance', 'Loss', 'Reflectance', 'Technician', 'Date', 'Traffic', 'Notes'];
    const rows = otdrMeasurements.map(otdr => [
      otdr.id,
      otdr.route,
      otdr.core,
      otdr.distance,
      otdr.loss,
      otdr.reflectance,
      otdr.technician,
      otdr.measurementDate,
      otdr.traffic || '',
      otdr.notes || ''
    ]);
    return [headers, ...rows].map(row => row.join('\t')).join('\n');
  };

  const generateAllDataXLS = () => {
    let xlsContent = 'FINDINGS\n';
    xlsContent += generateFindingsXLS();
    xlsContent += '\n\nASSETS\n';
    xlsContent += generateAssetsXLS();
    xlsContent += '\n\nOTDR MEASUREMENTS\n';
    xlsContent += generateOTDRXLS();
    return xlsContent;
  };

  // PDF export function
  const handleExportPDF = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow popups to export PDF');
      return;
    }

    let contentHtml = '';
    let title = '';

    switch (exportType) {
      case 'findings':
        contentHtml = generateFindingsPDF();
        title = 'Patrol Findings Report';
        break;
      case 'assets':
        contentHtml = generateAssetsPDF();
        title = 'Patrol Assets Report';
        break;
      case 'otdr':
        contentHtml = generateOTDRPDF();
        title = 'OTDR Measurements Report';
        break;
      case 'all':
      default:
        contentHtml = generateAllDataPDF();
        title = 'Complete Patrol Report';
        break;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3b82f6; padding-bottom: 15px; }
            .title { font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 5px; }
            .subtitle { font-size: 14px; color: #6b7280; }
            .section { margin-bottom: 30px; page-break-inside: avoid; }
            .section-title { font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 15px; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px; }
            .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .table th, .table td { border: 1px solid #e5e7eb; padding: 8px; text-align: left; font-size: 12px; }
            .table th { background-color: #f9fafb; font-weight: 600; }
            .table tr:nth-child(even) { background-color: #f9fafb; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 10px; font-weight: 600; }
            .badge-high { background: #fee2e2; color: #dc2626; }
            .badge-medium { background: #fef3c7; color: #d97706; }
            .badge-low { background: #dcfce7; color: #16a34a; }
            .badge-critical { background: #fecaca; color: #b91c1c; }
            .badge-excellent { background: #dcfce7; color: #16a34a; }
            .badge-good { background: #dbeafe; color: #2563eb; }
            .badge-fair { background: #fef3c7; color: #d97706; }
            .badge-poor { background: #fee2e2; color: #dc2626; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 15px; }
            @media print { .page-break { page-break-before: always; } }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">${title}</div>
            <div class="subtitle">Generated on ${new Date().toLocaleString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
          </div>
          ${contentHtml}
          <div class="footer">
            <div>Patrol Management System - ${title}</div>
            <div>Total Records: ${getTotalRecords()}</div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);

    setShowExportDialog(false);
  };

  const generateFindingsPDF = () => {
    return `
      <div class="section">
        <div class="section-title">Findings Report (${findings.length} records)</div>
        <table class="table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Type</th>
              <th>Location</th>
              <th>Description</th>
              <th>Severity</th>
              <th>Status</th>
              <th>Reported By</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            ${findings.map(finding => `
              <tr>
                <td>${finding.id}</td>
                <td>${finding.type}</td>
                <td>${finding.location}</td>
                <td>${finding.description}</td>
                <td><span class="badge badge-${finding.severity}">${finding.severity}</span></td>
                <td>${finding.status}</td>
                <td>${finding.reportedBy}</td>
                <td>${finding.reportedDate}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  };

  const generateAssetsPDF = () => {
    return `
      <div class="section">
        <div class="section-title">Assets Report (${assets.length} records)</div>
        <table class="table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Type</th>
              <th>Location</th>
              <th>Condition</th>
              <th>Completeness</th>
              <th>Route</th>
              <th>Last Inspection</th>
            </tr>
          </thead>
          <tbody>
            ${assets.map(asset => `
              <tr>
                <td>${asset.id}</td>
                <td>${asset.name}</td>
                <td>${asset.type}</td>
                <td>${asset.location}</td>
                <td><span class="badge badge-${asset.condition}">${asset.condition}</span></td>
                <td>${asset.completeness}%</td>
                <td>${asset.route}</td>
                <td>${asset.lastInspection}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  };

  const generateOTDRPDF = () => {
    return `
      <div class="section">
        <div class="section-title">OTDR Measurements Report (${otdrMeasurements.length} records)</div>
        <table class="table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Route</th>
              <th>Core</th>
              <th>Distance</th>
              <th>Loss</th>
              <th>Reflectance</th>
              <th>Technician</th>
              <th>Date</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            ${otdrMeasurements.map(otdr => `
              <tr>
                <td>${otdr.id}</td>
                <td>${otdr.route}</td>
                <td>${otdr.core}</td>
                <td>${otdr.distance}</td>
                <td>${otdr.loss}</td>
                <td>${otdr.reflectance}</td>
                <td>${otdr.technician}</td>
                <td>${otdr.measurementDate}</td>
                <td>${otdr.status}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  };

  const generateAllDataPDF = () => {
    return `
      ${generateFindingsPDF()}
      <div class="page-break"></div>
      ${generateAssetsPDF()}
      <div class="page-break"></div>
      ${generateOTDRPDF()}
    `;
  };

  const getTotalRecords = () => {
    switch (exportType) {
      case 'findings': return findings.length;
      case 'assets': return assets.length;
      case 'otdr': return otdrMeasurements.length;
      case 'all':
      default: return findings.length + assets.length + otdrMeasurements.length;
    }
  };

  // Import functions
  const handleImportData = async () => {
    if (!importFile) return;

    try {
      const text = await importFile.text();
      const lines = text.split('\n').filter(line => line.trim());

      if (lines.length < 2) {
        alert('CSV file must have at least a header row and one data row.');
        return;
      }

      // Detect data type based on headers
      const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

      if (headers.includes('Severity') && headers.includes('Reported By')) {
        // Import findings
        importFindingsFromCSV(lines);
      } else if (headers.includes('Condition') && headers.includes('Completeness')) {
        // Import assets
        importAssetsFromCSV(lines);
      } else if (headers.includes('Loss') && headers.includes('Reflectance')) {
        // Import OTDR
        importOTDRFromCSV(lines);
      } else {
        alert('CSV format not recognized. Please use the exported CSV format.');
        return;
      }

      alert('Data imported successfully!');
      setShowImportDialog(false);
      setImportFile(null);
    } catch (error) {
      console.error('Error importing data:', error);
      alert('Error importing data. Please check the file format.');
    }
  };

  const importFindingsFromCSV = (lines: string[]) => {
    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());

      if (values.length >= headers.length) {
        const finding = {
          id: values[headers.indexOf('ID')] || `F${Date.now()}-${i}`,
          type: values[headers.indexOf('Type')] || 'network_threat',
          location: values[headers.indexOf('Location')] || '',
          description: values[headers.indexOf('Description')] || '',
          severity: values[headers.indexOf('Severity')] || 'medium',
          status: 'open' as const,
          reportedBy: values[headers.indexOf('Reported By')] || 'Imported',
          reportedDate: values[headers.indexOf('Reported Date')] || new Date().toISOString().split('T')[0],
          route: values[headers.indexOf('Route')] || '',
          impactLevel: values[headers.indexOf('Impact Level')] || 'low',
          affectedAssets: values[headers.indexOf('Affected Assets')]?.split(';').map(s => s.trim()) || [],
          remark: values[headers.indexOf('Remark')] || '',
          photos: []
        };

        try {
          addFinding(finding);
        } catch (error) {
          console.error('Error adding finding:', error);
        }
      }
    }
  };

  const importAssetsFromCSV = (lines: string[]) => {
    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());

      if (values.length >= headers.length) {
        const asset = {
          id: values[headers.indexOf('ID')] || `A${Date.now()}-${i}`,
          name: values[headers.indexOf('Name')] || '',
          type: values[headers.indexOf('Type')] || 'equipment',
          location: values[headers.indexOf('Location')] || '',
          condition: values[headers.indexOf('Condition')] || 'good',
          completeness: values[headers.indexOf('Completeness')]?.replace('%', '') || '100',
          route: values[headers.indexOf('Route')] || '',
          lastInspection: values[headers.indexOf('Last Inspection')] || new Date().toISOString().split('T')[0],
          notes: values[headers.indexOf('Notes')] || '',
          changes: [],
          maintenanceHistory: [],
          photos: []
        };

        try {
          addAsset(asset);
        } catch (error) {
          console.error('Error adding asset:', error);
        }
      }
    }
  };

  const importOTDRFromCSV = (lines: string[]) => {
    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());

      if (values.length >= headers.length) {
        const otdr = {
          id: values[headers.indexOf('ID')] || `O${Date.now()}-${i}`,
          route: values[headers.indexOf('Route')] || '',
          core: values[headers.indexOf('Core')] || '',
          distance: values[headers.indexOf('Distance')] || '',
          loss: values[headers.indexOf('Loss')] || '',
          reflectance: values[headers.indexOf('Reflectance')] || '',
          technician: values[headers.indexOf('Technician')] || 'Imported',
          measurementDate: values[headers.indexOf('Date')] || new Date().toISOString().split('T')[0],
          status: 'pass' as const,
          traffic: values[headers.indexOf('Traffic')] || '',
          notes: values[headers.indexOf('Notes')] || '',
          photos: []
        };

        try {
          addOTDRMeasurement(otdr);
        } catch (error) {
          console.error('Error adding OTDR measurement:', error);
        }
      }
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
    }
  };

  const resetForm = () => {
    setFormData({
      type: '',
      severity: '',
      location: '',
      description: '',
      reportedBy: '',
      impactLevel: '',
      affectedAssets: '',
      remark: '',
      findingLength: '',
      thirdPartyContact: '',
      // Asset fields
      name: '',
      assetType: '',
      condition: '',
      completeness: '',
      maintenanceHistory: '',
      notes: '',
      // OTDR fields
      route: '',
      core: '',
      distance: '',
      loss: '',
      reflectance: '',
      technician: '',
      traffic: ''
    });
    // Reset photos
    setFindingPhotos([]);
    setAssetPhotos([]);
    setOtdrPhotos([]);
  };

  const handleAddReport = async () => {
    if (activeTab === 'findings') {
      const photos = await convertFilesToBase64(findingPhotos);
      const newFinding = {
        id: Date.now().toString(),
        type: formData.type,
        location: formData.location,
        description: formData.description,
        severity: formData.severity as 'low' | 'medium' | 'high' | 'critical',
        status: 'open' as const,
        reportedBy: formData.reportedBy,
        reportedDate: new Date().toISOString().split('T')[0],
        route: formData.route,
        impactLevel: formData.impactLevel as 'low' | 'medium' | 'high',
        affectedAssets: formData.affectedAssets ? formData.affectedAssets.split(',').map(s => s.trim()) : [],
        remark: formData.remark,
        findingLength: formData.findingLength,
        thirdPartyContact: formData.thirdPartyContact,
        photos: photos
      };
      addFinding(newFinding);
    } else if (activeTab === 'assets') {
      const photos = await convertFilesToBase64(assetPhotos);
      const newAsset = {
        id: Date.now().toString(),
        name: formData.name,
        type: formData.assetType,
        location: formData.location,
        condition: formData.condition as 'excellent' | 'good' | 'fair' | 'poor' | 'critical',
        completeness: formData.completeness,
        lastInspection: new Date().toISOString().split('T')[0],
        route: formData.route,
        notes: formData.notes,
        changes: [],
        maintenanceHistory: [],
        photos: photos
      };
      addAsset(newAsset);
    } else if (activeTab === 'otdr') {
      const photos = await convertFilesToBase64(otdrPhotos);
      const newOTDR = {
        id: Date.now().toString(),
        route: formData.route,
        core: formData.core,
        distance: formData.distance,
        loss: formData.loss,
        reflectance: formData.reflectance,
        status: 'pass' as const,
        measurementDate: new Date().toISOString().split('T')[0],
        technician: formData.technician,
        notes: formData.notes,
        traffic: formData.traffic,
        photos: photos
      };
      addOTDRMeasurement(newOTDR);
    }
    resetForm();
    setShowAddDialog(false);
  };

  const handleEdit = async (item: any, type: string) => {
    setEditingItem({ ...item, type });
    setFormData({
      type: item.type || '',
      location: item.location || '',
      description: item.description || '',
      severity: item.severity || 'medium',
      route: item.route || '',
      reportedBy: item.reportedBy || '',
      impactLevel: item.impactLevel || 'low',
      affectedAssets: item.affectedAssets ? item.affectedAssets.join(', ') : '',
      remark: item.remark || '',
      findingLength: item.findingLength || '',
      thirdPartyContact: item.thirdPartyContact || '',
      name: item.name || '',
      assetType: item.type || '',
      condition: item.condition || 'good',
      completeness: item.completeness || 100,
      maintenanceHistory: '',
      notes: item.notes || '',
      core: item.core || '',
      distance: item.distance || '',
      loss: item.loss || '',
      reflectance: item.reflectance || '',
      technician: item.technician || '',
      traffic: item.traffic || ''
    });

    // Load existing photos
    if (item.photos && item.photos.length > 0) {
      try {
        const files = await convertBase64ToFiles(item.photos);
        if (type === 'finding') {
          setFindingPhotos(files);
        } else if (type === 'asset') {
          setAssetPhotos(files);
        } else if (type === 'otdr') {
          setOtdrPhotos(files);
        }
      } catch (error) {
        console.error('Error loading photos for edit:', error);
      }
    } else {
      // Clear photos if no existing photos
      setFindingPhotos([]);
      setAssetPhotos([]);
      setOtdrPhotos([]);
    }

    setShowEditDialog(true);
  };

  const handleUpdate = async () => {
    if (editingItem.type === 'finding') {
      const photos = await convertFilesToBase64(findingPhotos);
      const updatedFinding = {
        ...editingItem,
        type: formData.type,
        location: formData.location,
        description: formData.description,
        severity: formData.severity,
        route: formData.route,
        reportedBy: formData.reportedBy,
        impactLevel: formData.impactLevel,
        affectedAssets: formData.affectedAssets ? formData.affectedAssets.split(',').map(s => s.trim()) : [],
        remark: formData.remark,
        findingLength: formData.findingLength,
        thirdPartyContact: formData.thirdPartyContact,
        photos: photos
      };
      updateFinding(editingItem.id, updatedFinding);
    } else if (editingItem.type === 'asset') {
      const photos = await convertFilesToBase64(assetPhotos);
      const updatedAsset = {
        ...editingItem,
        name: formData.name,
        type: formData.assetType,
        location: formData.location,
        condition: formData.condition,
        completeness: formData.completeness,
        route: formData.route,
        notes: formData.notes,
        photos: photos
      };
      updateAsset(editingItem.id, updatedAsset);
    } else if (editingItem.type === 'otdr') {
      const photos = await convertFilesToBase64(otdrPhotos);
      const updatedOTDR = {
        ...editingItem,
        route: formData.route,
        core: formData.core,
        distance: formData.distance,
        loss: formData.loss,
        reflectance: formData.reflectance,
        technician: formData.technician,
        notes: formData.notes,
        traffic: formData.traffic,
        photos: photos
      };
      updateOTDRMeasurement(editingItem.id, updatedOTDR);
    }
    resetForm();
    setShowEditDialog(false);
    setEditingItem(null);
  };

  const handleDelete = (id: string, type: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      if (type === 'finding') {
        deleteFinding(id);
      } else if (type === 'asset') {
        deleteAsset(id);
      } else if (type === 'otdr') {
        deleteOTDRMeasurement(id);
      }
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Patrol Management</h1>
          <div className="flex gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
            <span>Critical Findings: {getCriticalFindings().length}</span>
            <span>Failed OTDR: {getFailedOTDRMeasurements().length}</span>
            <span>Poor Assets: {assets.filter(a => a.condition === 'poor' || a.condition === 'critical').length}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowImportDialog(true)}
          >
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExportDialog(true)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Link to="/patrol/sor">
            <Button variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              SOR OTDR
            </Button>
          </Link>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button size="sm" onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Report
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="findings">Findings Monitoring</TabsTrigger>
        <TabsTrigger value="assets">Asset Monitoring</TabsTrigger>
        <TabsTrigger value="otdr">OTDR Measurement</TabsTrigger>
        </TabsList>

        {/* Monitoring Temuan */}
        <TabsContent value="findings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Findings Monitoring
              </CardTitle>
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search findings..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredFindings.map((finding) => (
                  <div key={finding.id} className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer" onClick={() => handleFindingClick(finding)}>
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded-full ${getSeverityColor(finding.severity)} text-white`}>
                          {getTypeIcon(finding.type)}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{finding.location}</h3>
                          <p className="text-gray-600 dark:text-gray-400 mt-1">{finding.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                            <span>Reported by: {finding.reportedBy}</span>
          <span>Date: {finding.reportedDate}</span>
                            {finding.route && <span>Route: {finding.route}</span>}
                            {finding.impactLevel && <span>Impact: {finding.impactLevel}</span>}
                          </div>
                          {finding.affectedAssets && finding.affectedAssets.length > 0 && (
                            <div className="mt-2 text-sm text-orange-600 dark:text-orange-400">
                              Affected Assets: {finding.affectedAssets.join(', ')}
                            </div>
                          )}

                          {/* Photos Section */}
                          {finding.photos && finding.photos.length > 0 && (
                            <div className="mt-3">
                              <div className="flex items-center gap-2 mb-2">
                                <Camera className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-500">
                                  {finding.photos.length} photo{finding.photos.length > 1 ? 's' : ''}
                                </span>
                              </div>
                              <div className="flex gap-2 overflow-x-auto">
                                {finding.photos.slice(0, 3).map((photo: string, index: number) => (
                                  <div key={index} className="relative flex-shrink-0">
                                    <img
                                      src={photo}
                                      alt={`Finding photo ${index + 1}`}
                                      className="w-16 h-16 object-cover rounded-lg border cursor-pointer hover:opacity-80"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedPhotos(finding.photos || []);
                                        setIsPhotoGalleryOpen(true);
                                      }}
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                      }}
                                    />
                                    <div className="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                                      {index + 1}
                                    </div>
                                  </div>
                                ))}
                                {finding.photos.length > 3 && (
                                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg border flex items-center justify-center cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedPhotos(finding.photos || []);
                                      setIsPhotoGalleryOpen(true);
                                    }}
                                  >
                                    <span className="text-xs text-gray-600 dark:text-gray-400">
                                      +{finding.photos.length - 3}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePrintFinding(finding);
                          }}
                          className="text-xs"
                        >
                          <Printer className="h-3 w-3 mr-1" />
                          Print
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCreateTroubleTicket(finding.id);
                          }}
                          className="text-xs"
                        >
                          <Ticket className="h-3 w-3 mr-1" />
                          Maintenance Timeline
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(finding, 'finding');
                          }}
                          className="text-xs"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(finding.id, 'finding');
                          }}
                          className="text-xs"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                        <Badge className={getStatusColor(finding.status)}>
                          {finding.status}
                        </Badge>
                        <Badge variant="outline">
                          {finding.severity}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Monitoring Asset */}
        <TabsContent value="assets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Asset Monitoring
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {assets.map((asset) => (
                  <Card key={asset.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleAssetClick(asset)}>
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{asset.name}</CardTitle>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{asset.location}</p>
                        </div>
                        <Badge variant="outline">{asset.type.toUpperCase()}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Condition:</span>
                        <Badge className={`${getConditionColor(asset.condition)} text-white`}>
                          {asset.condition}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Completeness:</span>
                        <span className="text-sm font-semibold">{asset.completeness}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${asset.completeness}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-500">
                        <p>Last Inspection: {asset.lastInspection}</p>
                        <p>Route: {asset.route}</p>
                      </div>
                      {asset.notes && (
                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                          Notes: {asset.notes}
                        </div>
                      )}
                      {asset.maintenanceHistory && asset.maintenanceHistory.length > 0 && (
                        <div className="mt-2 text-sm text-blue-600 dark:text-blue-400">
                          Last Maintenance: {asset.maintenanceHistory[asset.maintenanceHistory.length - 1].date}
                        </div>
                      )}
                      {asset.changes.length > 0 && (
                        <div className="mt-3">
                          <p className="text-sm font-medium mb-1">Recent Changes:</p>
                          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            {asset.changes.map((change, index) => (
                              <li key={index} className="flex items-start gap-1">
                                <span className="text-blue-500">•</span>
                                {change}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Photos Section */}
                      {asset.photos && asset.photos.length > 0 && (
                        <div className="mt-3">
                          <div className="flex items-center gap-2 mb-2">
                            <Camera className="h-4 w-4 text-gray-500" />
                            <span className="text-sm text-gray-500">
                              {asset.photos.length} photo{asset.photos.length > 1 ? 's' : ''}
                            </span>
                          </div>
                          <div className="flex gap-2 overflow-x-auto">
                            {asset.photos.slice(0, 3).map((photo: string, index: number) => (
                              <div key={index} className="relative flex-shrink-0">
                                <img
                                  src={photo}
                                  alt={`Asset photo ${index + 1}`}
                                  className="w-16 h-16 object-cover rounded-lg border cursor-pointer hover:opacity-80"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedPhotos(asset.photos || []);
                                    setIsPhotoGalleryOpen(true);
                                  }}
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                  }}
                                />
                                <div className="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                                  {index + 1}
                                </div>
                              </div>
                            ))}
                            {asset.photos.length > 3 && (
                              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg border flex items-center justify-center cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedPhotos(asset.photos || []);
                                  setIsPhotoGalleryOpen(true);
                                }}
                              >
                                <span className="text-xs text-gray-600 dark:text-gray-400">
                                  +{asset.photos.length - 3}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      <div className="flex gap-2 mt-3">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePrintAsset(asset);
                          }}
                        >
                          <Printer className="h-3 w-3 mr-1" />
                          Print
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(asset, 'asset');
                          }}
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          className="text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(asset.id, 'asset');
                          }}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pengukuran OTDR */}
        <TabsContent value="otdr" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                OTDR Measurement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3 font-semibold">Route</th>
                      <th className="text-left p-3 font-semibold">Core</th>
                      <th className="text-left p-3 font-semibold">Distance</th>
                      <th className="text-left p-3 font-semibold">Loss</th>
                      <th className="text-left p-3 font-semibold">Reflectance</th>
                      <th className="text-left p-3 font-semibold">Date</th>
                      <th className="text-left p-3 font-semibold">Technician</th>
                      <th className="text-left p-3 font-semibold">Traffic</th>
                      <th className="text-left p-3 font-semibold">Status</th>
                      <th className="text-left p-3 font-semibold">Notes</th>
                      <th className="text-left p-3 font-semibold">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {otdrMeasurements.map((measurement) => (
                      <tr key={measurement.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="p-3 font-medium">{measurement.route}</td>
                        <td className="p-3">{measurement.core}</td>
                        <td className="p-3">{measurement.distance}</td>
                        <td className="p-3">{measurement.loss}</td>
                        <td className="p-3">{measurement.reflectance}</td>
                        <td className="p-3">{measurement.measurementDate}</td>
                        <td className="p-3">{measurement.technician}</td>
                        <td className="p-3">{measurement.traffic || '-'}</td>
                        <td className="p-3">
                          <Badge className={getStatusColor(measurement.status)}>
                            {measurement.status}
                          </Badge>
                        </td>
                        <td className="p-3 text-gray-600 dark:text-gray-400">
                          {measurement.notes || '-'}
                        </td>
                        <td className="p-3">
                          <div className="flex gap-1">
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="text-xs"
                              onClick={() => handleEdit(measurement, 'otdr')}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="destructive" 
                              className="text-xs"
                              onClick={() => handleDelete(measurement.id, 'otdr')}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Report Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Report {activeTab === 'findings' ? 'Finding' : activeTab === 'assets' ? 'Asset' : 'OTDR'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {activeTab === 'findings' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="type">Finding Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cable_expose">Exposed Cable</SelectItem>
                        <SelectItem value="hdpe_expose">Exposed HDPE</SelectItem>
                        <SelectItem value="third_party_work">Third Party Work</SelectItem>
                        <SelectItem value="network_threat">Network Threat</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="severity">Severity Level</Label>
                    <Select value={formData.severity} onValueChange={(value) => setFormData({...formData, severity: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    value={formData.description} 
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Enter finding description"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="reportedBy">Reported By</Label>
                    <Input 
                      value={formData.reportedBy} 
                      onChange={(e) => setFormData({...formData, reportedBy: e.target.value})}
                      placeholder="Reporter name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="impactLevel">Impact Level</Label>
                    <Select value={formData.impactLevel} onValueChange={(value) => setFormData({...formData, impactLevel: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="affectedAssets">Affected Assets (separate with comma)</Label>
                  <Input 
                    value={formData.affectedAssets} 
                    onChange={(e) => setFormData({...formData, affectedAssets: e.target.value})}
                    placeholder="Asset1, Asset2, Asset3"
                  />
                </div>
                <div>
                  <Label htmlFor="remark">Remark</Label>
                  <Textarea 
                    value={formData.remark} 
                    onChange={(e) => setFormData({...formData, remark: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="findingLength">Finding Length</Label>
                    <Input 
                      value={formData.findingLength} 
                      onChange={(e) => setFormData({...formData, findingLength: e.target.value})}
                      placeholder="Example: 2.5 meter"
                    />
                  </div>
                  <div>
                    <Label htmlFor="thirdPartyContact">Third Party Contact</Label>
                    <Input 
                      value={formData.thirdPartyContact} 
                      onChange={(e) => setFormData({...formData, thirdPartyContact: e.target.value})}
                      placeholder="Company name - phone number"
                    />
                  </div>
                </div>
                
                {/* Photo Upload for Findings */}
                <PhotoUpload
                  label="Finding Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={findingPhotos}
                  onPhotosChange={setFindingPhotos}
                />
              </>
            )}

            {activeTab === 'assets' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Asset Name</Label>
                    <Input 
                      value={formData.name} 
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="Enter asset name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="assetType">Asset Type</Label>
                    <Select value={formData.assetType} onValueChange={(value) => setFormData({...formData, assetType: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="closure">Closure</SelectItem>
                        <SelectItem value="manhole">Manhole</SelectItem>
                        <SelectItem value="pole">Pole</SelectItem>
                        <SelectItem value="cabinet">Cabinet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="condition">Condition</Label>
                    <Select value={formData.condition} onValueChange={(value) => setFormData({...formData, condition: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                        <SelectItem value="poor">Poor</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="completeness">Completeness (%)</Label>
                    <Input 
                      type="number" 
                      min="0" 
                      max="100"
                      value={formData.completeness} 
                      onChange={(e) => setFormData({...formData, completeness: parseInt(e.target.value) || 0})}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                
                {/* Photo Upload for Assets */}
                <PhotoUpload
                  label="Asset Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={assetPhotos}
                  onPhotosChange={setAssetPhotos}
                />
              </>
            )}

            {activeTab === 'otdr' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                  <div>
                    <Label htmlFor="core">Core</Label>
                    <Input 
                      value={formData.core} 
                      onChange={(e) => setFormData({...formData, core: e.target.value})}
                      placeholder="Enter core"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="distance">Distance</Label>
                    <Input 
                      value={formData.distance} 
                      onChange={(e) => setFormData({...formData, distance: e.target.value})}
                      placeholder="e.g., 15.2 km"
                    />
                  </div>
                  <div>
                    <Label htmlFor="loss">Loss</Label>
                    <Input 
                      value={formData.loss} 
                      onChange={(e) => setFormData({...formData, loss: e.target.value})}
                      placeholder="e.g., 0.25 dB"
                    />
                  </div>
                  <div>
                    <Label htmlFor="reflectance">Reflectance</Label>
                    <Input 
                      value={formData.reflectance} 
                      onChange={(e) => setFormData({...formData, reflectance: e.target.value})}
                      placeholder="e.g., -45 dB"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="technician">Technician</Label>
                    <Input 
                      value={formData.technician} 
                      onChange={(e) => setFormData({...formData, technician: e.target.value})}
                      placeholder="Technician name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="traffic">Traffic</Label>
                    <Input 
                      value={formData.traffic} 
                      onChange={(e) => setFormData({...formData, traffic: e.target.value})}
                      placeholder="Example: High Traffic - 80% utilization"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                
                {/* Photo Upload for OTDR */}
                <PhotoUpload
                  label="OTDR Measurement Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={otdrPhotos}
                  onPhotosChange={setOtdrPhotos}
                />
              </>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => { setShowAddDialog(false); resetForm(); }}>Cancel</Button>
              <Button onClick={handleAddReport}>Save</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit {editingItem?.type === 'finding' ? 'Finding' : editingItem?.type === 'asset' ? 'Asset' : 'OTDR'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {editingItem?.type === 'finding' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="type">Finding Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cable_expose">Exposed Cable</SelectItem>
                        <SelectItem value="hdpe_expose">Exposed HDPE</SelectItem>
                        <SelectItem value="third_party_work">Third Party Work</SelectItem>
                        <SelectItem value="network_threat">Network Threat</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="severity">Severity Level</Label>
                    <Select value={formData.severity} onValueChange={(value) => setFormData({...formData, severity: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    value={formData.description} 
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Enter finding description"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="reportedBy">Reported By</Label>
                    <Input 
                      value={formData.reportedBy} 
                      onChange={(e) => setFormData({...formData, reportedBy: e.target.value})}
                      placeholder="Reporter name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="impactLevel">Impact Level</Label>
                    <Select value={formData.impactLevel} onValueChange={(value) => setFormData({...formData, impactLevel: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="affectedAssets">Affected Assets (separate with comma)</Label>
                  <Input 
                    value={formData.affectedAssets} 
                    onChange={(e) => setFormData({...formData, affectedAssets: e.target.value})}
                    placeholder="Asset1, Asset2, Asset3"
                  />
                </div>
                <div>
                  <Label htmlFor="remark">Remark</Label>
                  <Textarea 
                    value={formData.remark} 
                    onChange={(e) => setFormData({...formData, remark: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="findingLength">Finding Length</Label>
                    <Input 
                      value={formData.findingLength} 
                      onChange={(e) => setFormData({...formData, findingLength: e.target.value})}
                      placeholder="Example: 2.5 meter"
                    />
                  </div>
                  <div>
                    <Label htmlFor="thirdPartyContact">Third Party Contact</Label>
                    <Input 
                      value={formData.thirdPartyContact} 
                      onChange={(e) => setFormData({...formData, thirdPartyContact: e.target.value})}
                      placeholder="Company name - phone number"
                    />
                  </div>
                </div>

                {/* Photo Upload for Finding Edit */}
                <PhotoUpload
                  label="Finding Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={findingPhotos}
                  onPhotosChange={setFindingPhotos}
                />
              </>
            )}

            {editingItem?.type === 'asset' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Asset Name</Label>
                    <Input 
                      value={formData.name} 
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="Enter asset name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="assetType">Asset Type</Label>
                    <Select value={formData.assetType} onValueChange={(value) => setFormData({...formData, assetType: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="closure">Closure</SelectItem>
                        <SelectItem value="manhole">Manhole</SelectItem>
                        <SelectItem value="pole">Pole</SelectItem>
                        <SelectItem value="cabinet">Cabinet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input 
                      value={formData.location} 
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="condition">Condition</Label>
                    <Select value={formData.condition} onValueChange={(value) => setFormData({...formData, condition: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                        <SelectItem value="poor">Poor</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="completeness">Completeness (%)</Label>
                    <Input 
                      type="number" 
                      min="0" 
                      max="100"
                      value={formData.completeness} 
                      onChange={(e) => setFormData({...formData, completeness: parseInt(e.target.value) || 0})}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>

                {/* Photo Upload for Asset Edit */}
                <PhotoUpload
                  label="Asset Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={assetPhotos}
                  onPhotosChange={setAssetPhotos}
                />
              </>
            )}

            {editingItem?.type === 'otdr' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="route">Route</Label>
                    <Input 
                      value={formData.route} 
                      onChange={(e) => setFormData({...formData, route: e.target.value})}
                      placeholder="Enter route"
                    />
                  </div>
                  <div>
                    <Label htmlFor="core">Core</Label>
                    <Input 
                      value={formData.core} 
                      onChange={(e) => setFormData({...formData, core: e.target.value})}
                      placeholder="Enter core"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="distance">Distance</Label>
                    <Input 
                      value={formData.distance} 
                      onChange={(e) => setFormData({...formData, distance: e.target.value})}
                      placeholder="e.g., 15.2 km"
                    />
                  </div>
                  <div>
                    <Label htmlFor="loss">Loss</Label>
                    <Input 
                      value={formData.loss} 
                      onChange={(e) => setFormData({...formData, loss: e.target.value})}
                      placeholder="e.g., 0.25 dB"
                    />
                  </div>
                  <div>
                    <Label htmlFor="reflectance">Reflectance</Label>
                    <Input 
                      value={formData.reflectance} 
                      onChange={(e) => setFormData({...formData, reflectance: e.target.value})}
                      placeholder="e.g., -45 dB"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="technician">Technician</Label>
                    <Input 
                      value={formData.technician} 
                      onChange={(e) => setFormData({...formData, technician: e.target.value})}
                      placeholder="Technician name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="traffic">Traffic</Label>
                    <Input 
                      value={formData.traffic} 
                      onChange={(e) => setFormData({...formData, traffic: e.target.value})}
                      placeholder="Example: High Traffic - 80% utilization"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    value={formData.notes} 
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder="Additional notes"
                  />
                </div>

                {/* Photo Upload for OTDR Edit */}
                <PhotoUpload
                  label="OTDR Measurement Photos (Maximum 5 photos)"
                  maxPhotos={5}
                  photos={otdrPhotos}
                  onPhotosChange={setOtdrPhotos}
                />
              </>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => { setShowEditDialog(false); resetForm(); setEditingItem(null); }}>Cancel</Button>
              <Button onClick={handleUpdate}>Update</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Finding Detail Dialog */}
      <Dialog open={showFindingDetail} onOpenChange={setShowFindingDetail}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>Finding Detail - {selectedFinding?.location}</span>
            </DialogTitle>
          </DialogHeader>

          {selectedFinding && (
            <div className="space-y-6">
              {/* Photos Section */}
              {selectedFinding.photos && selectedFinding.photos.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Photos</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {selectedFinding.photos.map((photo: string, index: number) => (
                      <div key={index} className="relative group">
                        <img
                          src={photo}
                          alt={`Finding photo ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border cursor-pointer hover:opacity-80"
                          onClick={() => {
                            setSelectedPhotos(selectedFinding.photos);
                            setIsPhotoGalleryOpen(true);
                          }}
                        />
                        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                          {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                  <div className="space-y-2">
                    <div><span className="font-medium">ID:</span> {selectedFinding.id}</div>
                    <div><span className="font-medium">Type:</span> {selectedFinding.type}</div>
                    <div><span className="font-medium">Location:</span> {selectedFinding.location}</div>
                    <div><span className="font-medium">Severity:</span>
                      <Badge className={`ml-2 ${getSeverityColor(selectedFinding.severity)} text-white`}>
                        {selectedFinding.severity}
                      </Badge>
                    </div>
                    <div><span className="font-medium">Status:</span>
                      <Badge className={`ml-2 ${getStatusColor(selectedFinding.status)}`}>
                        {selectedFinding.status}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Reporting Details</h3>
                  <div className="space-y-2">
                    <div><span className="font-medium">Reported By:</span> {selectedFinding.reportedBy}</div>
                    <div><span className="font-medium">Reported Date:</span> {selectedFinding.reportedDate}</div>
                    <div><span className="font-medium">Route:</span> {selectedFinding.route}</div>
                    <div><span className="font-medium">Impact Level:</span> {selectedFinding.impactLevel || 'N/A'}</div>
                    {selectedFinding.findingLength && (
                      <div><span className="font-medium">Finding Length:</span> {selectedFinding.findingLength}</div>
                    )}
                    {selectedFinding.thirdPartyContact && (
                      <div><span className="font-medium">Third Party Contact:</span> {selectedFinding.thirdPartyContact}</div>
                    )}
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Description</h3>
                <p className="text-gray-700 dark:text-gray-300">{selectedFinding.description}</p>
              </div>

              {/* Remarks */}
              {selectedFinding.remark && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Remarks</h3>
                  <p className="text-gray-700 dark:text-gray-300">{selectedFinding.remark}</p>
                </div>
              )}

              {/* Affected Assets */}
              {selectedFinding.affectedAssets && selectedFinding.affectedAssets.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Affected Assets</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedFinding.affectedAssets.map((asset: string, index: number) => (
                      <Badge key={index} variant="outline">{asset}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={() => handlePrintFinding(selectedFinding)}>
                  <Printer className="h-4 w-4 mr-2" />
                  Print Report
                </Button>
                <Button variant="outline" onClick={() => {
                  setShowFindingDetail(false);
                  handleEdit(selectedFinding, 'finding');
                }}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Finding
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Asset Detail Dialog */}
      <Dialog open={showAssetDetail} onOpenChange={setShowAssetDetail}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Wrench className="h-5 w-5" />
              <span>Asset Detail - {selectedAsset?.name}</span>
            </DialogTitle>
          </DialogHeader>

          {selectedAsset && (
            <div className="space-y-6">
              {/* Photos Section */}
              {selectedAsset.photos && selectedAsset.photos.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Photos</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {selectedAsset.photos.map((photo: string, index: number) => (
                      <div key={index} className="relative group">
                        <img
                          src={photo}
                          alt={`Asset photo ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border cursor-pointer hover:opacity-80"
                          onClick={() => {
                            setSelectedPhotos(selectedAsset.photos);
                            setIsPhotoGalleryOpen(true);
                          }}
                        />
                        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                          {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                  <div className="space-y-2">
                    <div><span className="font-medium">ID:</span> {selectedAsset.id}</div>
                    <div><span className="font-medium">Name:</span> {selectedAsset.name}</div>
                    <div><span className="font-medium">Type:</span> {selectedAsset.type}</div>
                    <div><span className="font-medium">Location:</span> {selectedAsset.location}</div>
                    <div><span className="font-medium">Route:</span> {selectedAsset.route}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Status & Condition</h3>
                  <div className="space-y-2">
                    <div><span className="font-medium">Condition:</span>
                      <Badge className={`ml-2 ${getConditionColor(selectedAsset.condition)} text-white`}>
                        {selectedAsset.condition}
                      </Badge>
                    </div>
                    <div><span className="font-medium">Completeness:</span> {selectedAsset.completeness}%</div>
                    <div><span className="font-medium">Last Inspection:</span> {selectedAsset.lastInspection}</div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {selectedAsset.notes && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Notes</h3>
                  <p className="text-gray-700 dark:text-gray-300">{selectedAsset.notes}</p>
                </div>
              )}

              {/* Maintenance History */}
              {selectedAsset.maintenanceHistory && selectedAsset.maintenanceHistory.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Maintenance History</h3>
                  <div className="space-y-2">
                    {selectedAsset.maintenanceHistory.map((maintenance: any, index: number) => (
                      <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="text-sm">{typeof maintenance === 'string' ? maintenance : maintenance.description}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Changes */}
              {selectedAsset.changes && selectedAsset.changes.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Recent Changes</h3>
                  <div className="space-y-2">
                    {selectedAsset.changes.map((change: any, index: number) => (
                      <div key={index} className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div className="text-sm">{change.description}</div>
                        <div className="text-xs text-gray-500 mt-1">{change.date}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={() => handlePrintAsset(selectedAsset)}>
                  <Printer className="h-4 w-4 mr-2" />
                  Print Report
                </Button>
                <Button variant="outline" onClick={() => {
                  setShowAssetDetail(false);
                  handleEdit(selectedAsset, 'asset');
                }}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Asset
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Photo Gallery */}
      {isPhotoGalleryOpen && (
        <PhotoGallery
          photos={selectedPhotos}
          isOpen={isPhotoGalleryOpen}
          onClose={() => setIsPhotoGalleryOpen(false)}
        />
      )}

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Import Patrol Data</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="import-file">Select CSV File</Label>
              <input
                id="import-file"
                type="file"
                accept=".csv,.xls,.xlsx"
                onChange={handleFileSelect}
                className="mt-2 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>

            {importFile && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800">
                  <strong>Selected:</strong> {importFile.name}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  Size: {(importFile.size / 1024).toFixed(2)} KB
                </p>
              </div>
            )}

            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">Import Guidelines:</h4>
              <ul className="text-xs text-yellow-700 space-y-1">
                <li>• File must be in CSV format (exported from this system)</li>
                <li>• Data will be added to existing records</li>
                <li>• System will auto-detect data type from headers</li>
                <li>• Backup your data before importing</li>
              </ul>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => {
                setShowImportDialog(false);
                setImportFile(null);
              }}>
                Cancel
              </Button>
              <Button
                onClick={handleImportData}
                disabled={!importFile}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import Data
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Download className="h-5 w-5" />
              <span>Export Patrol Data</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="export-type">Data Type</Label>
              <Select value={exportType} onValueChange={(value: any) => setExportType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Data (Findings, Assets, OTDR)</SelectItem>
                  <SelectItem value="findings">Findings Only</SelectItem>
                  <SelectItem value="assets">Assets Only</SelectItem>
                  <SelectItem value="otdr">OTDR Measurements Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Export Information:</h4>
              <div className="text-xs text-blue-700 space-y-1">
                <p>• <strong>Findings:</strong> {findings.length} records</p>
                <p>• <strong>Assets:</strong> {assets.length} records</p>
                <p>• <strong>OTDR:</strong> {otdrMeasurements.length} records</p>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowExportDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleExportCSV} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                CSV
              </Button>
              <Button onClick={handleExportXLS} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                XLS
              </Button>
              <Button onClick={handleExportPDF}>
                <Download className="h-4 w-4 mr-2" />
                PDF
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PatrolPage;